import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import cardCompHoc from '@comp/card-comp-hoc';
import CardCompTpl from '@comp/card-comp-hoc/tpl';
import {
  MUView,
  MUImage
} from '@mu/zui';
import { EventTypes } from '@mu/madp-track';
import {
  sendTrackBeacon,
  beaconIds,
} from '@comp/comp-utils';
import ApplyButton from '@comp/button';
import { Url } from '@mu/madp-utils';
import Madp from '@mu/madp';
import succeedImg from './img/succeed.png';
import failedImg from './img/failed.png';

// TODO
// 手动埋点的beaconid 需要在@comp/comp-utils中配置该页面的beaconid
const BEACON_IDS = beaconIds.simpleResultPage;
// 埋点的pageid
const BEACON_PAGE_ID = BEACON_IDS.pageId();

const propTypes = {
  applyGlobalStore: PropTypes.shape({
    getGlobalApplyConfig: PropTypes.func.isRequired,
  }).isRequired
};

@cardCompHoc({
  title: '上传结果',
  pageId: BEACON_PAGE_ID,
  showProgress: false,
  showHelp: false,
  needStayDialog: false,
  needLoading: true,
  needFooter: false
}, () => {})
class SimpleResultComp extends Component {
  constructor(props) {
    super(props);
    this.state = this.dealResult();
  }

  componentDidMount() {
    Madp.eventCenter.trigger('CARD_INIT_DONE');
  }

  dealResult() {
    const submitResult = Url.getParam('submitResult') || 'faild';
    const cardType = Url.getParam('cardType') || 'faild';
    let title = '';
    let resultImg = '';
    let resultDesc = '';
    let buttonText = '';
    switch (submitResult) {
      case 'succeed':
        title = '上传成功';
        resultImg = succeedImg;
        resultDesc = '恭喜，您上传成功，快返回领取奖励吧！';
        buttonText = '返回领取奖励';
        sendTrackBeacon(BEACON_PAGE_ID, {
          event: EventTypes.PO,
          beaconId: BEACON_IDS.pageSucceed
        });
        break;
      case 'failed':
        title = '上传失败';
        resultImg = failedImg;
        resultDesc = '抱歉，您上传失败，请返回重新提交';
        buttonText = '返回重新提交';
        sendTrackBeacon(BEACON_PAGE_ID, {
          event: EventTypes.PO,
          beaconId: BEACON_IDS.pageFaild
        });
        break;
      default:
        break;
    }
    return {
      title,
      resultImg,
      resultDesc,
      buttonText,
      submitResult,
      cardType
    };
  }

  async submit() {
    const {
      submitResult
    } = this.state;
    const redirectUrl = Madp.getStorageSync('redirectUrl', 'SESSION') || '';
    if (submitResult === 'succeed' && redirectUrl) {
      // TODO 成功点击按钮
      Madp.reLaunch({
        url: redirectUrl,
      });
      return;
    }
    if (submitResult === 'failed') {
      // TODO 失败点击按钮
      return;
    }
    // 默认关闭webview
    Madp.closeWebview();
  }

  render() {
    const {
      title,
      resultImg,
      resultDesc,
      buttonText,
    } = this.state;
    const cardView = (
      <MUView className="simple-result">
        <MUView className="simple-result-img">
          <MUImage
            src={resultImg}
          />
        </MUView>
        <MUView className="simple-result-title">
          {title}
        </MUView>
        <MUView className="simple-result-desc">
          {resultDesc}
        </MUView>
        <MUView className="simple-result-button">
          <ApplyButton
            type="primary"
            beaconId={BEACON_IDS.submitBtn}
            onClick={this.submit.bind(this)}
          >
            {buttonText}
          </ApplyButton>
        </MUView>
      </MUView>
    );

    return process.env.TARO_ENV === 'h5' ? cardView : (
      <CardCompTpl
        {...this.props}
        {...this.state}
      >
        {cardView}
      </CardCompTpl>
    );
  }
}

SimpleResultComp.propTypes = propTypes;
export default SimpleResultComp;
export { BEACON_PAGE_ID };
