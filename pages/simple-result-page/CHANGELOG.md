# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## 1.0.1 (2023-04-03)


### Bug Fixes

* 删除多余渠道默认主题色 ([20cc9e0](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/20cc9e03a3a17840a758761b1ecad1ff8f0ad583))
* 删除多余主题颜色 ([3c686db](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3c686dbf8ca2fbe851d939970f46926e6c80c887))
* 删除生活号渠道默认主题 ([d8a6037](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d8a603750f64a1f9dc618cfdd0244bf477ee4224))
* 修复不自动倒计时 bug ([995260f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/995260fa2863ee267a084e614eeac843b99802a0))
* 修复步骤条样式 ([640cd3f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/640cd3f3fd0825b0e6848062f5233ea0a5630ac5))
* 修复轮播组件样式被覆盖 bug ([9a60fef](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/9a60fef11d1b4eb72267996cc9292955d7dfabd0))
* 修改步骤条颜色 ([f4bcc6a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f4bcc6a3ed57409fec8f834d8381dc65399fb99f))
* 修改步骤条样式 ([3a7496c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3a7496c986d55db293f0a0f74877c61c67528efc))
* 修改步骤条主题色 ([37b4b47](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/37b4b4739bb2bd17c028fbbba71fabf55d09cd04))
* 主题色适配 ([5cd7816](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5cd7816183c057001780b93e3ede60d9da3ed528))


### Features

* 调试波形图 ([f44d4bd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f44d4bdf6d0d0fb63baeb3e189070b199bdcd166))
* 调试账单选择页和银行卡列表页组件 ([c3f49c5](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c3f49c56f3434e024d9fcd88c711e53c512ec184))
* 调整 logout 函数 ([f91102e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f91102e003141ccfa4fea200dab6eb42a50383ba))
* 调整部分逻辑 ([7afabcf](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7afabcfcd2d861f681ee4418664be0dd3e33d10c))
* 调整录屏结果页流水号从url 中获取 ([ef4a9f7](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ef4a9f7da83fd4a1767a357b19b8922afe56ed4a))
* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd5efa629c0d35c96bf0b30d577d6c28f59))
* 调整网络监听函数 ([8c97264](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8c9726444b26af55b37600b22e939708d74c54e7))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb0f3cad22ee66e7ed189dec1a027fcba0c))
* 调整账单/录屏组件 ([ced43de](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ced43de5481679d5c6cc2ff2b358e7d5bb004e61))
* 函数抽取 ([3fdf675](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3fdf675c550a00a7b5e2422e1c7ab21842c87738))
* 录屏组件添加页面曝光能力 ([336d9c4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/336d9c4558952d233d849b38164d96865d8cb1f9))
* 区块和页面物料添加 data-template="zl" 方便后续代码搜索统计物料使用情况 ([c23aa19](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c23aa19f3d9e906813c270c5ec3304695e172386))
* 删除一些 console ([fb5f35e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fb5f35e038355c542248542fe410bd3184484481))
* 删除click-selector多余主题色 ([11807be](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/11807befd09134c1567baf650b52176bfb7fef2b))
* 生成物料信息文件 ([a01a54b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/a01a54be55df6b804c4ee5f54ad60cd6ca7ef949))
* 添加 im 消息返回 4 的类型 ([be47312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/be47312fa6c3fcd63e99a44a7e3c62e614be8b22))
* 添加录屏/账单组件 beaconId ([5595b5a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5595b5adb2f217d9b4ceb4dedcc5147a46addd5f))
* 添加录屏结果组件 ([8b06cfe](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b06cfee072a8f55b25706f23522b4a452b748df))
* 添加埋点信息 ([4e87be3](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/4e87be3125e7b46d6561b9fb6ae85a4a4c7d5c2e))
* 添加埋点信息 ([7715908](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/77159080cd1dcbb182adf09d3b02a0edc3c85d4f))
* 添加申请表单提交页面模板物料 ([e1f20b1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e1f20b1a6dac5264cc52a8fa1a9e0dce482d1498))
* 添加申请业务基础页面模板物料 ([8b1431f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b1431fd2faedc2567fe433bb3ab546dee01be88))
* 添加申请业务通用结果页面模板物料 ([df434f8](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/df434f89493612839fc3ba7c88b271ab04a372dd))
* 添加视频组件 ([b541022](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b5410225565282db18c47d3fc561ef122b006961))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e06726189f8bf1e8552089d7f6576733ac))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c15ae82b0120c637c2857eafbc4b31a168b))
* 添加网络监听事件 ([db5c5bb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/db5c5bbca64d5ff06f46c151ffa939feefc6f18b))
* 添加销毁实例函数 ([b206232](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b206232fdb08b6bf84c079e7d125278c85474694))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d60191dc9fa17b922f425caa3197851e312))
* 添加银行卡列表页组件 ([d4d50fc](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d4d50fc5d472ce5098d6c84b0dac8182253e4c8d))
* 添加账单选择页组件 ([7bd0c3c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7bd0c3c0538ac215e7d423e3ba6e9757ab06969a))
* 添加重试功能 ([881bb52](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/881bb5282e424e96f98ffdf88d2299e316f143c4))
* 图片上传组件新增页面组件 ([9b60113](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/9b6011392b81b087a0c079d965f4955d58b8508a))
* 完善 actionSheetPicker ([c725488](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c7254882ebaccc29c5efc1b945e859c4543758fe))
* 完善 click-selector ([5a74d35](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5a74d353d19d26c720460530c3302e5bd744633d))
* 完善 hospital-selector ([059b99e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/059b99e22a50de3dac61dd3d7cb6ae51a84fd8aa))
* 完善 readme ([e8da312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e8da312c262572b27343efd2aaafdcbbeee62ee4))
* 完善 result-loading ([645cc56](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/645cc560a1b701192e55aec8b35b02144977d6eb))
* 完善 school-selector ([50bc92c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/50bc92c6398bbca15b2d66337f35d192d20c932d))
* 完善 text-progress-bar ([cc2d42f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/cc2d42fa2f5fa10e151bd7c8fc11f27e6066ab6a))
* 完善 vague-search ([f48eb4d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f48eb4dfd6d4789e48b8c842ca0bf24f4beb298c))
* 完善 vague-search ([2af22e4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/2af22e423b91b095dca8ea5d95807fecc8185261))
* 完善区块物料 ([bc7deee](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bc7deee5403f08a4eb9625ba2cdb84b8388ff7c0))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb8a40ab04f8be125e30e6cacd189f59a62))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09ac4fa09e06eebe3943dfca19c8f12cf7a))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2fd9c34fbdb1ba08aa5c75d7180a2f39405))
* 修改 zego 推流 url ([0c40a62](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0c40a6260a766c0aab194896cb4115dfd255729a))
* 修改 zegoUrl ([7466c33](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7466c336eca962ac79b068a4608758f484ad56ba))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e621038a4ee304f9bab61ece66dab4d77b1495))
* 修改账单组件名称 ([bd5f336](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bd5f336fc41b67d7dcce28486d817ceccb80a37b))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a1621b9046977ec05c7b73e8077c1685ee))
* 增加按钮 ([f05a31b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f05a31b3da96e306b4f4e1807de23a3454734520))
* 增加编译 copy ([136108e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/136108eb3b16b5791003984d0497518fbfbb3d2b))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d444ad55a490369c3a035e23d07a1a1c97b))
* 增加超时返回和稍等 loading ([834e110](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/834e110b4cd8745ac8c8f7ddc7eb104bda9fa49f))
* 增加倒计时组件 ([3709e31](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3709e3117d3a4971a871d30d6d280cd0469593e5))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55ed90109e99816d77060a7e0f60843603b))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1b29f70f6c04d703ebebc6f4d158160143))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2e1e47f09ea693e94016e7bb4f1bbd5af8))
* 增加录屏页面 ([fd22965](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fd229657950d92c5f12c9c7b9b809a4afb46890d))
* 增加网络切换错误码 ([5468819](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/54688197af2eb1dd8656175489d99e73de410a16))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb81a6af4e2c1f631f945e0ad5285c57c2e))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e9246fa337adaf58d299f1fdf2b2ed9359346))
* init ([105e881](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/105e88191586ecdc56c49653a1cbd9d725ed3edf))
* init bill ([f1802f7](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f1802f765d123946fa98f70ba9686bb898fc4cc8))



# 1.0.0 (2022-08-09)


### Bug Fixes

* 修复不自动倒计时 bug ([995260f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/995260fa2863ee267a084e614eeac843b99802a0))


### Features

* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd5efa629c0d35c96bf0b30d577d6c28f59))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb0f3cad22ee66e7ed189dec1a027fcba0c))
* 添加申请表单提交页面模板物料 ([e1f20b1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e1f20b1a6dac5264cc52a8fa1a9e0dce482d1498))
* 添加申请业务基础页面模板物料 ([8b1431f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b1431fd2faedc2567fe433bb3ab546dee01be88))
* 添加申请业务通用结果页面模板物料 ([df434f8](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/df434f89493612839fc3ba7c88b271ab04a372dd))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e06726189f8bf1e8552089d7f6576733ac))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c15ae82b0120c637c2857eafbc4b31a168b))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d60191dc9fa17b922f425caa3197851e312))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb8a40ab04f8be125e30e6cacd189f59a62))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09ac4fa09e06eebe3943dfca19c8f12cf7a))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2fd9c34fbdb1ba08aa5c75d7180a2f39405))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e621038a4ee304f9bab61ece66dab4d77b1495))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a1621b9046977ec05c7b73e8077c1685ee))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d444ad55a490369c3a035e23d07a1a1c97b))
* 增加倒计时组件 ([3709e31](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3709e3117d3a4971a871d30d6d280cd0469593e5))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55ed90109e99816d77060a7e0f60843603b))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1b29f70f6c04d703ebebc6f4d158160143))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2e1e47f09ea693e94016e7bb4f1bbd5af8))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb81a6af4e2c1f631f945e0ad5285c57c2e))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e9246fa337adaf58d299f1fdf2b2ed9359346))
