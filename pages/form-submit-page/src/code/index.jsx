import { Component } from '@tarojs/taro';
import { track, EventTypes } from '@mu/madp-track';
import cardPageHoc from '@utils/card-page-hoc';
import FormSubmitComp, { BEACON_PAGE_ID } from '@comp/demo-comp';
// 自定义组件样式要在page引入才生效，注意先后顺序
import '@comp/card-comp-hoc/index.scss';
import '@comp/demo-comp/index.scss';

@track({
  event: EventTypes.PO,
}, {
  pageId: BEACON_PAGE_ID
})
@cardPageHoc()
class FormSubmitPage extends Component {
  render() {
    return (
      <FormSubmitComp
        {...this.props}
        data-template="zl"
      />
    );
  }
}

export default FormSubmitPage;
// TODO
/**
 * 申请工程中一般将组件统一存放至src/components目录下
 * 这里页面模板由于代码下载的限制 demo-comp组件 放在页面的目录下
 * 实际开发中请按照申请统一规范 将组件移动至src/components目录
 */
