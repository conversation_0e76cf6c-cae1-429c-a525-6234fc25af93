import {
  observable, action, computed, configure, flow
} from 'mobx';
import Madp from '@mu/madp';
import { EventTypes } from '@mu/madp-track';
import {
  beaconIds, sendTrackBeacon, getUserInfo, queryContractConfig, contractCodeMap
} from '@comp/comp-utils';
// 严格模式下，不允许在 action 外更改任何状态
configure({
  enforceActions: 'observed'
});

// TODO
// 手动埋点的beaconid 需要在@comp/comp-utils中配置该页面的beaconid
const BEACON_IDS = beaconIds.formSubmitPage;
// 埋点的pageid
let BEACON_PAGE_ID = BEACON_IDS.pageId();
class FormSubmitStore {
  // eslint-disable-next-line no-unused-vars
  constructor(props) {
    // 再次取pageId更新，兼容小程序初始预加载拿不到url参数的问题
    BEACON_PAGE_ID = BEACON_IDS.pageId();
  }

  // 本地维护的表单结构
  @observable form = {
    custName: '',
    certId: ''
  };

  // 合同框勾选状态
  @observable contractChecked = false;

  // 是否检测用户行为
  @observable isneedDetectUserBhvr = false;

  // 用户基本信息
  @observable userInfo = {
    maskRealName: '',
    maskIdNo: '',
    maskMobile: '',
    identified: false
  };

  // 机构编号
  @observable orgId = '';

  // 机构名称
  @observable orgName = '';

  // 业务合作信息授权书（第三方对招联导流版）
  // 业务合作信息授权书（联合贷、0:100版）
  // 需要传入联系方式（实际为客服电话，若无展示空）
  @observable custservicePhoneNo = '';

  // 联合贷协议需要传入商户号
  @observable merchantId = '';

  // 贷款告知书要展示的商户名
  @observable merchantName = '';

  // 公司邮箱
  @observable companyEmail = '';

  // 是否强制阅读协议
  @observable forceReadContract = true;

  // 协议组合
  @observable contractMap = contractCodeMap.XYDC;

  // 是否显示协议
  @observable isShowContract = true;

  // 回显是否编辑
  @observable isIdentityReadOnly = true;

  // 是否显示证件扫描
  @observable isHideIdentityEntry = true;

  // 协议合同配置
  @observable channelContractConfigList = [];

  /**
   * 初始化页面
   */
  @action.bound
  initComp = flow(function* initComp() {
    // 初始化表单
    const res = yield queryContractConfig();
    this.channelContractConfigList = res;
    Madp.eventCenter.trigger('CARD_INIT_DONE');
  });

  // 表格数据更新
  @action.bound
  updateObjForm(key, value) {
    this.form[key] = value;
  }

  @action.bound
  initUserInfo = flow(function* initUserInfo() {
    const res = yield getUserInfo();
    if (res.loginSuccess) {
      this.form.custName = res.maskRealName;
      this.form.certId = res.maskIdNo;
      this.isIdentityReadOnly = false;
    }
  })

  /**
   * 点击合同勾选框
   */
  @action.bound
  checkboxClick(val) {
    this.contractChecked = val;
    if (val) {
      // 协议勾选时埋点
      sendTrackBeacon(BEACON_PAGE_ID, {
        event: EventTypes.EV,
        beaconId: beaconIds.contractChecked
      });
    }
  }

  /**
   * 提交按钮是否置灰
   */
  @computed get disabledBtn() {
    return this.contractChecked && this.form.certId && this.form.custName;
  }

  // 检验表单
  @action.bound
  checkForm() {
    if (!this.contractChecked) {
      Madp.showToast({ title: '协议未勾选', icon: 'none' });
      return false;
    }
    return true;
  }

  /**
   * 提交表单
   */
  @action.bound
  submit = flow(function* submit() {
    // 点击下一步埋点（按照协议组合不同区分 这里以XYDC示例）
    sendTrackBeacon(BEACON_PAGE_ID, {
      event: EventTypes.EV,
      beaconId: 'submitWith XYDC'
    });
    // 点击下一步埋点（按是否强制阅读区分 @邹苗苗）
    sendTrackBeacon(BEACON_PAGE_ID, {
      event: EventTypes.EV,
      beaconId: this.forceReadContract
        ? beaconIds.forceReadSubmit
        : beaconIds.normalSubmit
    });

    if (this.checkForm()) {
      // 表单校验通过
      // TODO
      // 提交表单资料
      yield console.log('提交信息');
    }
  });
}
// 因为小程序重新获取BEACON_PAGE_ID，所以前面用let不能直接export
FormSubmitStore.BEACON_IDS = BEACON_IDS;
FormSubmitStore.BEACON_PAGE_ID = BEACON_PAGE_ID;

export default FormSubmitStore;
