/* eslint-disable max-len */
import Madp from '@mu/madp';
import {
  <PERSON>U<PERSON><PERSON><PERSON>,
  <PERSON>UF<PERSON>,
  MUInput,
  MUModal,
  MUButton
} from '@mu/zui';
import { isMuapp } from '@mu/madp-utils';
import PropTypes from 'prop-types';
import cardCompHoc from '@comp/card-comp-hoc';
import { AgreementDrawer } from '@mu/agreement';
import {
  contractCodeMap,
  onContractClick,
  beaconIds
} from '@comp/comp-utils';
import {
  getMapCode
} from '@utils/constants';
import ContractChecker from '@comp/contract-checker';
import FormSubmitStore from './store';

const { BEACON_IDS, BEACON_PAGE_ID } = FormSubmitStore;
const propTypes = {
  applyGlobalStore: PropTypes.shape({
    getGlobalApplyConfig: PropTypes.func.isRequired,
    setGlobalData: PropTypes.func.isRequired,
    getGlobalData: PropTypes.func.isRequired
  }).isRequired,
  formSubmitStore: PropTypes.shape({
    form: PropTypes.object.isRequired,
    contractChecked: PropTypes.bool.isRequired,
    isneedDetectUserBhvr: PropTypes.bool.isRequired,
    userInfo: PropTypes.object.isRequired,
    orgId: PropTypes.string.isRequired,
    custservicePhoneNo: PropTypes.string.isRequired,
    merchantId: PropTypes.string.isRequired,
    merchantName: PropTypes.string.isRequired,
    companyEmail: PropTypes.string.isRequired,
    forceReadContract: PropTypes.bool.isRequired,
    contractMap: PropTypes.object.isRequired,
    isShowContract: PropTypes.bool.isRequired,
    isIdentityReadOnly: PropTypes.bool.isRequired,
    isHideIdentityEntry: PropTypes.bool.isRequired,
    disabledBtn: PropTypes.bool.isRequired,
    channelContractConfigList: PropTypes.array.isRequired,
    updateObjForm: PropTypes.func.isRequired,
    initComp: PropTypes.func.isRequired,
    initUserInfo: PropTypes.func.isRequired,
    checkboxClick: PropTypes.func.isRequired,
    checkForm: PropTypes.func.isRequired,
    submit: PropTypes.func.isRequired
  }).isRequired
};

@cardCompHoc(
  {
    needCardWrap: true,
    title: '申请表单提交页面',
    background: '#FFF',
    pageId: BEACON_PAGE_ID,
    getPageId: BEACON_IDS.pageId,
    showChatEntry: false,
    footerText: '',
    showProgress: false
  },
  (props) => ({
    formSubmitStore: new FormSubmitStore(props)
  })
)
class FormSubmitComp extends Madp.Component {
  constructor(props) {
    super(props);
    this.state = {
      toast: {
        isOpened: false,
        text: '提示'
      },
      hideContractFlag: true,
      infoIconIsOpen: false,
      showForceReadContractsModal: false, // 是否显示强制阅读弹窗
      forceReadContractChecked: false, // 是否已阅读并同意强制阅读协议
    };
    this.readDuration = 0; // 强制阅读协议时长
    this.forceReadContractParams = []; // 强制阅读协议配置
    this.checkIdentified = this.checkIdentified.bind(this);
    this.onScanClick = this.onScanClick.bind(this);
    this.infoIconModalConfirm = this.infoIconModalConfirm.bind(this);
  }

  async componentDidMount() {
    const {
      formSubmitStore: { initUserInfo, initComp }
    } = this.props;
    await initComp();
    this.getForceReadContractParams();
    initUserInfo();
  }

  // 获取强制阅读协议合同模版参数
  getForceReadContractParams() {
    const {
      formSubmitStore: {
        channelContractConfigList,
        contractMap: {
          contracts
        }
      },
    } = this.props;
    const channelContractConfig = channelContractConfigList.slice(0, channelContractConfigList.length);
    contracts.forEach((item) => {
      this.getContractParams(item.contractType, item.text, channelContractConfig);
    });
  }

  onInfoIconClick = () => {
    this.setState({ infoIconIsOpen: true });
  };

  infoIconModalConfirm = () => {
    this.setState({ infoIconIsOpen: false });
  };

  onScanClick = async () => {
    if (this.checkIdentified()) {
      const {
        formSubmitStore: { updateObjForm }
      } = this.props;
      const result = await Madp.frontIdcardScan();
      if (result) {
        updateObjForm('certId', result.idNo);
        updateObjForm('custName', result.custName);
      }
    }
  };

  /*
   * 获取合同模板参数
   */
  getContractParams = (id, text, channelContractConfig) => {
    const {
      formSubmitStore: {
        form,
        userInfo,
        companyEmail,
        contractGroupId,
        merchantName,
        merchantId,
        orgId,
        orgName,
        custservicePhoneNo
      }
    } = this.props;
    let params = {
      name: form.custName || (userInfo && userInfo.maskRealName) || '',
      certId: form.certId || (userInfo && userInfo.maskIdNo) || '',
      contractType: id,
    };
    const date = new Date();

    switch (id) {
      // 个人征信及资信信息授权
      // 敏感信息处理授权
      case 'CREDITOWN':
      case 'SENSITIVEAUTH':
      case 'CREDIT':
        params = {
          ...params,
          yearNow: date.getFullYear(),
          monthNow: date.getMonth() + 1,
          dayNow: date.getDate()
        };
        break;
      case 'LIMIT':
        params = {
          ...params,
          contractDate: `${date.getFullYear()}-${date.getMonth()
            + 1}-${date.getDate()}`,
          mobile: (userInfo && userInfo.maskMobile) || '',
          notarizeType:
            (contractCodeMap[contractGroupId]
              && contractCodeMap[contractGroupId].notarizeType)
            || '1'
        };
        break;
      case 'FQAPPLY':
        params = {
          ...params,
          yearNow: date.getFullYear(),
          monthNow: date.getMonth() + 1,
          dayNow: date.getDate(),
          mobile: (userInfo && userInfo.maskMobile) || '',
          email: companyEmail || '/'
        };
        break;
      case 'CREDITCOMPRE':
        params = {
          ...params,
          yearNow: date.getFullYear(),
          monthNow: date.getMonth() + 1,
          dayNow: date.getDate(),
          partner: merchantName,
          partnerId: merchantId
          // ...channelConfigProps.LHDContractDifferentParams,
        };
        break;
      case 'BUSICOOPAUTHIN':
        params = {
          ...params,
          yearNow: date.getFullYear(),
          monthNow: date.getMonth() + 1,
          dayNow: date.getDate(),
          partner: merchantName || '/',
          partnerId: merchantId || '/',
          partnerContact: custservicePhoneNo || '/',
          orgId: orgId || '/',
          orgName: orgName || merchantName || '/'
        };
        break;
      case 'BUSICOOPAUTHUN':
        params = {
          ...params,
          yearNow: date.getFullYear(),
          monthNow: date.getMonth() + 1,
          dayNow: date.getDate(),
          partner: merchantName || '/',
          partnerId: merchantId || '/',
          orgId: orgId || '/',
          orgName: orgName || merchantName || '/',
          partnerContact: custservicePhoneNo || '/',
          // 联合贷目前只有浦发一个，所以第二个合作方默认传 /
          twoPartner: '/',
          twoPartnerId: '/',
          twoPartnerContact: '/',
          twoOrgId: '/',
          twoOrgName: '/',
          insureOrgName: '/',
          insureOrgContact: '/',
          twoInsureOrgName: '/',
          twoInsureOrgContact: '/'
        };
        break;
      default:
        break;
    }
    if (channelContractConfig && channelContractConfig.length > 0) {
      channelContractConfig.forEach((ContractConfigItem) => {
        // 调试的时候如果合同数据都不是强制阅读 可以把这里条件改成'N' 查看协议预览效果
        if (id === ContractConfigItem.contractType && ContractConfigItem.forceReadFlag === 'Y') { // 如果强制阅读字段是Y则表示需要强制阅读
          this.readDuration = ContractConfigItem.readDuration; // 保存强制阅读时间
          this.forceReadContractParams.push({
            title: text,
            params
          });
        }
      });
    }
    return params;
  };

  // 点击输入框 有获取用户信息时 不可更改
  checkIdentified() {
    const {
      formSubmitStore: { isIdentityReadOnly }
    } = this.props;
    if (!isIdentityReadOnly) {
      this.setState({
        toast: {
          isOpened: true,
          text: '你的姓名及身份证号不可修改，如有疑问请联系在线客服！'
        }
      });
      return false;
    }
    return true;
  }

  // 点击协议勾选设置拉起弹窗
  handleCheckboxClick(val) {
    const {
      formSubmitStore: {
        checkboxClick,
      },
    } = this.props;
    const {
      forceReadContractChecked,
    } = this.state;
    // 没有强制阅读协议时， 点击直接勾选，预览不需要倒计时
    // 如果已经点击了阅读 不会再弹预览
    // 如果是取消勾选 也不需要校验
    if (this.forceReadContractParams.length === 0 || forceReadContractChecked || !val) {
      checkboxClick(val);
    }

    // 如果有强制阅读协议，且没有在弹窗同意，且没有在协议预览同意 则显示强制阅读
    if (this.forceReadContractParams.length > 0 && !forceReadContractChecked) {
      this.setState({ showForceReadContractsModal: true });
    }
  }

  // 点击协议弹窗同意阅读协议
  onCheckboxClickPopModalSubmit() {
    const {
      formSubmitStore: {
        checkboxClick,
      },
    } = this.props;
    this.setState({ forceReadContractChecked: true });
    checkboxClick(true);
  }

  render() {
    const { toast, infoIconIsOpen, showForceReadContractsModal } = this.state;
    const {
      formSubmitStore: {
        isIdentityReadOnly,
        updateObjForm,
        isHideIdentityEntry,
        forceReadContract,
        disabledBtn,
        submit,
        form,
        isneedDetectUserBhvr,
        isShowContract,
        contractMap,
        contractChecked
      }
    } = this.props;

    return (
      <MUView className="form-submit">
        <MUForm
          titleType="list"
          title="身份信息"
          onTitleIconClicked={this.onInfoIconClick}
        >
          <MUInput
            name="custName"
            title="姓名"
            type="text"
            placeholder="请输入你的真实姓名"
            needDetectUserBhvr={isneedDetectUserBhvr}
            clear
            enableBlurTrim
            maxLength={50}
            beaconId={beaconIds.applyPersonalInfo.custName}
            editable={isIdentityReadOnly}
            value={form.custName}
            onClick={this.checkIdentified}
            onChange={(val) => {
              updateObjForm('custName', val);
            }}
          />
          <MUInput
            name="certId"
            title="身份证号"
            type="idcard"
            placeholder="同意并输入你的身份证号"
            className="identity-certId"
            needDetectUserBhvr={isneedDetectUserBhvr}
            clear
            maxLength={18}
            enableBlurTrim
            beaconId={beaconIds.applyPersonalInfo.certId}
            editable={isIdentityReadOnly}
            value={form.certId}
            onClick={this.checkIdentified}
            onChange={(val) => {
              updateObjForm('certId', val);
            }}
            postFixIcon={isMuapp() && isHideIdentityEntry ? 'scan2' : ''}
            onPostFixIconClick={this.onScanClick}
            needCloseKbOnPostFixIconClick
          />
        </MUForm>

        {isShowContract ? (
          <ContractChecker
            outerControl
            contractText={contractMap.contractText}
            contracts={contractMap.contracts}
            beaconId={beaconIds.contractBox}
            onContractClick={(item) => {
              onContractClick(getMapCode(), item.contractType,
                this.getContractParams(item.contractType), BEACON_PAGE_ID);
            }}
            checkedValue={contractChecked}
            handleCheckboxClick={this.handleCheckboxClick.bind(this)}
            forceReadContractChecked={forceReadContract}
          />
        ) : null}
        {/* {协议预览弹窗} */}
        <AgreementDrawer
          agreementViewProps={{
            type: 1,
            list: this.forceReadContractParams,
            current: 0,
          }}
          show={showForceReadContractsModal}
          submit={this.onCheckboxClickPopModalSubmit.bind(this)}
          close={() => this.setState({ showForceReadContractsModal: false })}
          totalCount={this.readDuration}
        />
        <MUView className="identity__button-block">
          <MUButton
            type="primary"
            beaconId={beaconIds.submitBtn}
            disabled={!disabledBtn}
            onClick={submit.bind(this)}
          >
            下一步
          </MUButton>
        </MUView>
        {/* 提示 */}
        <MUModal
          isOpened={toast.isOpened}
          content={toast.text}
          confirmText="知道了"
          onConfirm={() => this.setState({ toast: { isOpened: false } })}
        />

        {/* 身份证号说明弹框 */}
        {infoIconIsOpen && (
          <MUModal
            type="text"
            title="身份证号说明"
            beaconId={beaconIds.cerditInfoIntroModal}
            isOpened={infoIconIsOpen}
            closeOnClickOverlay={false}
            content="在以下场景中，招联消费金融有限公司需要收集、使用你的身份证号码：<br>（1）根据相关监管规定，在你申请授信、借款或绑定银行卡时用于核实你的身份；<br>（2）为满足你办理业务、接受服务的需求，招联可能会向第三方传输你的身份证号码，以委托第三方开展相关工作，包括身份及其他信息验证、征信及其他信息查询、数据分析、数据测试、客户服务、贷款催收、市场营销、信息推送、代收及代付服务、会计服务、审计服务、评级服务、法律服务、公证服务等。<br>（3）为满足你向招联合作方购买商品或接受服务的需求，招联可能会向合作方提供你的身份证号码，以用于合作方核实你的身份，招联在向合作方提供前将另行获得你的同意。<br>身份证号码是你的重要个人信息，一旦泄露或处理不当可能危害你的人身、财产安全，招联将根据法律法规要求并参照行业最佳实践为你的个人信息安全提供保障。"
            confirmText="我知道了"
            onConfirm={this.infoIconModalConfirm}
            onClose={this.infoIconModalConfirm}
          />
        )}
      </MUView>
    );
  }
}

FormSubmitComp.propTypes = propTypes;
export default FormSubmitComp;
export { BEACON_PAGE_ID };
