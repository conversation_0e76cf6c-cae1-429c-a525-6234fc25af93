{"name": "result-template", "version": "1.0.0", "description": "申请结果页DDD前端模板", "license": "MIT", "author": "mucfc", "scripts": {"build:alipay": "madp build --type alipay", "build:h5": "madp build --type h5", "build:h5-offline": "cross-env BT=offline npm run build:h5", "build:h5-report": "madp build --type h5 report", "build:qq": "madp build --type qq", "build:quickapp": "madp build --type quickapp", "build:rn": "madp build --type rn", "build:swan": "madp build --type swan", "build:tt": "madp build --type tt", "build:weapp": "madp build --type weapp", "dev:alipay": "npm run build:alipay -- --watch", "dev:h5": "madp build --type h5 -- --watch debug", "dev:h5-offline": "cross-env BT=offline npm run dev:h5", "dev:h5-st1": "cross-env ENV=st1 npm run dev:h5", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:weapp": "npm run build:weapp -- --watch", "lint": "eslint --ext .ts,.tsx src --fix", "stylelint": "stylelint  \"**/*.scss\" --fix", "prd:server": "madp server"}, "lint-staged": {"src/**/*.{ts,tsx}": "npm run lint", "src/**/*.scss": "npm run stylelint"}, "dependencies": {"@mu/basic-library": "1.24.0-beta.20", "@mu/local-config": "1.6.2-beta.5", "@mu/zui": "1.24.3-beta.37", "@mu/click-selector": "1.2.1-beta.15", "@mu/vague-search": "1.0.1-beta.11", "@mu/agreement": "1.5.23-nostyle.1", "@mu/lui": "2.22.37-beta.1", "@mu/ldf-flow-card": "1.0.0-alpha.81", "@mu/coupon-selector-utils": "1.0.0-beta.13", "@mu/survey": "2.2.6-beta.5"}, "devDependencies": {"@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "8.3.4", "@mu/madp-cli": "1.20.0-cli.1", "@types/react": "16.9.26", "@types/webpack-env": "1.15.1", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "babel-eslint": "8.2.6", "babel-plugin-syntax-dynamic-import": "6.18.0", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "1.7.0", "commitlint": "8.3.5", "conventional-changelog-cli": "2.0.31", "cross-env": "5.2.1", "cz-conventional-changelog": "2.1.0", "eslint": "7.32.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "2.5.1", "eslint-plugin-taro": "3.3.20", "husky": "9.1.7", "image-webpack-loader": "6.0.0", "lint-staged": "9.5.0", "lodash-es": "4.17.15", "postcss": "8.4.49", "postcss-scss": "4.0.9", "preload-webpack-plugin": "3.0.0-beta.4", "standard-version": "6.0.1", "stylelint": "14.16.1", "stylelint-config-recommended-scss": "8.0.0", "stylelint-config-standard": "29.0.0", "stylelint-scss": "4.7.0", "typescript": "4.0.2", "vconsole-webpack-plugin": "1.5.1", "webpack-bundle-analyzer": "3.6.1", "webpack-merge": "4.2.2"}, "repo": ""}