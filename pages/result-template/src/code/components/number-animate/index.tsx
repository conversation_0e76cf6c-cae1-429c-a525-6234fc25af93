import { Component } from '@tarojs/taro';
import { MUText } from '@mu/zui';
import NumberAnimate from './NumberAnimate';
import './index.scss';

class NumberAnimateCom extends Component {
  constructor(props) {
    super(props);
    this.state = {
      num: '',
      // num1Complete: '',
    };
    let n1 = new NumberAnimate({
      ...this.props,
      onUpdate: () => {
        this.setState({
          num: n1.tempValue
        });
      },
      // onComplete: () => {
      //   this.setState({
      //     num1Complete: '完成了'
      //   });
      // }
    });
  }
  componentDidMount() {

  }
  render() {
    const { num } = this.state;
    return (
      <MUText className="credit">
        {num}
      </MUText>
    );
  }
}
export default NumberAnimateCom;
