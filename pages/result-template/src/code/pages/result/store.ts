// 申请结果页store（控制层） 
import { observable, action } from 'mobx';
import Madp from '@mu/madp';
import { Url, isAlipay } from '@mu/madp-utils';
import { injectState } from '@mu/leda';
import { ApplyService } from '../../domain/services/apply-service';
import { getStandardResultCardInstance } from '../../domain/models/apply/standard-result-card';
import { getAccountInstance } from '../../domain/models/apply/account';
import { getApplyInstance } from '../../domain/models/apply/apply';
import { textPool } from '../../utils/text-pool';
import { ResultCardService } from '../../domain/services/result-card-service';
import { getParamFromHash } from '../../utils';
import { legaoPageIdMap } from '../../utils/constants';

const resultCode = Url.getParam('resultCode'); // 结果码，一般被拒才有
const cardCode = Url.getParam('cardCode') || '';
const redirectUrl = Url.getParam('redirectUrl') || Madp.getStorageSync('redirectUrl', 'SESSION') || '';
const applyResultMap = textPool.defaultApplyResultMapNew;

class ResultStore {
  /**
   * 标准结果卡片领域模型实例
   */
  @observable standardResultCard = getStandardResultCardInstance();

  /**
   * 账户领域模型实例
   */
  @observable account = getAccountInstance();

  /**
   * 申请领域模型实例
   */
  @observable apply = getApplyInstance();

  /**
   * 结果内容
   */
  @observable resultContent = {} as any;

  /**
   * 按钮气泡文案
   */
  @observable btnBubbleText = '';

  /**
   * 结果类型
   */
  @observable resultType = Url.getParam('type') || '';

  /**
   * 是否展示非标导流组件
   */
  @observable showLDS = false;

  /**
   * 倒计时数字
   */
  @observable countingNum = '';

  /**
   * 页面初始化
   */
  @action.bound
  async initComp() {
    try {
      // 调用领域服务层查询卡片信息
      await ApplyService.getCardInfo({
        cardCode: 'DEFAULT',
        mainApplyNo: '',
        componentId: ''
      });
    } catch (error) {
      console.error('初始化申请结果页失败:', error);
      throw error;
    }
  }

  @action.bound
  async queryLimit() {
    const applyNo = Madp.getStorageSync('resultPageMainApplyNo', 'SESSION');
    try {
      await ApplyService.getApplyInfo({
        queryScene: '5', // 只查询案件关联的额度类型, 额度值，是否主额度，额度状态
        applyNo,
      });
    } catch (error) {
      console.error('申请接口调用失败:', error);
      throw error;
    }
  }

  @action.bound
  async initResultCard() {
    const resultPageShownFlag = Madp.getStorageSync('resultPageShownFlag', 'SESSION');
    Madp.setStorageSync('resultPageShownFlag', '', 'SESSION');
    // URL上带了结果直接展示，激活、各种管控被拒
    if (this.resultType) {
      await this.initResultDefault();
      return;
    }
    const promises = [
      resultStore.initComp(),
      resultStore.queryLimit()
    ];
    await Promise.all(promises);
    const { limit, hasCredit, hasMainCreditToActivate } = resultStore.account || {};
    const { applyStep, rejectCode, curChannelSupport } = resultStore.apply || {};
    // 已出额度，直接展示 applyStep 11 表示激活审批中，需要轮询结果
    if (hasCredit && applyStep !== '11') {
      await this.initResultWithCredit(limit, applyStep, hasMainCreditToActivate, curChannelSupport);
    } else { // 未出额度 applyStep 11 表示激活审批中，需要轮询结果
      const checkCountDown = !resultPageShownFlag && (applyStep === '3' || applyStep === '11'); // 审批中则判断倒计时
      await this.initResultWithNoCredit(checkCountDown, applyStep, rejectCode);
    }
  }

  @action.bound
  async initResultDefault() {
    
  }

  @action.bound
  async initResultWithCredit(totalCredit: number, applyStep: string, hasMainCreditToActivate: boolean, curChannelSupport: string) {
    this.resultType = 'stdSuccess';
    if (applyStep === '8' && hasMainCreditToActivate && curChannelSupport) {
      this.resultType = 'stdActivate';
    }
    if (applyStep === '8' && hasMainCreditToActivate && !curChannelSupport) {
      this.resultType = 'notSupportActivate';
    }

    // 获取展示内容
    const resultContent = await this.getResultContentWithCredit(totalCredit);
    // 获取按钮气泡
    // const btnBubbleText = resultContent.needBtnBubble ? await this.getBtnBubbleText() : '';
    this.set('resultContent', resultContent);
  }

  @action.bound
  async getResultContentWithCredit(totalCredit: number) {
    const credit = parseInt(String(totalCredit), 10).toFixed(2);
    const resultContent = applyResultMap[this.resultType][cardCode] || applyResultMap[this.resultType].default;
    let { buttonList, needBtnBubble, autoJumpUrl } = resultContent;
    const { limitType, hasMainCreditToActivate } = this.account || {};
    const isXFCredit = limitType === 'E14';
    const { needJumpToDiversionPage, isPassFocusFQH } = this.standardResultCard || {};

    // 有自动跳转url，直接返回
    if (autoJumpUrl) {
      return resultContent;
    }

    // 有入口返回url，则更新按钮仅返回
    if (needJumpToDiversionPage && redirectUrl) {
      const backButtonText = getParamFromHash(redirectUrl, 'backButtonText');
      buttonList = [{
        text: backButtonText || '返回',
        type: 'close'
      }];
      needBtnBubble = false;
      //   1: 不支持分期花的话和不支持引导分期花，去掉分期花按钮
    } else if (resultContent.buttonList.length && (!await ApplyService.checkSupportFQH() || !isPassFocusFQH)) {
      // 其他渠道投放到支付宝环境，由于支付宝环境，不容许导流到其他app，因此这里特殊判断  ----新系统激活不需要引导 待删除
      if (isAlipay() && hasMainCreditToActivate && resultContent.supportChangeInfo) {
        resultContent.creditDesc = resultContent.creditDescAli;
        buttonList = resultContent.buttonList.filter((button: any) => button.type === 'activate');
      } else if (isAlipay() && resultContent.supportChangeInfo) {
        buttonList = resultContent.buttonList.filter((button: any) => button.type === 'loan');
      } else if (resultContent.supportChangeInfo) {
        buttonList = resultContent.buttonList.filter((button: any) => button.type === 'download');
      } else {
        buttonList = resultContent.buttonList.filter((button: any) => button.type !== 'fqh');
      }
    }

    // 审批通过，抖音渠道，按钮文案替换
    if (this.standardResultCard.isGuideDownloan) {
      buttonList = [{
        text: resultContent.guideDownloan,
        type: 'download'
      }];
    }

    // 如果主额度是消费额度，按钮文案变成返回
    if (isXFCredit) {
      buttonList = [{
        text: '返回',
        type: 'close'
      }];
    }
    return {
      ...resultContent,
      credit,
      buttonList,
      needBtnBubble,
    };
  }

  @action.bound
  async initResultWithNoCredit(checkCountDown: boolean, applyStep: string, rejectCode: string) {
    const { loadingTime } = this.standardResultCard || {};
    if (checkCountDown && loadingTime) {
      this.countingNum = String(loadingTime);
      this.resultType = 'stdPolling';
      injectState({
        pageId: legaoPageIdMap[this.resultType],
        stateKeys: [
          'virtualIdCommon',
        ],
      });
      return;
    }
    // 重审状态
    if (applyStep === '21') {
      this.resultType = 'cross';
      await ApplyService.submitApplyInfo({
        cardCode,
      });
      // await goNext();
      return;
    } else if (applyStep === '6' || applyStep === '7') { // 被拒管控期外、内
      this.resultType = 'stdFail';
      this.showLDS = await ResultCardService.getLdsTag();
    } else { // 审批中等其他状态，统一展示审批中
      this.resultType = 'stdFail';
    }

    let resultContent = {} as any;
    resultContent = await this.getResultContent(applyResultMap[this.resultType][cardCode]
      || applyResultMap[this.resultType].default);

    const resultContentNew = Object.assign({} as any, resultContent);
    if (this.standardResultCard.isGuideDownloan) {
      resultContentNew.buttonList = [{
        text: resultContent.guideDownloan,
        type: 'download',
        backupButton: {}
      }];
    }

    this.set('resultContent', resultContentNew);
    injectState({
      pageId: legaoPageIdMap[this.resultType],
      stateKeys: [
        'standardDetainDialog',
        'marqueeTips',
        'bannerWithoutBorder',
        'virtualIdCommon',
        'commonBtn',
        'multiFuncRange',
        'imageTitle'
      ],
    });
  }

  @action.bound
  async getResultContent(content: any, availLimit?: string) {
    let info = Url.getParam('info');
    let desc = Url.getParam('desc');
    let contentNew = content;

    if (resultCode && contentNew.resultCodeText && contentNew.resultCodeText[resultCode]) {
      contentNew = {
        ...contentNew,
        ...contentNew.resultCodeText[resultCode]
      };
      if (resultCode === 'UMDP03409') {
        desc = desc && availLimit && desc.replace(/amount/g, availLimit) || '';
      }
    }

    if (!content.buttonList || !content.buttonList.length) {
      return contentNew;
    }
    return {
      ...contentNew,
      info: info || contentNew.info,
      desc: desc || contentNew.desc
    };
  }

  @action.bound
  set(key: string, value: any) {
    this[key] = value;
  }
}

// 导出单例实例
const resultStore = new ResultStore();
export default resultStore; 