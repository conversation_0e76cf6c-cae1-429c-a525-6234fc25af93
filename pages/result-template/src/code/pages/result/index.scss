@import "~@mu/zui/dist/style/variables/default.scss";
@import "~@mu/zui/dist/style/mixins/index.scss";

.resultCardView {

  .resultView-main,
  .resultView-incrementEntry {
    background: #fff;
  }

  .resultView {
    display: flex;
    flex-direction: column;

    // 内容区块
    &-content {
      flex: 1;
      position: relative;

      .multiFuncRange {
        margin-top: 20px;
      }
    }

    &-questionnaire-container {
      margin: 20px;
    }

    &-incrementEntry {
      display: flex;
      flex-direction: row-reverse;
      font-size: $font-size-base;
      color: $color-blue;
      text-align: right;
      padding: 0 $spacing-h-xl;
      padding-top: $spacing-v-sm;
      margin-bottom: -40px;

      .entry {
        width: fit-content;
      }
    }

    &-main {
      padding-top: 100px;
      padding-bottom: 20px;
      width: 100vw;

      &_content {
        display: flex;
        padding: 0 $spacing-h-xl;
        text-align: center;
        flex-direction: column;

        &_icon {
          /* stylelint-disable-next-line unit-case */
          width: 80PX;
          /* stylelint-disable-next-line unit-case */
          height: 80PX;
          margin: 0 auto;
          margin-bottom: $spacing-v-xxxl;
          background: center center no-repeat;
          background-size: contain;
        }

        &_tips {
          display: flex;
          flex-direction: column;
          justify-content: center;

          .tips-info {
            font-weight: bold;
            color: $color-text-title;
            font-size: 40px;
            line-height: 40px;
          }

          .info2 {
            line-height: 50px;
          }

          .tips-desc {
            font-size: $font-size-base;
            color: $color-text-title-secondary;
            margin-top: 36px;
          }
        }

        &-stdSuccess .tips-credit,
        &-stdActivate .tips-credit,
        &-notSupportActivate .tips-credit {
          .credit {
            height: 76px;
            line-height: 76px;
            font-size: 76px;
            margin-top: $spacing-v-md;
            color: $color-text-base;

            &::before {
              content: "\00A5";
              font-size: $font-size-xl;
              margin-right: $spacing-h-sm;
            }
          }

          .tips {
            position: relative;
            background: #f4f4f4;
            padding: 8px 6px;
            width: fit-content;
            margin: 10px auto 0;
            border: 1px solid #e5e5e5;
            border-radius: $border-radius-md;
            box-sizing: border-box;
            color: $at-textarea-tips-color;
            font-size: $at-textarea-tips-size;

            &::before,
            &::after {
              left: 48%;
              border: 12px solid transparent !important;
              border-bottom-color: #f4f4f4 !important;
              top: -22px !important;
            }

            &::before {
              top: -23px !important;
            }
          }
        }
      }

      &_btnBlock {
        position: relative;
        padding: 50px $spacing-h-xl 20px;

        .button-tips {
          position: relative;
          background: #f4f4f4;
          padding: 24px;
          margin-top: 34px;
          border: 1px solid #e5e5e5;
          border-radius: 16px;
          box-sizing: border-box;

          &_title {
            font-size: $at-textarea-font-size;
            color: $color-text-base;
            display: flex;
            align-items: center;

            &--icon {
              width: 32px;
              height: 32px;
            }
          }

          &_info {
            color: $at-textarea-tips-color;
            font-size: $at-textarea-tips-size;
            margin-top: $spacing-v-xs;
          }

          &-left::before,
          &-left::after {
            left: 20%;
          }

          &-right::before,
          &-right::after {
            right: 23%;
          }
        }

        .button-view_bubble {
          padding: 10px 13px;
          top: 20px;
          right: $spacing-h-xl;
          position: absolute;
          background: #f80;
          font-size: $font-size-sm;
          line-height: $font-size-sm;
          color: #fff;
          text-align: center;
          border-radius: 0 16px;
        }
      }

      .btn-row {
        display: flex;

        .button-view {
          min-width: 330px;
          flex: 1;
        }

        .at-button--secondary {
          margin-right: 15px;
        }

        .at-button--primary {
          margin-left: 15px;
        }

        .at-button__text {
          font-size: 34px;
        }
      }

      .button-tips,
      .tips-credit .tips {

        &::before,
        &::after {
          content: "";
          position: absolute;
          width: 0;
          height: 0;
          border: 16px solid transparent;
          border-bottom-color: #f4f4f4;
          top: -30px;
        }

        &::before {
          border-bottom-color: #e5e5e5;
          top: -31px;
        }
      }
    }

    .certify-view {
      margin: $spacing-v-md 0 $spacing-v-md;

      .mu-list__title {
        display: none;
      }
    }

    &-steward {
      margin-top: 20px;

      &_list {
        background: $color-grey-base !important;
        margin: 0 20px;

        &-item {
          margin-bottom: $spacing-v-md;
          background: $color-bg;
          border-radius: 16px;

          .at-list__item-content {
            width: auto;
          }

          .item-content__info-title {
            margin-bottom: $spacing-v-sm;
          }

          .item-extra__info,
          .at-button {
            display: inline-flex !important;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      /* stylelint-disable-next-line selector-type-no-unknown */
      mu-list-item:not(:last-child) .certify-view_list-item {
        margin-bottom: 20px;
      }
    }

    &-cheap-tips {
      width: 100%;
      height: 32px;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;

      &_icon {
        width: 28px;
        height: 32px;
        display: inline-block;
        margin-right: 12px;
      }

      &_text {
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: 22px;
        color: #808080;
        line-height: 30px;
        padding: 0;
      }
    }

    &-wangshangList {
      margin: 20px;
      margin-top: 0;
      border-radius: 16px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &_title {
        color: #f80;
        font-size: 32px;
        margin-bottom: 40px;
      }

      &_subTitle {
        font-size: 22px;
        color: #808080;
        background: #e5e5e5;
        border-radius: 18.5px;
        text-align: center;
        padding: 4px 20px;
        margin: 0 auto;
        position: relative;
        top: -21px;
        width: fit-content;
      }

      &_main {
        background: #fff;
        width: 100%;
        margin-top: 20px;

        &::before {
          width: calc(100% - 60px);
          margin: 0 auto;
        }

        .at-list__item-content {
          width: fit-content;
        }

        .item-content__info-title {
          margin-bottom: $spacing-v-sm;
        }

        .at-list__item:not(:last-child)::after {
          display: none;
        }

        .item-extra__info {
          display: inline-flex;
        }
      }
    }

    &-resultTips {
      margin: 40px 30px;

      &_title {
        text-align: center;
        font-size: $font-size-base;
        color: $color-text-base;
      }

      &_step {
        display: flex;
        color: #808080;
        font-size: $font-size-base;
        margin-top: 20px;
      }
    }

    .highlight-text {
      color: $color-orange;
    }

    .creditRenewal {
      color: #808080;
      font-size: 28px;
      text-align: center;
      // padding: 10px 0;
    }
  }

  .mu-subscribe-page {
    margin-top: $spacing-v-md;
  }
}

.result-style {
  p {
    font-size: $font-size-base;
    color: $color-text-title-secondary;
  }

  .main-title {
    margin-top: 36px;
  }

  .highlight-text {
    color: $color-orange;
  }
}

.result-tips-steps-highlight {
  color: $color-orange;
}

.at-button__wxbutton-disbled {
  pointer-events: none;
  cursor: default;

  .at-button--primary {
    opacity: 0.3;
  }
}

.common-btn {
  width: 100%;
}

.entry-Rate {
  margin: 20px;
}

.quicklyAskQuestion {
  margin: 20px;
}

.loadingPage {
  .quicklyAskQuestion {
    margin: 100px 20px 20px 20px;
  }
}

.gbl-basic-imageTitle {
  margin: $spacing-v-xxxl 0;
}