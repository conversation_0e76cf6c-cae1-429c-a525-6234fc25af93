// 申请结果页入口（视图层） 
import Taro, { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import {
  MUView,
  MUText,
  MUImage,
  MURichText,
  MUListItem,
  MUButton,
} from '@mu/zui';
import {
  MarqueeTips, BannerWithoutBorder, MultiFuncRange, ImageTitle
} from '@mu/lui';
import { LdfMidFlow } from '@mu/ldf-flow-card';
import NumberAnimateCom from '../../components/number-animate';
import resultStore from './store';

import './index.scss';

const cheapIcon = 'https://file.mucfc.com/abf/1/0/202212/20221201112617b8d67f.png';
const wgjIcon = 'https://file.mucfc.com/abf/1/0/202212/20221201112640fcdf58.png';

/**
 * 组件状态接口定义
 */
interface ResultPageState {
  credit: string;
  resultContent: any;
  btnBubbleText: string;
}

@observer
class ResultPage extends Component<{}, ResultPageState> {
  state: ResultPageState;

  constructor(props: any) {
    super(props);
    this.state = {
      credit: '50,000.00',
      resultContent: {
        image: 'https://file.mucfc.com/abf/1/0/202212/success-icon.png',
        info: '恭喜您申请成功',
        desc: '您的申请已通过审核，可以开始借款了',
        creditDesc: '额度有效期90天，请及时激活使用',
        buttonList: [{
          text: '立即借款',
          type: 'loan'
        }]
      },
      btnBubbleText: '首借免息券'
    };
  }

  async componentDidMount() {
    await resultStore.initResultCard();
  }

  /**
   * 封装结果描述View
   * @param {Object} content 包含单个结果信息的对象
   */
  getContentView(content: any) {
    if (!content) {
      return null;
    }
    const {
      hasMainCreditToActivate,
    } = resultStore.account || {};
    const {
      curChannelSupport,
      channelControlDesc,
      activationVaildDate
    } = resultStore.apply || {};

    const type = content.type || resultStore.resultType;
    const {
      info, info2, desc, credit, creditDesc, channelControlTip
    } = content;
    const contentDescView = desc;
    return (
      <MUView className={`resultView-main_content resultView-main_content-${type}`}>
        {content.image
          && <MUImage
            className="resultView-main_content_icon icon"
            src={content.image || ''}
          />}
        <MUView className="resultView-main_content_tips">
          <MUView className="resultView-main_content_tips-info tips-info">{info}</MUView>
          {info2 && <MUView className="resultView-main_content_tips-info tips-info info2">{info2}</MUView>}
          <MUView className="resultView-main_content_tips-desc tips-desc">
            <MURichText className="text" nodes={contentDescView} space='nbsp' />
          </MUView>
          {contentDescView && credit && (
            <MUView className="tips-credit">
              <NumberAnimateCom from={credit} totalTime={800} refreshTime={30} />
              {/* 额度如果是待激活额度 本渠道不支持激活时，取中台字段channelControlDesc提示激活，如果未返回且不支持，则兜底显示channelControlTip */}
              {creditDesc && hasMainCreditToActivate && (
                <MUView className="tips">
                  {!curChannelSupport && (channelControlDesc || channelControlTip)}
                  {activationVaildDate}
                  {creditDesc}
                </MUView>
              )}
            </MUView>
          )}
        </MUView>
      </MUView>
    );
  }

  /**
   * 获取结果页按钮view
   * @param {Object} content 包含单个结果信息的对象
   */
  getButtonView(content: any) {
    const { btnBubbleText } = resultStore || {};
    const { buttonList, needBtnBubble } = content;

    if (!buttonList) {
      return null;
    }

    let buttonListView: any[] = [];
    let resultBtnProps = {} as any;
    buttonList.forEach((button: any) => {
      let buttonType = 'primary';
      resultBtnProps = {
        type: buttonType,
        onClick: () => {},
        children: button.text,
      };
      buttonListView.push(resultBtnProps);
    });

    const buttonView = buttonListView.map((button: any) => (
      <MUView className="button-view">
        <MUButton {...button}>{button.children}</MUButton>
        {/* 高亮按钮展示气泡，如成功展示优惠券 */}
        {needBtnBubble && btnBubbleText && button.type === 'primary' && (
          <MUView className="button-view_bubble">{btnBubbleText}</MUView>
        )}
      </MUView>
    ));

    return (
      <MUView className="resultView-main_btnBlock">
        <MUView className="btn-column">{buttonView}</MUView>
      </MUView>
    );
  }

  /**
   * 获取额度自动续期文案
   */
  creditRenewalText = () => {
    const { limitInfoList, limitType } = resultStore.account || {};
    const { limitStatus, fixedLimitValidateDate, autoRenewalFlag }
      = limitInfoList && limitInfoList.find(item => item.limitType === limitType) || {};
    if (fixedLimitValidateDate && autoRenewalFlag && limitStatus === 'SUCCESS') {
      // 日期格式化 YY-MM-DD
      const date = new Date(fixedLimitValidateDate);
      const YY = `${date.getFullYear()}-`;
      const MM = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}-` : `${date.getMonth() + 1}-`;
      const DD = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate();
      return `*额度到期日: ${YY + MM + DD}, 到期后将自动续期`;
    }
    return '';
  }

  /**
   * 防欺诈提示
   */
  getCheatTips() {
    const { showCheatTips } = resultStore.standardResultCard || {};
    const { resultType } = resultStore || {};
    if (!['stdApproval', 'stdFail', 'stdSuccess'].includes(resultType) || !showCheatTips) {
      return null;
    }
    return (
      <MUView className="resultView-cheap-tips">
        <MUImage className="resultView-cheap-tips_icon" src={cheapIcon} />
        <MUText className="resultView-cheap-tips_text">谨防以招联名义收费的诈骗行为，切勿向陌生人转账!</MUText>
      </MUView>
    );
  }

  /**
   * 招联微管家
   */
  getSteward() {
    return (
      <MUView className="resultView-steward">
        <MUView className="resultView-steward_list">
          <MUListItem
            className="resultView-steward_list-item"
            hasBorder={false}
            title="招联微管家" // 主标题
            note="加微管家 享受VIP服务" // 描述
            thumb={wgjIcon}
            extraText="添加"
            beaconId="ClickZLWGJ"
            extraType="button"
            onBtnClick={() => { }}
          />

        </MUView>

      </MUView>
    );
  }

  /**
   * 获取结果提示展示
   * 比如失败页的信用提示
   * @param {Object} content 包含单个结果信息的对象
   */
  getResultTipsView(content: any) {
    const { tips } = content;
    // const { showResultTips } = this.state;
    // 没有配置
    if (!tips) {
      return null;
    }

    const { title, defaultSteps: steps } = tips;

    return (
      true ? <MUView className="resultView-resultTips">
        <MUView className="resultView-resultTips_title">{`- ${title} -`}</MUView>
        {steps.map((o) => o.replace(/<h>/g, '<span class="result-tips-steps-highlight">').replace(/<\/h>/g, '</span>')).map((step) => (
          <MUView className="resultView-resultTips_step"><MURichText className="text" nodes={step} space='nbsp' /></MUView>
        ))}
      </MUView> : <MUView />
    );
  }

  render() {
    const { resultContent, showLDS } = resultStore || {};
    const { showZLSteward } = resultStore.standardResultCard || {};
    
    // 模拟展位数据
    const marqueeTips = { text: '恭喜您申请成功！' };
    const bannerWithoutBorder = {};
    const multiFuncRange = {};
    const imageTitle = {};
    
    
    return (
      <MUView className={`resultCardView resultCardView-${resultStore.resultType}`}>
        <MUView className="resultView">
          <MUView className="resultView-content">
            
            {/* 展位跑马灯 */}
            <MarqueeTips {...marqueeTips} />

            <MUView className="resultView-main">
              {/* 结果文案 */}
              {this.getContentView(resultContent)}

              {/* 按钮 */}
              {this.getButtonView(resultContent)}

              {/* 额度续期文案 */}
              <MUView className="creditRenewal">{this.creditRenewalText()}</MUView>

              {/* 防欺诈提示 */}
              {this.getCheatTips()}

            </MUView>

            {/* 好借钱组件 */}
            {showLDS && <LdfMidFlow beaconId="LdfMidFlow" />}

            {/* MultiFuncRange组件 */}
            <MUView className="multiFuncRange">
              <MultiFuncRange {...multiFuncRange} />
            </MUView>

            {/* 招联微管家 */}
            {showZLSteward && this.getSteward()}

            {/* 展位banner */}
            <BannerWithoutBorder {...bannerWithoutBorder} />

            {/* 展位宣传图 */}
            <ImageTitle {...imageTitle} />

            {/* 结果提示文案 */}
            {this.getResultTipsView(resultContent)}
          </MUView>
        </MUView>
      </MUView>
    );
  }
}

export default ResultPage; 