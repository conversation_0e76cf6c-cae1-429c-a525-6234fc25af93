import { apiHost, fetch } from '@mu/business-basic';

/**
 * 获取客户信息
 * @param params 查询参数
 * @returns 客户信息
 */
async function getCustInfo(params?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.infoMaintain.getCustInfo`, {
        data: params,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

export {
  getCustInfo,
};