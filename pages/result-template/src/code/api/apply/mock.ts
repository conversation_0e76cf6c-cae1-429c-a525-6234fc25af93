// 申请相关 Mock 数据

/**
 * mucfc.apply.apply.queryCardInfo 接口 Mock 数据
 * 
 * 模拟后端返回的原始数据结构，用于本地开发和测试
 */
export const mockQueryCardInfoResponse = {

};

/**
 * mucfc.apply.apply.applyInfo 接口 Mock 数据
 * 
 * 模拟申请信息查询的返回数据结构
 */
export const mockQueryApplyInfoResponse = {
  // 额度信息列表
  limitInfoList: [
    {
      limit: 50000,
      limitType: 'MAIN',
      limitStatus: 'Y', // Y-正常, P-待激活, TOBE_ACTIVATED-待激活
      mainLimit: true,
      activationVaildDate: '90'
    }
  ],
  // 申请信息
  mainApplyNo: 'AP202312010001',
  applyStep: '11', // 申请步骤：3-审批中, 5-审批通过, 11-激活审批中
  rejectCode: '', // 拒绝码
  channelControlDesc: '恭喜您申请成功', // 申请结果副标题文案
  activateChannelFlag: '0', // 激活渠道是否是自有渠道
  curChannelSupport: 'Y' // 当前渠道是否支持激活
};