// 申请相关API封装 
import { apiHost, fetch } from '@mu/business-basic';
import { cardInfoTranslator, applyInfoTranslator } from './translator';
import { mockQueryCardInfoResponse, mockQueryApplyInfoResponse } from './mock';
import { MockConfig } from '../../constants/mockConfig';

async function queryCardInfo(params: any): Promise<any> {
  let response: any;
  
  if (MockConfig.ENABLE_MOCK) {
    // Mock 模式：返回模拟数据
    if (MockConfig.SHOW_MOCK_LOGS) {
      console.log('🎭 Mock Mode: 使用模拟数据代替真实接口调用', { params });
    }
    response = mockQueryCardInfoResponse;
    
    // 模拟网络延迟
    if (MockConfig.MOCK_DELAY > 0) {
      await new Promise(resolve => setTimeout(resolve, MockConfig.MOCK_DELAY));
    }
  } else {
    // 真实接口调用
    try {
      response = await fetch(
        `${apiHost.mgp}?operationId=mucfc.apply.apply.queryCardInfo`, {
          data: params,
          autoLoading: false,
          autoToast: false,
        },
      );
    } catch (error) {
      // 接口调用失败时自动降级到 mock 数据
      console.warn('⚠️ API 调用失败，自动降级到 Mock 数据:', error);
      response = mockQueryCardInfoResponse;
    }
  }
  
  return cardInfoTranslator(response);
}

/**
 * 查询申请信息
 * @param params 查询参数
 * @returns Promise<any>
 */
async function queryApplyInfo(params: any): Promise<any> {
  let response: any;
  
  if (MockConfig.ENABLE_MOCK) {
    // Mock 模式：返回模拟数据
    if (MockConfig.SHOW_MOCK_LOGS) {
      console.log('🎭 Mock Mode: 使用模拟申请信息数据代替真实接口调用', { params });
    }
    response = mockQueryApplyInfoResponse;
    
    // 模拟网络延迟
    if (MockConfig.MOCK_DELAY > 0) {
      await new Promise(resolve => setTimeout(resolve, MockConfig.MOCK_DELAY));
    }
  } else {
    // 真实接口调用
    try {
      response = await fetch(
        `${apiHost.mgp}?operationId=mucfc.apply.apply.applyInfo`, {
          data: params,
          autoLoading: false,
          autoToast: false,
        },
      );
    } catch (error) {
      // 接口调用失败时自动降级到 mock 数据
      console.warn('⚠️ 申请信息 API 调用失败，自动降级到 Mock 数据:', error);
      response = mockQueryApplyInfoResponse;
    }
  }

  return applyInfoTranslator(response);
}

/**
 * 提交申请信息
 * @param params 提交参数
 * @returns 提交结果
 */
async function submitApplyInfo(params: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.apply.apply.submitApplyInfo`, {
        data: params,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 查询是否支持分期花开通
 */
async function checkSupportFQH(): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.coop.mucfcpay.check`, {
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

export {
  queryCardInfo,
  queryApplyInfo,
  submitApplyInfo,
  checkSupportFQH,
}; 