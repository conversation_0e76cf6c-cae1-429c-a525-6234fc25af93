// 申请数据转换器 

/**
 * 申请信息数据转换器
 * 从getApplyInfo接口响应中提取账户相关信息和申请领域信息
 * @param response 接口响应数据
 * @returns 包含账户信息和申请信息的扁平结构
 */
function applyInfoTranslator(response: any) {
  const { 
    limitInfoList = [],
    mainApplyNo = '',
    applyStep = '', 
    rejectCode = '',
    channelControlDesc = '',
    activateChannelFlag = '',
    curChannelSupport = ''
  } = response || {};
  
  const limitInfo = limitInfoList.filter((item: any) => item.mainLimit === true && item.limit > 0);
  
  // 从筛选后的第一条记录中提取字段，如果没有记录则使用默认值
  const {
    limit = 0,
    limitType = '',
    limitStatus = '',
    activationVaildDate = ''
  } = (limitInfo && limitInfo[0]) || {};
  
  return {
    // 账户信息
    account: {
      limit: Number(limit) || 0,
      limitType,
      limitStatus,
      closeReason: '', // 暂时设为空字符串，后续可根据业务需要补充
      limitInfoList
    },
    // 申请信息
    apply: {
      mainApplyNo,
      applyStep,
      rejectCode,
      channelControlDesc,
      activateChannelFlag,
      curChannelSupport,
      activationVaildDate
    }
  };
}

function cardInfoTranslator(response: any) {
  const { cardPropertyInfo = {} } = response || {};
  const { cardExtraProperty: originalCardExtraProperty = {} } = cardPropertyInfo || {};
  
  // 提取领域模型需要的字段
  const {
    finalLoadingTime = '',
    passShowIncrementEntry = '',
    GUIDE_raiseLimit_SWITCH = '',
    passFocusFQH = '',
    GUIDE_doubleStages_SWITCH = '',
    passGuideFQH = '',
    productCode = '',
    GUIDE_needJumpToDiversionPage_SWITCH = '',
    supportJumpToDiversionPage = '',
    GUIDE_supportJumpToDiversionPage_SWITCH = '',
    resultWGJ = '',
    resultFQZTS = '',
    GUIDE_autoSkipBorrowing_SWITCH = '',
    GUIDE_download_SWITCH = ''
  } = originalCardExtraProperty;

  // 直接返回领域模型需要的字段
  return {
    finalLoadingTime,
    passShowIncrementEntry,
    GUIDE_raiseLimit_SWITCH,
    passFocusFQH,
    GUIDE_doubleStages_SWITCH,
    passGuideFQH,
    productCode,
    GUIDE_needJumpToDiversionPage_SWITCH,
    supportJumpToDiversionPage,
    GUIDE_supportJumpToDiversionPage_SWITCH,
    resultWGJ,
    resultFQZTS,
    GUIDE_autoSkipBorrowing_SWITCH,
    GUIDE_download_SWITCH
  };
}

export {
  applyInfoTranslator,
  cardInfoTranslator,
}; 