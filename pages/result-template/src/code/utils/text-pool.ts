import { getLocalConfig } from '@mu/business-basic';
const companyName = getLocalConfig({ configName: 'companyName', nameSpace: 'common' });
const shortCompanyName = getLocalConfig({ configName: 'shortCompanyName', nameSpace: 'common' });

const supplyImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111263346012e.png';
const warningImg = 'https://file.mucfc.com/abf/1/0/202212/20221201112633ccb228.png';
const failImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111262520cef3.png';
const srdFailImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111262574523c.png';
const srdFailNewImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111262569b1ab.png';
const stdSuccessImg = 'https://file.mucfc.com/abf/1/0/202212/20221201112625bf86e6.png';
const stdApprovalImg = 'http://file.mucfc.com/abf/1/0/202212/2022120111262512e9b0.png';
const stdApprovalNewImg = 'https://file.mucfc.com/abf/1/0/202212/202212011126253222d4.png';
const successImg = 'https://file.mucfc.com/abf/1/0/202212/202212011126256a5872.png';
const successNewImg = 'https://file.mucfc.com/abf/1/0/202212/2022120111262589bd47.png';
const inControlImg = 'https://file.mucfc.com/abf/1/0/202212/20221201112617f34ab4.png';
const hasCreditImg = 'https://file.mucfc.com/abf/1/0/202212/20221201112617c32f23.png';
const toActivateImg = 'https://file.mucfc.com/abf/1/0/202212/20221201112617f22005.png';

const RESULT_IMG_MAP = {
  fail: failImg,
  success: successImg,
  inControl: inControlImg,
  toActivate: toActivateImg,
  stdSuccess: stdSuccessImg,
  stdFail: srdFailImg,
  stdFailNew: srdFailNewImg,
  stdApproval: stdApprovalImg,
  stdApprovalNew: stdApprovalNewImg,
  supply: supplyImg,
  hasCredit: hasCreditImg,
  activateSuccess: successImg,
  bioFail: warningImg,
  successNew: successNewImg,
};

/**
 * 文本池
 */
const textPool = {
  /** 结果页卡片状态分类 */
  defaultApplyResultMapNew: {
    stdSuccess: {
      default: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '申请成功',
        desc: '你的可用额度为',
        guideDownloan: '订阅私信并去APP借钱',
        buttonList: [{
          text: '立即借款',
          countDowntText: '正在进入借款',
          type: 'loan' // type标记按钮类型，便于按钮点击事件及埋点区分
        }, {
          text: '开通分期花',
          type: 'fqh',
          desc: {
            title: `什么是${companyName}分期花？`,
            info: `<div class="result-style">${companyName}分期花是一张有额度的分期卡，支持绑定至<span class="highlight-text">微信、支付宝</span>使用，日常消费、发红包、转账等都可以用，覆盖你的生活所需，还可选择1-24期灵活分期，还款更省心。</div>`
          }
        }],
        supportWangShang: true,
        needBtnBubble: true, // 按钮上展示优惠券气泡
        supportChangeInfo: false,
      },
      220003: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '申请成功',
        desc: '你的可用额度为',
        buttonList: [{
          text: '返回',
          type: 'close' // type标记按钮类型，便于按钮点击事件及埋点区分
        }],
        needBtnBubble: true, // 按钮上展示优惠券气泡
      },
      220004: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '申请成功',
        desc: '你的可用额度为',
        creditDesc: `天内去${companyName}APP补充资料并激活额度，才能长期有效哦！`,
        creditDescAli: '天内去好期贷生活号补充资料并激活额度，才能长期有效哦！',
        buttonList: [
          {
            text: `下载${companyName}APP`,
            type: 'download',
          },
          {
            text: '立即激活',
            type: 'activate',
          }, {
            text: '立即借款',
            type: 'loan',
          }
        ],
        needBtnBubble: true, // 按钮上展示优惠券气泡
        supportChangeInfo: true,
        applyUrl: 'DOMAIN/CHANNEL/apply/#/pages/index/index', // 激活申请
        loanUrl: 'DOMAIN/CHANNEL/loan/#/pages/index/index?cashLoanMode=1' // 标准借款页
      },
      220005: {
        autoJumpUrl: 'DOMAIN/CHANNEL/loan/#/pages/index/index?cashLoanMode=1' // 标准借款页
      },
      220007: {
        title: '激活结果',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '恭喜，额度激活成功！',
        desc: '',
        buttonList: [{
          text: '返回首页',
          type: 'close' // type标记按钮类型，便于按钮点击事件及埋点区分
        }],
        needBtnBubble: false, // 按钮上展示优惠券气泡
      }
    },
    stdApproval: {
      default: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdApprovalNew,
        info: '申请提交成功',
        guideDownloan: '订阅私信并去APP查看结果',
        // eslint-disable-next-line max-len
        desc: `<div class="result-style"><p class="main-title">${shortCompanyName}审批中，预计在<span class="highlight-text">3分钟</span>内完成审批</p><p class="sub-title">请你留意<span class="highlight-text">短信</span>或<span class="highlight-text">电话</span>通知</p></div>`,
        buttonList: [{
          text: '设置交易密码',
          type: 'tradePsw',
          backupButton: {
            text: '回到首页 边逛边等',
            type: 'close' // type标记按钮类型，便于按钮点击事件区分
          }
        }],
        needBtnBubble: false,
      },
      220004: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdApprovalNew,
        info: '申请提交成功',
        // eslint-disable-next-line max-len
        desc: `<div class="result-style"><p class="main-title">${shortCompanyName}审批中，预计在<span class="highlight-text">3分钟</span>内完成审批</p><p class="sub-title">请你留意<span class="highlight-text">短信</span>或<span class="highlight-text">电话</span>通知</p></div>`,
        buttonList: [{
          text: `去${companyName}APP查看结果`,
          type: 'download',
        }],
        needBtnBubble: false,
      }
    },
    stdFail: {
      default: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdFailNew,
        info: '抱歉，你暂不符合该贷款产品申请要求',
        desc: '你可以保持良好信用，一个月以后再来试一试',
        resultCodeText: {
          ASA00001: {
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你可以保持良好信用，一个月以后再来试一试',
          },
          ASA00002: {
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你可以保持良好信用，一个月以后再来试一试',
          },
          ASA00010: {
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你可以保持良好信用，一个月以后再来试一试',
          },
          // 账户敦客管控
          ASA00012: {
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你可以保持良好信用，一个月以后再来试一试',
            modalConfig: {
              modalCotnent: '经初筛，你满足其他产品的申请条件',
              modalConfirmText: '去申请',
              modalCancelText: '取消',
            }
          },
          // 账户挂失冻结
          ASA00013: {
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你可以保持良好信用，一个月以后再来试一试',
            modalConfig: {
              modalCotnent: '当前账户处于挂失状态，是否需要现在解除挂失？',
              modalConfirmText: '解除挂失',
              modalCancelText: '取消',
            },
          },
          // 账户逾期管控
          ASA00014: {
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你可以保持良好信用，一个月以后再来试一试',
            modalConfig: {
              modalCotnent: '抱歉，你当前已逾期，正常还款后，才能继续申请该贷款产品',
              modalConfirmText: '去还款',
              modalCancelText: '取消',
            },
          },
          UMDP03406: { // 账户挂失
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '您的账户已挂失，请先接触挂失后再来申请',
            buttonList: [{
              text: '解除挂失',
              type: 'lost',
            }],
          },
          UMDP03407: { // 账户逾期管控
            info: '抱歉，你暂不符合该贷款产品申请要求',
            desc: '你的账户当前已逾期，请还款后再来申请',
            buttonList: [{
              text: '去还款',
              type: 'repayment',
            }],
          },
          UMDP03408: { // 找客服申请
            info: '您已有额度，可联系客服处理',
            desc: '您的申请遇到问题，请联系在线客服协助',
            buttonList: [{
              text: '联系客服',
              type: 'service',
            }],
          },
          UMDP03409: { // 104、105、106 管控引导
            info: '您已有额度，重新评估后即可使用',
            desc: '',
            buttonList: [{
              text: '去评估',
              type: 'unfreeze',
            }],
          },
          ASA00004: {
            info: '本次申请暂未通过',
            desc: '你的年龄暂不符合申请要求',
          }
        },
        buttonList: [{
          text: '回到首页 以后再试',
          type: 'close' // type标记按钮类型，便于按钮点击事件区分
        }],
        needBtnBubble: false,
        tips: {
          title: '如何保持良好的个人信用',
          defaultSteps: [
            '1.量入为出，合理消费，不过度负债',
            '2.牢记贷款、信用卡的还款日期和金额，按时足额还款',
            '3.定期关注个人信用情况，及时发现征信异常信息，但不过度、频繁查询个人征信',
            '4.妥善保管个人信息并及时更新基本信息'
          ]
        }
      },
      220005: {
        title: '借款结果',
        image: RESULT_IMG_MAP.stdFailNew,
        info: '经综合评估，你暂不符合借款条件',
        desc: '你可以保持良好信用，一个月以后再来试一试',
        buttonList: [{
          text: '回到首页 以后再试',
          type: 'close' // type标记按钮类型，便于按钮点击事件区分
        }],
        needBtnBubble: false,
        tips: {
          title: '如何保持良好的个人信用',
          defaultSteps: [
            '1.量入为出，合理消费，不过度负债',
            '2.牢记贷款、信用卡的还款日期和金额，按时足额还款',
            '3.定期关注个人信用情况，及时发现征信异常信息，但不过度、频繁查询个人征信',
            '4.妥善保管个人信息并及时更新基本信息'
          ]
        }
      },
      220007: {
        title: '激活结果',
        image: RESULT_IMG_MAP.stdFailNew,
        info: '本次激活暂未通过',
        desc: '你可以保持良好信用，一个月以后再来试一试',
        buttonList: [{
          text: '回到首页 以后再试',
          type: 'close' // type标记按钮类型，便于按钮点击事件区分
        }],
        needBtnBubble: false,
        tips: {
          title: '如何保持良好的个人信用',
          defaultSteps: [
            '1.量入为出，合理消费，不过度负债',
            '2.牢记贷款、信用卡的还款日期和金额，按时足额还款',
            '3.定期关注个人信用情况，及时发现征信异常信息，但不过度、频繁查询个人征信',
            '4.妥善保管个人信息并及时更新基本信息'
          ]
        }
      },
    },
    toActivate: {
      default: {
        title: '额度待激活',
        image: RESULT_IMG_MAP.inControl,
        info: '你已获得招联金融额度',
        desc: '请返回原申请渠道激活额度，如有疑问请联系客服',
        button: '去查看',
        buttonList: [{
          text: '返回',
          type: 'close'
        }],
        needBtnBubble: false,
      },
    },
    cross: {
      default: {
        title: '预审通过',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '初审已通过',
        info2: '回招联金融官方应用继续申请',
        desc: '请留意短信指引，继续补充资料，完成申请',
        buttonList: [{
          text: '返回首页',
          type: 'close',
          backupButton: {
            text: '返回首页',
            type: 'close'
          }
        }],
        needBtnBubble: false,
        needBtnChange: true,
      }
    },
    stdActivate: {
      default: {
        title: '申请结果',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '申请成功，还差一步即可借款',
        desc: '您的待激活额度为',
        creditDesc: '天内激活额度，才能长期有效哦！',
        creditDescAli: '天内去好期贷生活号补充资料并激活额度，才能长期有效哦！',
        buttonList: [
          {
            text: '立即激活',
            type: 'toActivate',
          }
        ],
      }
    },
    notSupportActivate: {
      default: {
        title: '额度待激活',
        image: RESULT_IMG_MAP.inControl,
        info: '你已获得招联金融额度',
        desc: '您的待激活额度为',
        creditDesc: '天内激活额度，才能长期有效哦！',
        channelControlTip: '请返回原申请渠道激活额度。',
        button: '去查看',
        buttonList: [{
          text: '返回首页',
          type: 'close'
        }],
      }
    },
    hasCredit: {
      default: {
        title: '已获得额度',
        image: RESULT_IMG_MAP.hasCredit,
        info: ` 你已获得${companyName}额度`,
        desc: `请下载${companyName}APP查看`,
        descAli: '请去好期贷生活号查看',
        buttonList: [{
          text: '去查看',
          type: 'download',
          backupButton: {
            text: '立即借款',
            type: 'loan',
          }
        }],
        needBtnBubble: false,
        loanUrl: 'DOMAIN/CHANNEL/loan/#/pages/index/index?cashLoanMode=1' // 标准借款页
      }
    },
    activateSuccess: {
      default: {
        title: '激活结果',
        image: RESULT_IMG_MAP.stdSuccess,
        info: '恭喜你！额度激活成功！',
        desc: '',
        buttonList: [{
          text: '返回首页',
          type: 'close',
        }],
        needBtnBubble: false,
      }
    },
    activateFail: {
      default: {
        title: '激活结果',
        image: RESULT_IMG_MAP.fail,
        info: '很遗憾，激活未成功',
        desc: `建议你累积良好信用记录，过段时间再来，${shortCompanyName}会在这里等着你~`,
        buttonList: [{
          text: '返回首页',
          type: 'close',
        }],
        needBtnBubble: false,
      }
    },
    supply: {
      default: {
        title: '待补充证件',
        image: RESULT_IMG_MAP.supply,
        info: `你的${companyName}额度申请缺少信息`,
        desc: `请下载${companyName}APP补充相关证件`,
        buttonList: [{
          text: '下载最新APP',
          type: 'download',
        }],
        needBtnBubble: false,
      }
    },
  }
};

export default textPool;
export { textPool };