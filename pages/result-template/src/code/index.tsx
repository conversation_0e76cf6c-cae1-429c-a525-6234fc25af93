import Madp from '@mu/madp';
import { MUView } from '@mu/zui';
import './index.scss';

export default class OnboardingTemplate extends Madp.Component {
  defaultProps = {};

  constructor(props) {
    super(props);
  }

  onClick = (event) => {
    console.log('event trigger');
  };

  // data-template="zl"用于统计模板使用情况 不影响使用，勿删

  render() {
    return (
      <MUView
        className="onboarding-template"
        data-template="zl"
        onClick={this.onClick}
      >
        这是一个测试代码片段
      </MUView>
    );
  }
}
