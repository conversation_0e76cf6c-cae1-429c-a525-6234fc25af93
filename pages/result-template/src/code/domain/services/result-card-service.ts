import { getRemoteConfig } from '@mu/business-basic';
import { getCustInfo } from '../../api/user/api';

export class ResultCardService {
  // 获取非标导流组件展示标签
  static async getLdsTag() {
    const showHJQCardSwitch = await getRemoteConfig('showHJQCardSwitch', 'N'); // HJQ开关
    // 这里不能使用 sdk 里面的 getLoginInfo，好借钱标签是被拒后实时打标的，getLoginInfo 有15min缓存，会导致查不到标签
    const { userTagList } = await getCustInfo();
    const hasHJQTag = userTagList && userTagList.findIndex((item) => item.tagCode === 'U_CUST_MAIN' && item.tagValue === 'HJQ') > -1; // HJQ标签
    const showLDS = showHJQCardSwitch === 'Y' && hasHJQTag; // 是否展示卡片
    return showLDS;
  };
}