import { queryCardInfo, queryApplyInfo, submitApplyInfo, checkSupportFQH } from '../../api/apply/api';
import { getStandardResultCardInstance } from '../models/apply/standard-result-card';
import { getAccountInstance } from '../models/apply/account';
import { getApplyInstance } from '../models/apply/apply';

export class ApplyService {
  /**
   * 获取卡片信息并更新领域模型
   * @param params 查询参数
   */
  static async getCardInfo(params: any): Promise<void> {
    // 调用接口获取数据
    const cardData = await queryCardInfo(params);
    
    // 获取领域模型实例并更新数据
    const standardResultCardModel = getStandardResultCardInstance();
    standardResultCardModel.setStandardResultCardInfo(cardData);
  }

  /**
   * 获取申请信息并更新账户和申请领域模型
   * @param params 查询参数
   */
  static async getApplyInfo(params: any): Promise<void> {
    // 调用接口获取数据
    const response = await queryApplyInfo(params);
    const { account, apply } = response || {};
    
    // 获取账户领域模型实例并更新数据
    const accountModel = getAccountInstance();
    accountModel.setAccountInfo(account);

    // 获取申请领域模型实例并更新数据
    const applyModel = getApplyInstance();
    applyModel.setApplyInfo(apply);
  }

  /**
   * 提交申请信息
   * @param params 提交参数
   * @returns 提交结果
   */
  static async submitApplyInfo(params: any): Promise<void> {
    const response = await submitApplyInfo(params);
    return response;
  }

  /**
   * 查询是否支持分期花开通
   * @returns 是否支持分期花开通
   */
  static async checkSupportFQH(): Promise<boolean> {
    const response = await checkSupportFQH();
    const { retCode } = response || {};
    return retCode === 'UBS00000';;
  }
}