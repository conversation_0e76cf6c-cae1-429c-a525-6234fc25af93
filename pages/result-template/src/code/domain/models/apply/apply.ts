import { observable, action } from 'mobx';
import { ApplyInfo } from '../../../types/apply';

/**
 * 申请领域模型
 * 申请实体（聚合根），包含申请相关信息
 */
export class ApplyModel implements ApplyInfo {
  /** 案件编号 */
  @observable mainApplyNo: string = '';
  
  /** 申请步骤 */
  @observable applyStep: string = '';
  
  /** 拒绝码 */
  @observable rejectCode: string = '';
  
  /** 申请结果副标题文案 */
  @observable channelControlDesc: string = '';
  
  /** 激活渠道是否是自有渠道 */
  @observable activateChannelFlag: string = '';
  
  /** 当前渠道是否支持激活 */
  @observable curChannelSupport: string = '';

  /** 激活有效期 */
  @observable activationVaildDate: string = '';

  /**
   * 批量设置申请信息
   * @param data 申请数据（支持部分更新）
   */
  @action.bound
  setApplyInfo = (data: Partial<ApplyInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 单例实例
let applyInstance: ApplyModel | null = null;

/**
 * 获取申请领域模型单例实例
 * @returns ApplyModel实例
 */
export function getApplyInstance(): ApplyModel {
  if (!applyInstance) {
    applyInstance = new ApplyModel();
  }
  return applyInstance;
} 