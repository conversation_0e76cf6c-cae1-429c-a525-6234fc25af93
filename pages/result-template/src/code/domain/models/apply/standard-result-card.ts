import { observable, action, computed } from 'mobx';
import { getEnv } from '@mu/madp-utils';
import { StandardResultCardInfo } from '../../../types/apply';

/**
 * 申请结果页卡片属性值对象
 */
export class StandardResultCardModel implements StandardResultCardInfo {
  /**
   * 审批倒计时配置（秒）
   */
  @observable finalLoadingTime = '';

  /**
   * 审批通过是否展示提额入口
   */
  @observable passShowIncrementEntry = '';

  /**
   * 提额引导开关（兼容新属性）
   */
  @observable GUIDE_raiseLimit_SWITCH = '';

  /**
   * 审批通过是否重点引导分期花
   */
  @observable passFocusFQH = '';

  /**
   * 双阶段引导开关（兼容新属性）
   */
  @observable GUIDE_doubleStages_SWITCH = '';

  /**
   * 审批通过是否引导分期花
   */
  @observable passGuideFQH = '';

  /**
   * 产品编码（用于entryId）
   */
  @observable productCode = '';

  /**
   * 是否跳导流页-非自动跳转
   */
  @observable GUIDE_needJumpToDiversionPage_SWITCH = '';

  /**
   * 是否自动跳转导流页
   */
  @observable supportJumpToDiversionPage = '';

  /**
   * 自动跳转导流页开关（兼容新属性）
   */
  @observable GUIDE_supportJumpToDiversionPage_SWITCH = '';

  /**
   * 是否展示招联微管家
   */
  @observable resultWGJ = '';

  /**
   * 是否展示防欺诈提示
   */
  @observable resultFQZTS = '';

  /**
   * 是否自动跳借款
   */
  @observable GUIDE_autoSkipBorrowing_SWITCH = '';

  /**
   * 审批通过&审批中-引导下载
   */
  @observable GUIDE_download_SWITCH = '';

  // ========== 计算属性 ==========

  /**
   * 审批倒计时配置（秒）
   */
  @computed get loadingTime(): number {
    return Number(this.finalLoadingTime) || (getEnv() !== 'prod' ? 30 : 10);
  }

  /**
   * 审批通过是否展示提额入口
   */
  @computed get showIncrementEntry(): boolean {
    return (this.passShowIncrementEntry === 'Y' || this.GUIDE_raiseLimit_SWITCH === 'Y');
  }

  /**
   * 审批通过是否重点引导分期花
   */
  @computed get isPassFocusFQH(): boolean {
    return this.passFocusFQH === 'Y' || this.GUIDE_doubleStages_SWITCH === 'Y';
  }

  /**
   * 审批通过是否引导分期花
   */
  @computed get isPassGuideFQH(): boolean {
    return this.passGuideFQH === 'Y';
  }

  /**
   * 认证列表跳转entryId
   */
  @computed get entryId(): string {
    return this.productCode || '';
  }

  /**
   * 是否跳导流页-非自动跳转
   */
  @computed get needJumpToDiversionPage(): boolean {
    return this.GUIDE_needJumpToDiversionPage_SWITCH === 'Y';
  }

  /**
   * 是否自动跳转导流页
   */
  @computed get showsupportJumpToDiversionPage(): boolean {
    return this.supportJumpToDiversionPage === 'Y' || this.GUIDE_supportJumpToDiversionPage_SWITCH === 'Y';
  }

  /**
   * 是否展示招联微管家
   */
  @computed get showZLSteward(): boolean {
    return this.resultWGJ === 'Y';
  }

  /**
   * 是否展示防欺诈提示
   */
  @computed get showCheatTips(): boolean {
    return this.resultFQZTS === 'Y';
  }

  /**
   * 是否自动跳借款
   */
  @computed get isAutoSkipBorrowing(): boolean {
    return this.GUIDE_autoSkipBorrowing_SWITCH === 'Y';
  }

  /**
   * 审批通过&审批中是否引导下载
   */
  @computed get isGuideDownloan(): boolean {
    return this.GUIDE_download_SWITCH === 'Y';
  }

  /**
   * 批量设置申请结果页卡片属性
   * @param data 申请结果页卡片数据（支持部分更新）
   */
  @action.bound
  setStandardResultCardInfo = (data: Partial<StandardResultCardInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 申请结果页卡片单例实例
let standardResultCardInstance: StandardResultCardModel | null = null;

/**
 * 获取申请结果页卡片单例实例
 * @returns StandardResultCardModel实例 - 全局唯一的申请结果页卡片实例
 */
export const getStandardResultCardInstance = (): StandardResultCardModel => {
  if (!standardResultCardInstance) {
    standardResultCardInstance = new StandardResultCardModel();
  }
  return standardResultCardInstance;
}; 