import { observable, action, computed } from 'mobx';
import { AccountInfo } from '../../../types/account';

/**
 * 账户领域模型
 * 账户实体，包含额度相关信息
 */
export class AccountModel implements AccountInfo {
  /** 总额度 */
  @observable limit: number = 0;
  
  /** 额度类型 */
  @observable limitType: string = '';
  
  /** 额度状态 */
  @observable limitStatus: string = '';
  
  /** 额度关闭原因 */
  @observable closeReason: string = '';

  /** 额度列表 */
  @observable limitInfoList: any[] = [];

  // ========== 计算属性 ==========

  /**
   * 是否有待激活主额度
   */
  @computed get hasMainCreditToActivate(): boolean {
    return this.limitStatus === 'P' || this.limitStatus === 'TOBE_ACTIVATED';
  }

  /**
   * 是否有正常主额度
   */
  @computed get hasUsableMainCredit(): boolean {
    return this.limitStatus === 'Y';
  }

  /**
   * 是否有额度
   */
  @computed get hasCredit(): boolean {
    return this.limit > 0;
  }

  /**
   * 批量设置账户信息
   * @param data 账户数据（支持部分更新）
   */
  @action.bound
  setAccountInfo = (data: Partial<AccountInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 单例实例
let accountInstance: AccountModel | null = null;

/**
 * 获取账户领域模型单例实例
 * @returns AccountModel实例
 */
export function getAccountInstance(): AccountModel {
  if (!accountInstance) {
    accountInstance = new AccountModel();
  }
  return accountInstance;
} 