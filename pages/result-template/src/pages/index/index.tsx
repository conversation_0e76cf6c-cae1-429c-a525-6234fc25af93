import { Component } from '@tarojs/taro';
import { MUView, MUButton } from '@mu/zui';
import { observer } from '@tarojs/mobx';
import IndexStore from './store';

@observer
class IndexPage extends Component {
  onClick() {
    IndexStore.localAction();
  }

  render() {
    return (
      <MUView>
        {IndexStore.localThing}
        平方是：{IndexStore.localComputedThing}
        <MUButton onClick={this.onClick.bind(this)}>点击更改状态+1</MUButton>
      </MUView>
    );
  }
}

export default IndexPage;
