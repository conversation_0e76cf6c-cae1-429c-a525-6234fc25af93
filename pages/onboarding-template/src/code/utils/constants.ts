import { getLocalConstant } from '@mu/business-basic';

export const legaoPageIdMap = {
  stdPolling: 'ba291fda-f827-403d-bddb-c5c6263208d5',
  stdFail: '3d724622-a17f-4ec4-905c-619a4f4c800c',
  stdApproval: 'd4da6177-96f4-49d0-a5a6-ef9382b94c99',
  stdSuccess: '5d398c84-1ea0-4d74-9aad-008ffebb64aa',
  preFail: '6dd455fd-ecf7-477f-9004-3515bf6b6210',
  success: 'd6bb7c9a-44f5-4954-968d-ac3587360da6',
  inControl: '1877b0f9-eea2-4dda-9e1f-2d8283d6faf9',
  hasCredit: 'cb715c4f-a8e9-4df8-87f8-cc53cbe6e40e',
  toActivate: '',
  supply: '',
  activateSuccess: '',
  activateFail: '',
};

/**
 * 1、用来兼容马上，因为马上的链接是合作方写死的，所以要通过商户号映射取出productCode和mapCode（马上的部分是必须的）
 * 2、对于贝壳而言，因为切换贝壳链接以及小程序发布之间有时间间隔
 *  - 避免先发布后切链接导致代码发布后因为还未切链接导致拿不到productCode等
 *  - 后面可以贝壳部分，或者如果先切链接后发布，也可以不用贝壳的
 */
export const merchantIdToMsg = {
  ********: {
    mapCode: '6a22cc248508a4c6',
    productCode: 'CPCJ.CPCJ02'
  },
  ********: {
    mapCode: '8ccc64d69ae03d55',
    productCode: 'CPCJ.CPCJ03'
  }
};

export const getLoanUrl = () => {
  let loanUrl = getLocalConstant({ configName: 'loan', nameSpace: 'link' });
  loanUrl = `${loanUrl}?cashLoanMode=1`;
  return loanUrl;
};

export const getNewPhoneOwnerUrl = () => {
  const newPhoneOwnerUrl = '/loginregister/pages/NewPhoneOwner/AccountRecovery/index';
  return newPhoneOwnerUrl;
};