export default {
  '13A': [
    {
      contractTypeFlag: true,
      contractType: 'AUTH_CON_ZX',
      forceReadFlag: 'Y',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: true,
      contractType: 'SENSITIVEAUTH',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: true,
      contractType: 'LIMIT_INFO',
      forceReadFlag: 'Y',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: true,
      contractType: 'BANK_CARD_AUTH',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: true,
      contractType: 'LOAN_APPLY_NOTICE',
      forceReadFlag: 'Y',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: true,
      contractType: 'PAY_DEDUCT',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: true,
      contractType: 'FACE_AUTH',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: false,
      contractType: 'CREDIT',
      forceReadFlag: 'Y',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: false,
      contractType: 'SENSITIVEAUTH',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: false,
      contractType: 'LIMIT',
      forceReadFlag: 'Y',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: false,
      contractType: 'BANKCARDAUTH',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: false,
      contractType: 'PAYWITHHOLD',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    },
    {
      contractTypeFlag: false,
      contractType: 'FACEAUTH',
      forceReadFlag: 'N',
      catalogueName: '申请',
      readDuration: 5,
      catalogueCode: '13A'
    }
  ],
  '13E': [
    {
      contractTypeFlag: true,
      contractType: 'AUTH_CON_ZX',
      forceReadFlag: 'Y',
      catalogueName: '提额',
      readDuration: 5,
      catalogueCode: '13E'
    },
    {
      contractTypeFlag: true,
      contractType: 'AUTH_INFO_MANAGER',
      forceReadFlag: 'N',
      catalogueName: '提额',
      readDuration: 5,
      catalogueCode: '13E'
    },
    {
      contractTypeFlag: false,
      contractType: 'CREDIT',
      forceReadFlag: 'Y',
      catalogueName: '提额',
      readDuration: 5,
      catalogueCode: '13E'
    },
    {
      contractTypeFlag: false,
      contractType: 'SALESTOOLAUTH',
      forceReadFlag: 'N',
      catalogueName: '提额',
      readDuration: 5,
      catalogueCode: '13E'
    }
  ]
};

/**
* 协议编码-协议编码映射
*/
export const contractCodeMap = {
 // 联合贷
 LHDC: {
   contracts: [{
     id: 0,
     contractType: 'CREDITCOMPRE',
     text: '个人征信授权书（贷款机构）、个人信息授权协议（贷款机构）、个人信息授权书（保险机构）'
   }, {
     id: 1,
     contractType: 'BUSICOOPAUTHUN',
     text: '业务合作信息授权书'
   }, {
     id: 2,
     contractType: 'LIMIT',
     text: '个人借款额度合同'
   }, {
     id: 3,
     contractType: 'NOTSTUDENT',
     text: '非学生承诺函'
   }],
   contractText: '征信授权、资信授权、敏感信息授权、额度合同及非学生承诺函',
   notarizeType: '2'// 是否执行公证 已下线，可以不传 或默认1
 },
 // 前海公证，已下线
 QHGZC: {
   contracts: [{
     id: 0,
     contractType: 'CREDIT',
     text: '个人征信授权书、个人信息授权协议'
   }, {
     id: 1,
     contractType: 'LIMIT',
     text: '个人借款额度合同'
   }, {
     id: 2,
     contractType: 'FQAPPLY',
     text: '深圳市前海公证处公证申请表'
   }],
   contractText: '个人征信授权书、信息授权协议、借款额度合同、公证申请表',
   notarizeType: '2' // 是否执行公证 已下线，可以不传 或默认1
 },
 // 信用贷，标准协议（默认值）
 XYDC: {
   contracts: [{
     id: 0,
     contractType: 'CREDIT',
     text: '个人征信及资信信息授权书'
   },
   {
     id: 1,
     contractType: 'SENSITIVEAUTH',
     text: '敏感信息处理授权书'
   }, {
     id: 2,
     contractType: 'LIMIT',
     text: '个人借款额度合同'
   },
   {
     id: 3,
     contractType: 'NOTSTUDENT',
     text: '非学生承诺函'
   }
   ],
   contractText: '征信授权、资信授权、敏感信息授权、额度合同及非学生承诺函',
   notarizeType: '2'// 是否执行公证 已下线，可以不传 或默认1
 },
 // 信用贷非自营渠道
 XYDCFZY: {
   contracts: [{
     id: 0,
     contractType: 'CREDIT',
     text: '个人征信及资信信息授权书'
   },
   {
     id: 1,
     contractType: 'BUSICOOPAUTHIN',
     text: '业务合作信息授权书'
   }, {
     id: 2,
     contractType: 'LIMIT',
     text: '个人借款额度合同'
   }, {
     id: 3,
     contractType: 'NOTSTUDENT',
     text: '非学生承诺函'
   }
   ],
   contractText: '征信授权、资信授权、合作信息授权、额度合同及非学生承诺函',
   notarizeType: '2'// 是否执行公证 已下线，可以不传 或默认1
 },
 // 话费宝协议
 HFB: {
   contracts: [{
     id: 0,
     contractType: 'CREDITCOMPRE',
     text: '个人征信授权书、个人信息授权协议'
   }, {
     id: 1,
     contractType: 'TOLLTREASUREAUTH',
     text: '合作业务信息授权书'
   }, {
     id: 2,
     contractType: 'LIMIT',
     text: '个人借款额度合同'
   },
   {
     id: 3,
     contractType: 'TOLLCONSUMELOANSERVICE',
     text: '“话费宝-信用延停”消费贷款服务单'
   }, {
     id: 4,
     contractType: 'NOTSTUDENT',
     text: '非学生承诺函'
   }
   ],
   contractText: '征信授权、资信授权、合作信息授权、额度合同、消费贷款服务单及非学生承诺函',
   notarizeType: '2'
 },
 // 录屏提额
 LPTEC: {
   contracts: [{
     id: 0,
     contractType: 'CREDIT',
     text: '个人征信及资信信息授权书'
   }],
   contractText: '个人征信及资信信息授权书',
   notarizeType: '1'// 是否执行公证 已下线，可以不传 或默认1
 },
 // A2有效大e贷额度合同
 A2ED: {
   contracts: [{
     id: 0,
     contractType: 'LIMIT',
     text: '个人借款额度合同'
   }],
   contractText: '借款额度合同',
   notarizeType: '2'
 }
};
