import { Validator, getCurrentPageUrl } from '@mu/madp-utils';
import { dispatchTrackEvent } from '@mu/madp-track';
import defaultChannelParams from './contract-params';
import Madp from '@mu/madp';

/**
 * 查找对象数组中，含有目标对象的项
 * @param {Array} rangeArray 待查对象数组
 * @param {object} targetObj 目标对象
 */
export const findObjFromArr = (rangeArray, targetObj) => {
  let index = 0;
  let target = {};
  if (rangeArray && rangeArray.length) {
    target = rangeArray.filter((item, i) => {
      const keys = Object.keys(targetObj);
      if (keys.every((k) => String(item[k]) === String(targetObj[k]))) {
        index = i;
        return true;
      }
      return false;
    })[0] || {};
  }
  return {
    target,
    index
  };
};

/**
  * 校验名称格式
  * @param {String} name 名称
  */
export const checkName = (name: string) => {
  if (Validator.isName) {
    // 仅支持汉字和中文圆点
    return !Validator.isName(name);
  } else {
    return !Validator.isChineseCharacterOnly(name);
  }
};

/**
 * 获取明文身份证年龄
 * @param {String} certId 身份证号
 */
export const getCertIdAge = (certId: string) => {
  const info = Validator.getIdCardInfo(certId);
  return Validator.getAge(info.year, info.month, info.day);
};

/**
 * 从本地配置文件中获取合同配置
 * */
export const queryContractConfig = (catalogueCode = '13A') => {
  const channelcontractConfigList: any[] = [];
  const channelCatalogueAgreementDtoList = defaultChannelParams[catalogueCode];
  channelCatalogueAgreementDtoList.forEach((item: any) => {
    channelcontractConfigList.push(item);
  });
  return channelcontractConfigList;
};

/**
 * 是否是小程序环境
 */
export const isWeapp = process.env.TARO_ENV === 'weapp';

/** 是否是H5环境 */
export const isH5 = process.env.TARO_ENV === 'h5';

/** 是否是抖音小程序环境 */
export const isTT = process.env.TARO_ENV === 'tt';

/**
 * 手动埋点方法，在基础方法上拓展，封装添加模块Id、PageId
 * @param {string} pageId 页面Id
 * @param {object} beaconObj 埋点数据对象，跟dispatchTrackEvent定义的所需参数一样
 */
export const sendTrackBeacon = (pageId, beaconObj) => {
  let moduleId = process.env.TRACK_MODULE_ID; // 默认工程moduleId
  // 兼容小程序moduleId获取（路由上带）
  const currentRoute = getCurrentPageUrl();
  if (
    (process.env.TARO_ENV !== 'h5') && typeof currentRoute === 'string'
  ) {
    const matchRes = currentRoute.match(/^\/?(.+)\/pages\/.+$/);
    if (matchRes) {
      // eslint-disable-next-line prefer-destructuring
      moduleId = matchRes[1];
    }
  }
  let beaconId = moduleId;
  // 传参只有pageId
  if (pageId && !beaconObj.beaconId) {
    beaconId = `${beaconId}.${pageId}`;
  }
  // 传参已拼pageId
  if (!pageId && beaconObj.beaconId) {
    beaconId = `${beaconId}.${beaconObj.beaconId}`;
  }
  // 传参分开pageId和beaconId
  if (pageId && beaconObj.beaconId) {
    beaconId = `${beaconId}.${pageId}.${beaconObj.beaconId}`;
  }

  dispatchTrackEvent({
    ...beaconObj,
    beaconId
  });
};

/**
 * 获取掩码电话号码
 */
export const getMaskMobileNum = (mobileNum: string) => {
  const maskMobileNum = `${mobileNum.slice(0, 3)}****${mobileNum.slice(7)}`;
  return maskMobileNum;
};

/**
 * 检查手机号码是否合法
 */
export const checkPhoneNum = (mobileNum: string, needShowToast?: boolean) => {
  if (!mobileNum) {
    needShowToast && Madp.showToast({
      title: '请输入手机号码',
      icon: 'none'
    });
    return false;
  }
  const isValid = Validator.isMobilePhoneNumber(mobileNum);
  const isMaskMobileNum = mobileNum.length === 11 && mobileNum.indexOf('****') !== -1;
  if (!(isValid || isMaskMobileNum)) {
    needShowToast && Madp.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    });
    return false;
  }
  return true;
};