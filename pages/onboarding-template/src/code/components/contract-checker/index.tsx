import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MUContractChecker,
} from '@mu/zui';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('./index.scss');
}

const propTypes = {
  contracts: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      text: PropTypes.string.isRequired
    })
  ).isRequired,
  onContractClick: PropTypes.func.isRequired,
  onChangeContractStatus: PropTypes.func,
  beaconId: PropTypes.string.isRequired,
  contractText: PropTypes.string.isRequired,
  beforeContractText: PropTypes.string,
  handleCheckboxClick: PropTypes.func,
  checkedValue: PropTypes.bool,
  outerControl: PropTypes.bool,
  isRhAuthorized: PropTypes.bool,
  contractType: PropTypes.string,
  forceReadContractChecked: PropTypes.bool,
  forcedReadContract: PropTypes.shape({
    name: PropTypes.string
  })
};

const defaultProps = {
  beforeContractText: '同意',
  checkedValue: false,
  outerControl: false,
  contractType: '',
  isRhAuthorized: false,
  forcedReadContract: {
    name: ''
  },
  contractNowrap: false,
  forceReadContractChecked: false,
  handleCheckboxClick: () => { },
  onChangeContractStatus: () => { },
};

class ContractChecker extends Component {
  constructor(props) {
    super(props);

    // 倒计时timer
    this.timer = null;

    this.state = {
      checked: props.checkedValue,
    };
  }

  componentDidMount() {
    PropTypes.checkPropTypes(propTypes, this.props, 'prop', 'ContractChecker');
  }

  componentWillReceiveProps(nextProps) {
    const { checkedValue } = nextProps;
    if (checkedValue !== this.props.checkedValue && checkedValue !== this.state.checked) {
      this.setState({ checked: checkedValue });
    }
  }
  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }


  /**
   * 点击合同checkbox
   */
  onCheckboxClick = (val) => {
    const { checked } = this.state;
    const {
      onChangeContractStatus,
      handleCheckboxClick,
      forceReadContractChecked,
      contractType,
      isRhAuthorized
    } = this.props;
    // 点击勾选，判断是否需要拉起强制协议阅读弹窗
    handleCheckboxClick(val);
    // 已勾选
    if (checked) {
      onChangeContractStatus(false, contractType);
      this.setState({
        checked: false
      });
      return;
    }
    // 如果强制协议弹窗已同意，则把值设为true
    if (forceReadContractChecked) {
      onChangeContractStatus(true, contractType);
      if (!isRhAuthorized) { // 如果是信用智能评估页 勾选了协议后又取消了人行征信授权，则不需要改变状态
        return;
      }
      this.setState({
        checked: true
      });
    }
  }

  render() {
    const {
      beaconId,
      onContractClick,
      contracts,
      contractText,
      beforeContractText,
      outerControl,
      contractNowrap
    } = this.props;


    const {
      checked,
    } = this.state;

    return (
      <MUView className="apply-contract-checker">
        <MUContractChecker
          contractNowrap={contractNowrap}
          outerControl={outerControl}
          beaconId={`${beaconId}Checker`}
          beforeContractText={beforeContractText}
          contractText={contractText}
          contracts={contracts}
          checkboxValue={checked}
          onCheckboxClick={this.onCheckboxClick}
          onContractClick={onContractClick}
          className="contract-sheet"
        />
      </MUView>
    );
  }
}

ContractChecker.propTypes = propTypes;
ContractChecker.defaultProps = defaultProps;

export default ContractChecker;
