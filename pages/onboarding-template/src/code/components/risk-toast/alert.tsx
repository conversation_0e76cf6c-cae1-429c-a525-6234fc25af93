import classNames from 'classnames';
import {
  MUModal, MURichText,
  MUButton, MUView,
  MUImage,
} from '@mu/zui';
import Madp, { Component } from '@mu/madp';

const imgMap = {
  riskTipIcon: 'https://file.mucfc.com/abf/1/0/202411/20241127104810e6677f.png',
  safeIcon: 'https://file.mucfc.com/abf/1/0/202411/20241127104810da857a.png',
  tipItem: 'https://file.mucfc.com/abf/1/0/202411/20241127104810aef311.png',
  customIcon: 'https://file.mucfc.com/cop/51/52/202505/2025050811550523fc94.png'
};

export default class Alert extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShow: false, // 用于弹窗初始化渲染再显示，确保埋点及handleTouchScroll执行无误
      isOpened: false,
      disabledBtn: false,
      count: 0
    };
    this.properties = {
      title: '温馨提示',
      subTitle: '',
      content: '',
      confirmText: '确定',
      cancelText: '',
      type: 'default',
      closeOnClickOverlay: false,
      onCancel: () => {},
      onConfirm: () => {},
      beaconId: 'Alert',
      isForceRead: false,
      totalCount: 3 // 倒计时时长
    };
    this.propertiesCopy = { ...this.properties };
    this.linkUrl = '';
    this.tips = '';
  }

  componentWillUnmount() {
    this.clearTimer();
  }

  config = {
    styleIsolation: 'shared'
  }
  showAlert = (options) => {
    if (!options || this.isShowing()) return;
    let opt = {};
    if (typeof (options) === 'string') {
      opt = {
        content: options,
        onCancel: () => {},
        onConfirm: () => {},
      };
      this.linkUrl = '';
      this.tips = '';
    } else if (options.onClose && typeof (options.onClose) === 'function') {
      opt = {
        title: options.title,
        subTitle: options.subTitle,
        type: options.type,
        content: options.content,
        confirmText: options.confirmText,
        onCancel: () => {},
        onConfirm: options.onClose,
        beaconId: options.beaconId,
        beaconContent: options.beaconContent,
      };
      this.linkUrl = options.linkUrl || '';
      this.tips = options.tips || '';
    } else {
      opt = {
        title: options.title,
        subTitle: options.subTitle,
        content: options.content,
        type: options.type,
        confirmText: options.confirmText,
        cancelText: options.cancelText,
        onCancel: options.onCancel || (options.success && (() => options.success({ cancel: true }))) || (() => {}),
        onConfirm: options.onConfirm || (options.success && (() => options.success({ confirm: true }))) || (() => {}),
        beaconId: options.beaconId,
        beaconContent: options.beaconContent,
        isForceRead: options.isForceRead || false,
        totalCount: options.totalCount || 3
      };
      this.linkUrl = options.linkUrl || '';
      this.tips = options.tips || '';
    }
    setTimeout(() => { // hideLoading中的handleBeforeDestroy是放在宏任务中的，所以show也要，保持任务队列一致
      this.initAlert({ ...opt });
    }, 100);
  }

  initAlert = (opt) => {
    if (!opt) return;
    const option = {
      title: opt.title || '',
      subTitle: opt.subTitle || '',
      content: opt.content,
      type: opt.type || 'default',
      confirmText: opt.confirmText || '确定',
      cancelText: opt.cancelText,
      closeOnClickOverlay: false,
      beaconId: opt.beaconId || 'Alert',
      beaconContent: opt.beaconContent,
      onConfirm: async () => {
        this.setState(
          { isOpened: false },
          () => this.setState({ isShow: false }, opt.onConfirm)
        );
      },
      onCancel: async () => {
        this.setState(
          { isOpened: false },
          () => this.setState({ isShow: false }, opt.onCancel)
        );
      },
      isForceRead: opt.isForceRead || false,
      totalCount: opt.totalCount || 3
    };
    this.properties = { ...this.properties, ...option };
    this.propertiesCopy = { ...this.properties };
    if (option.isForceRead) {
      this.counting();
    }
    this.setState({ isShow: true });
    // 加延迟解决弹窗无法展示的问题
    setTimeout(() => this.setState({ isOpened: true }), 0);
  }

  counting = () => {
    const { totalCount } = this.propertiesCopy;
    if (+totalCount < 1) {
      this.setState({ disabledBtn: false });
      return;
    }
    this.clearTimer();
    this.setState({
      count: 0,
      disabledBtn: true
    }, () => {
      this.timer = setInterval(() => {
        const { count } = this.state;
        if (count + 1 === +totalCount) {
          this.clearTimer();
          this.setState({ disabledBtn: false });
        }
        this.setState({ count: count + 1 });
      }, 1000);
    });
  };

  clearTimer = () => {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  };

  hideAlert = () => {
    this.setState(
      { isOpened: false },
      () => this.setState({ isShow: false })
    );
  }

  isShowing = () => {
    const { isShow, isOpened } = this.state;
    return isShow && isOpened;
  }

  goLink = () => {
    if (this.linkUrl) {
      Madp.navigateTo({ url: this.linkUrl });
    }
  }

  render() {
    const { safeIcon, riskTipIcon, tipItem } = imgMap;
    const {
      isShow, disabledBtn, count
    } = this.state;
    const {
      content, type, confirmText, totalCount
    } = this.properties;
    const btnText = disabledBtn && +totalCount >= 1 ? `${confirmText}(${+totalCount - count}s)` : confirmText;
    const button1Class = classNames({
      'button-1': !!this.propertiesCopy.confirmText,
      single: Number(!!this.propertiesCopy.confirmText) + Number(!!this.propertiesCopy.cancelText) === 1
    });
    const button2Class = classNames({
      'button-2': !!this.propertiesCopy.cancelText,
      single: Number(!!this.propertiesCopy.confirmText) + Number(!!this.propertiesCopy.cancelText) === 1
    });
    const isTextTipsModal = type === 'text' && this.tips;
    const isLinkModal = !!this.linkUrl;
    if (isLinkModal || isTextTipsModal) {
      this.properties.title = '';
      this.properties.content = '';
    } else if (content && typeof content === 'string') {
      if (process.env.TARO_ENV === 'h5') {
        this.properties.content = (type === 'text')
          ? content
          : (<MURichText className="self-alert-content" nodes={content} />);
      } else {
        this.properties.content = (type === 'text') ? content : content.replace(/<[\s\S]*?\/?>/g, '');
      }
    }

    return (
      isShow ? (
        <MUModal className="login-alert" {...this.properties} isOpened>
          {(isLinkModal || isTextTipsModal) ? (
            <Block>
              <MUImage src={riskTipIcon} mode="scaleToFill" className="top-img" />
              {this.propertiesCopy.title && (
                <MUView className="mu-modal__header">
                  <MUView className="title">{this.propertiesCopy.title}</MUView>
                </MUView>
              )}
              <MUView className="mu-modal__content">
                <MUView className="content-text" style={isTextTipsModal ? { textAlign: 'justify' } : {}}>
                  <MUView className="sub-title">{this.propertiesCopy.subTitle}</MUView>
                  {isLinkModal && (
                    <MUView className="content-text__container">
                      {((this.propertiesCopy.content || '').split('#') || []).map((o, i) => (i % 2 === 0 ? o : <MUView style={{ color: '#3477FF', display: 'inline' }} beaconId={`${this.propertiesCopy.beaconId}.Link`} onClick={this.goLink}>{o}</MUView>))}
                    </MUView>
                  )}
                  {isTextTipsModal
                && (
                  <MUView className="content-text__container">
                    {(this.propertiesCopy.content || []).map((o, i) => (
                      <MUView key={i} className="content-item">
                        <MUImage src={tipItem} className="icon" mode="scaleToFill" />
                        <MUView className="text">{o}</MUView>
                      </MUView>
                    ))}
                  </MUView>
                )}
                </MUView>
              </MUView>
              <MUView className="at-modal__footer mu-modal__footer">
                <MUView className="at-modal__action">
                  {this.propertiesCopy.confirmText ? <MUButton className={button1Class} beaconId={`${this.propertiesCopy.beaconId}.ModalButtonConfirm`} onClick={this.propertiesCopy.onConfirm} disabled={disabledBtn}>{btnText}</MUButton> : null}
                  {this.propertiesCopy.cancelText ? <MUButton className={button2Class} beaconId={`${this.propertiesCopy.beaconId}.ModalButtonCancel`} onClick={this.propertiesCopy.onCancel}>{this.propertiesCopy.cancelText}</MUButton> : null}
                </MUView>
              </MUView>
              {isTextTipsModal && (
                <MUView className="bottom-tips">
                  <MUImage className="bottom-tips-img" src={safeIcon} mode="scaleToFill" />
                  <MUView className="bottom-tips-text">{this.tips}</MUView>
                </MUView>
              )}
            </Block>
          ) : null}
        </MUModal>
      ) : null
    );
  }
}
