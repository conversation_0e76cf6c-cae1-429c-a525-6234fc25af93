.login-alert{	
    .mu-modal__container {	
        background-image: url('https://file.mucfc.com/mdp/21/0/202409/202409230932417da69a.png');	
        background-size: cover;	
        width: 560px;	
        padding: 0;	
        margin: 0;	
    }	
    .top-img{	
      width: 312px;	
      height: 176px;	
      margin: 52px auto 15px;	
      display: flex;	
      align-items: 'center';	
    }	
    .mu-modal__header{	
      margin: 20px auto;	
    }	
    .sub-title{	
      font-size: 28px;	
      text-align: center;	
    }	
    .content-text__container {	
      display: flex;	
      flex-wrap: wrap;	
      justify-content: space-around;	
      align-items: center;
      background-image: url('https://file.mucfc.com/mdp/21/0/202409/202409230932417de4b3.png');	
      background-size: 100% 100%;	
      background-position: 'center';
      padding-top: 30px;	
      margin-top: 20px;	
      width: 500px;
      .content-item {	
        display: flex;	
        align-items: center;	
        margin-bottom: 8px; 	
        width: 240px;	
        
        .icon {	
          display: block;	
          width: 24px;	
          height: 24px;	
          margin-right: 4px;	
          margin-left: 10px;	
        }	
        
        .text {	
          font-size: 22px;	
          line-height: 42px;	
          flex: 1;	
          overflow-wrap: break-word;	
          white-space: normal; // 允许正常换行
          text-align: left;	
        }	
      }	
    }	
        
    .bottom-tips{	
        height: 60px;	
        padding: 9px;
        width: 560px;	
        background-color: rgba(52, 119, 255, 0.1);	
        box-sizing: border-box;	
        display: flex;	
        align-items: center;	
        justify-content: center;	
        &-img{	
          width: 32px;	
          height: 32px;	
          margin-right: 8px;	
        }	
        
        &-text{	
          font-size: 22px;	
          line-height: 30px;	
          color: #808080;	
          white-space: nowrap;	
        }	
      }	
  }