import { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import { EventTypes, track } from '@mu/madp-track';
import {
  MUView, MUModal
} from '@mu/zui';
// @ts-ignore
import { IdArea } from '@mu/login-popup';
import { getCurrentPageUrlWithArgs, Url } from '@mu/madp-utils';
// @ts-ignore
import { urlDomain } from '@mu/business-basic';
import H5Login from './h5-login/index';
import { sendTrackBeacon, isWeapp, isH5, isTT } from '../../utils';
import { getNewPhoneOwnerUrl } from '../../utils/constants';
import loginStore from './store';
import RiskToast from '../risk-toast';
import WeappLogin from './weapp-login';
import TTLogin from './tt-login';

const riskToastContent = {
  title: '防诈骗提醒',
  subTitle: '招联官方提醒，请防范以下诈骗套路',
  content: [
    '非应用市场下载APP',
    '假冒招联客服收费',
    '假冒公检法查案',
    '刷单赚佣金',
    '隐私泄漏',
    '投资理财'
  ],
  tip: '招联全力保护您的信息安全',
  btnText: '我已知晓以上诈骗行为'
};
const currentPageUrl = isH5 ? getCurrentPageUrlWithArgs() : `/${getCurrentPageUrlWithArgs()}`;

interface State {
  idShow: boolean;
  openFaceDialog: boolean;
  maskRealName: string;
}

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContentz
  uiType: 'CommonLoginComp', // 就是当前组件类名
}))
@observer
class CommonLoginComp extends Component<any, State> {
  riskToast: any;

  constructor(props: any) {
    super(props);
    this.state = {
      idShow: false,
      openFaceDialog: false,
      maskRealName: '',
    };
  }

  handleSendTrackBeacon = (beaconIdTail = '', extraBeaconContentCus = {}) => {
    const { pageId } = this.props.track;
    const { beaconContentCus, securityLevel } = this.props;
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: beaconIdTail,
      beaconContent: {
        cus: {
          securityLevel,
          ...extraBeaconContentCus,
          ...beaconContentCus
        }
      }
    });
  }

  // 防诈骗弹窗处理
  handlerRiskTip = (riskTip, tipLevel) => new Promise(async (resolve) => {
    if (riskTip === 'RCSFB110' && tipLevel) {
      this.handleSendTrackBeacon('RiskTip');
      const {
        title,
        subTitle,
        content,
        tip,
        btnText
      } = riskToastContent;
      this.riskToast.showModal({
        title,
        subTitle,
        content,
        showCancel: false,
        confirmText: btnText,
        confirmColor: '#3477ff',
        success: resolve,
        fail: resolve,
        type: 'text',
        tips: tip,
        isForceRead: tipLevel ? tipLevel === 'T1' : false, // 是否强读
        totalCount: 3 // 强读时长
      });
    } else {
      // @ts-ignore
      resolve();
    }
  })

  handleLoginResult = async (result: any) => {
    const { securityLevel, onSuccess, extraLoginData, mobileNum, needHideFailToast } = this.props;
    const {
      needJumpFace,
      needVerify,
      loginSuccess,
      riskTip,
      tipLevel,
      loginProcessType,
      maskRealName
    } = result || {};
    const { onePassLogin } = loginStore.loginModel;
    if (securityLevel === 'special-low-medium' && loginProcessType === 'APPEAL_MOBILE') {
      // 特殊的中低安全等级（模版二小程序）需要根据登录接口的返回值手动触发三要素处理
      // 身份证和姓名在登录时也要传，所以直接从extraLoginData中解构出来
      // 因为模版二小程序中手机号不允许修改，所以可以直接使用props传过来的电话
      this.handleSendTrackBeacon('AppealMobile');
      const { inputIdNo: certId, inputName: custName } = extraLoginData || {};
      const resetPhoneUrl = `${getNewPhoneOwnerUrl()}?mobile=${mobileNum}&isUnionLogin=1&realName=${custName}&idNo=${certId}&sendEventOnExit=1`;
      // storeUrlBeforeToExternalSys();
      Madp.navigateTo({
        url: resetPhoneUrl,
      });
    } else if (needJumpFace) {
      this.setState({ openFaceDialog: true });
    } else if (needVerify) {
      this.handleSendTrackBeacon('IdCardVerify');
      this.setState({ idShow: true, maskRealName });
    } else if (loginSuccess) {
      if (isWeapp) {
        this.handleSendTrackBeacon('LoginSuccess');
      } else {
        const beaconId = onePassLogin ? 'OnepassSuccess' : 'SmsLoginSuccess';
        this.handleSendTrackBeacon(beaconId);
      }
      await this.handlerRiskTip(riskTip, tipLevel);
      if (typeof onSuccess === 'function') {
        onSuccess();
      }
    } else {
      if (isWeapp) {
        this.handleSendTrackBeacon('LoginFail', { failReason: 'loginApiFail' });
      } else {
        const beaconId = onePassLogin ? 'OnepassFail' : 'SmsLoginFail';
        this.handleSendTrackBeacon(beaconId, { failReason: 'loginApiFail' });
      }
      if (!needHideFailToast) {
        Madp.showToast({
          title: '登录失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }

  // 身份证号认证成功回调
  idSuccess = async (data) => {
    loginStore.set('idShow', false);
    const { onSuccess } = this.props;
    const { onePassLogin } = loginStore.loginModel;
    this.handleSendTrackBeacon('IdCardVerifySuccess');
    if (isWeapp) {
      this.handleSendTrackBeacon('LoginSuccess');
    } else {
      const beaconId = onePassLogin ? 'OnepassSuccess' : 'SmsLoginSuccess';
      this.handleSendTrackBeacon(beaconId);
    }
    await this.handlerRiskTip(data.riskTip, data.tipLevel);
    if (typeof onSuccess === 'function') {
      onSuccess();
    }
  };

  idFail = () => {
    const { onePassLogin } = loginStore.loginModel;
    if (isWeapp) {
      this.handleSendTrackBeacon('LoginFail', { failReason: 'idFail' });
    } else {
      const beaconId = onePassLogin ? 'OnepassFail' : 'SmsLoginFail';
      this.handleSendTrackBeacon(beaconId, { failReason: 'idFail' });
    }
    loginStore.set('idShow', false);
  }

  /** 刷脸登录 */
  goFaceCheck = () => {
    this.handleSendTrackBeacon('GoFaceCheck');
    loginStore.set('openFaceDialog', false);
    // faceVerifySuccess参数用来标识是否从活体验证成功后回来的，如果是，则直接建案分发
    const flag = {
      faceVerifySuccess: true
    };
    const redirectUrl = Url.addParam(currentPageUrl, flag);
    let url = '';
    if (isH5) {
      // 跳转外部模块前调用，避免重复执行beforeRouteLeave中的内容
      // storeUrlBeforeToExternalSys();
      // H5跳转时要使用完整路径
      url = `${urlDomain}/${Madp.getChannel()}/loginregister/#/pages/UnionLogin/faceCheck?redirectUrl=${encodeURIComponent(redirectUrl)}&gobackUrl=${encodeURIComponent(currentPageUrl)}`;
    } else {
      // 小程序跳转时要使用原生页面的跳转，否则因为前后sessionId不一致导致报错
      url = `/loginregister/pages/UnionLogin/faceCheck?redirectUrl=${encodeURIComponent(redirectUrl)}&gobackUrl=${encodeURIComponent(currentPageUrl)}`;
    }
    Madp.navigateTo({ url });
  }

  render() {
    const { securityLevel, h5LoginViewMode, appealFailedText } = this.props;
    const { idShow, maskRealName, openFaceDialog } = loginStore;
    
    return (
      <MUView className={securityLevel === 'normal-low-medium' && h5LoginViewMode === 'embedded' && 'login-comp'}>
        {securityLevel === 'normal-low-medium' && (
          <MUView>
            {isH5 && <H5Login {...this.props} handleLoginResult={this.handleLoginResult} handleSendTrackBeacon={this.handleSendTrackBeacon}>
              {this.props.children}
            </H5Login>}
            {isWeapp && <WeappLogin {...this.props} handleLoginResult={this.handleLoginResult}>
              {this.props.children}
            </WeappLogin>}
            {isTT && <TTLogin {...this.props} handleLoginResult={this.handleLoginResult}>
              {this.props.children}
            </TTLogin>}
          </MUView>
        )}

        {/* 身份证后四位校验组件 */}
        <IdArea
          isOpen={idShow}
          maskRealName={maskRealName}
          idAmount={4}
          onSuccess={this.idSuccess}
          onClose={this.idFail}
          needJumpRedirectUrlAfterAppeal
          needShowAgeementOnAppealSuccess={false}
          appealFailText={appealFailedText}
          appealResultRedirectUrl={currentPageUrl}
        />

        {/* 活体验证弹窗 */}
        <MUModal
          isOpened={openFaceDialog}
          title="温馨提示"
          content="为确保账户安全，请完成活体验证"
          confirmText="去验证"
          cancelText="暂不验证"
          onConfirm={this.goFaceCheck}
          onCancel={() => loginStore.set('openFaceDialog', false)}
        />

        {/* 防诈骗弹窗组件 */}
        <RiskToast ref={(ref) => { this.riskToast = ref; }} />
      </MUView>
    );
  }
}

export default CommonLoginComp; 