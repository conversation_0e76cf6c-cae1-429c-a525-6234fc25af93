import { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import {
  MUView, MUInput, MUButton, MUIcon, MUDialog
} from '@mu/zui';
import { debounce } from '@mu/madp-utils';
import SMSVerify from '../sms-verify/index';
import onePassPlugin from '../one-pass-plugin/index';
import loginStore from '../store';
import { getMaskMobileNum, checkPhoneNum } from '../../../utils';

interface State {
  authCode: string;
}

@observer
class H5Login extends Component<any, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      authCode: '',
    };
  }

  componentDidMount() {
    // 本机校验初始化
    const { init } = onePassPlugin;
    init((res) => {
      if (res.code === '1') {
        loginStore.updateLoginProperty('onePassLogin', true);
      } else {
        console.log('本机校验初始化失败', res.code, res.msg);
      }
    });
  }

  /**
   * 点击登录按钮
   */
  handleLoginButtonClick = () => {
    const { disabled, handleBeforeLoginCheck, handleSendTrackBeacon } = this.props;
    const { loginMobileNum, onePassLogin } = loginStore.loginModel;
    const isMobileNum = checkPhoneNum(loginMobileNum);
    // 上送点击埋点
    if (typeof handleSendTrackBeacon === 'function') {
      handleSendTrackBeacon('LoginClick');
    }
    // 登录前校验
    if (typeof handleBeforeLoginCheck === 'function') {
      const flag = handleBeforeLoginCheck();
      if (!flag) return;
    }
    if (disabled || !isMobileNum) {
      return;
    }
    if (onePassLogin) {
      this.handleOnePassLogin();
    } else {
      this.handleSmsLogin();
    }
  };

  /**
   * 一键登录处理
   */
  handleOnePassLogin = debounce(() => {
    const { loginMobileNum } = loginStore.loginModel;
    const { handleSendTrackBeacon } = this.props;
    // 上送开始一键登录埋点
    if (typeof handleSendTrackBeacon === 'function') {
      handleSendTrackBeacon('OnepassStart');
    }
    if (checkPhoneNum(loginMobileNum)) {
      loginStore.doStartOnePass(this.props);
    } else {
      return;
    }
  }, 1000, { leading: true, trailing: false });

  handleSmsLogin = () => {
    console.log('动码登录');
  }

  renderCommonContent = () => {
    const { onePassLogin, loginMobileNum } = loginStore.loginModel;
    const { disabled, isMask, mainButtonText, mainButtonBubbleText } = this.props;
    const isMobileNum = checkPhoneNum(loginMobileNum);
    return (
      <MUView>
        <MUView className="login-input-content">
          {/* 一键登录模式：只显示手机号输入框 */}
          {onePassLogin && (
            <MUView className="login-input-view">
              <MUIcon value="mobile" size="16" className="innerImage" ariaLabel="手机号图标" />
              <MUInput
                className="innerInput"
                maxLength={11}
                name="mobileNum"
                type="text"
                value={isMask ? getMaskMobileNum(loginMobileNum) : loginMobileNum}
                placeholder="请输入您的手机号"
                onChange={(val) => loginStore.updateLoginProperty('loginMobileNum', val)}
                clear
                border={false}
              />
            </MUView>
          )}

          {/* 动码登录模式：显示SMSVerify组件 */}
          {!onePassLogin && (
            <SMSVerify {...this.props} />
          )}
        </MUView>

        {/* children内容 */}
        {this.props.children}

        {/* 登录按钮区域 */}
        <MUView className="login-btn-view">
          {
            mainButtonBubbleText && (
              <MUView className="login-btn-view__bubble">{mainButtonBubbleText}</MUView>
            )
          }
          <MUButton className="login-button-btn brand-bg" disabled={disabled || !isMobileNum} onClick={this.handleLoginButtonClick}>
            {mainButtonText}
          </MUButton>
        </MUView>
      </MUView>
    )
  }

  renderBottomContent = () => {
    const { isOpenLoginDialog } = loginStore;
    const { onePassLogin, loginMobileNum } = loginStore.loginModel;
    const { disabled, isMask, mainButtonText } = this.props;
    const isMobileNum = checkPhoneNum(loginMobileNum);
    return (
      <MUDialog className="loginAreaView-dialog" isOpened={isOpenLoginDialog}>
        <MUIcon value="close2" size="16" color="#A6A6A6" className="close-btn" ariaLabel="关闭" onClick={() => loginStore.set('isOpenLoginDialog', false)} />
        <MUView className="margin-left-right">
          <MUView className="login-input-content">
            {
            onePassLogin && (
              <MUView className="login-input-view " >
                <MUIcon value="mobile" size="16" className="innerImage" ariaLabel="手机号图标" />
                <MUInput
                  className="innerInput"
                  maxLength={11}
                  name="mobileNum"
                  type="text"
                  value={isMask ? getMaskMobileNum(loginMobileNum) : loginMobileNum}
                  placeholder="请输入您的手机号"
                  editable={!isMask}
                  onChange={(val) => loginStore.updateLoginProperty('loginMobileNum', val)}
                  clear
                  border={false}
                />
              </MUView>)
            }
            {!onePassLogin && <SMSVerify {...this.props} />}
          </MUView>
          {this.props.children}
          <MUButton className="login-button-btn brand-bg" disabled={disabled || !isMobileNum} onClick={this.handleLoginButtonClick}>
            {mainButtonText}
          </MUButton>
        </MUView>
      </MUDialog>
    )
  }

  render() {
    const { h5LoginViewMode = 'embedded' } = this.props;
    const bottomDialog = h5LoginViewMode === 'bottom-popup';
    return (
      <MUView className="login-view">
        {bottomDialog && this.renderBottomContent()}
        {!bottomDialog && this.renderCommonContent()}
      </MUView>
    );
  }
}

export default H5Login; 