import { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import {
  MUView, MUIcon, MUInput
} from '@mu/zui';
import loginStore from '../store';
import { getMaskMobileNum, checkPhoneNum } from '../../../utils';
import GeetestPlugin from '../geetest-plugin';

interface State {
  authCodeTitle: string;
  timerCount: number;
  authButonDisabled: boolean;
  authCodeInput: string;
}

@observer
class SMSVerify extends Component<any, State> {
  constructor(props: any) {
    super(props);
    this.state = {
      authCodeTitle: '获取验证码',
      timerCount: 60,
      authButonDisabled: false,
      authCodeInput: ''
    };
  }

  /**
   * 动码输入内容变化
   * @param text 输入内容
   */
  handleCodeChange = (text: string) => {
    loginStore.set('authCodeInput', text);
  }

  /**
   * 验证码发送前检查
   */
  authCodeAction = () => {
    const { authButonDisabled, loginModel } = loginStore;
    const { loginMobileNum } = loginModel;
    if (authButonDisabled) {
      return;
    }
    if (!checkPhoneNum(loginMobileNum, true)) {
      return;
    }
    loginStore.sendSmsCodeFore();
  }

  render() {
    const { isMask } = this.props;
    const { authCodeInput, authButonDisabled, authCodeTitle, loginModel } = loginStore;
    const { loginMobileNum } = loginModel;
    
    return (
      <MUView className="sms-login">
        {/* 手机号输入框 */}
        <MUView className="login-input-view midMargin">
          <MUIcon value="mobile" size="16" className="innerImage" ariaLabel="手机号图标" />
          <MUInput
            className="innerInput"
            maxLength={11}
            name="mobileNum"
            type="phone"
            value={isMask ? getMaskMobileNum(loginMobileNum) : loginMobileNum}
            placeholder="请输入您的手机号"
            onChange={(val) => loginStore.updateLoginProperty('loginMobileNum', val)}
            clear
            border={false}
          />
        </MUView>

        {/* 验证码输入框 */}
        <MUView className="login-input-view midMargin">
          <MUIcon value="captcha" size="16" className="innerImage" ariaLabel="验证码图标" />
          <MUView className="innerInput">
            <MUInput
              className="innerInput"
              maxLength={6}
              name="authCode"
              type="number"
              placeholder="请输入验证码"
              value={authCodeInput}
              onChange={(text: string) => this.handleCodeChange(text)}
              border={false}
            />
          </MUView>
          <MUView
            className={
              authButonDisabled ? 'auth-code-input-auth-area auth-code-input-counting brand-text'
                : 'auth-code-input-auth-area auth-code-input-normal brand-text'
            }
            onClick={this.authCodeAction}
          >
            {authCodeTitle}
          </MUView>
        </MUView>

        <GeetestPlugin
          ref={ref => loginStore.set('geetestPlugin', ref)}
          scene="SCENE_LOGIN"
          onSuccess={(result) => loginStore.sendSmsCodeAction(result.token)}
          onError={(error) => {
            loginStore.set('allowGetCode', true);
            console.log('Geetest error:', error);
          }}
        />
      </MUView>
    );
  }
}

export default SMSVerify;
