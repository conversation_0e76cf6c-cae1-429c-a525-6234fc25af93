/**
 * 动码登录组件（本机校验流程失败后会走到这里）
 */
import { Component, } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import { Button } from '@tarojs/components';
import { MUView } from '@mu/zui';
import classNames from 'classnames';
import loginStore from '../store';

interface State {
}

@observer
class WeappLogin extends Component<any, State> {
  componentDidMount() {
    this.initMiniProgram();
  }

  componentDidUpdate(prevProps) {
    // props中控制初始化的变量有变化，则再次进行小程序初始化判断
    if ((this.props.processType !== prevProps.processType) || (this.props.needInitInMiniProgram !== prevProps.needInitInMiniProgram)) {
      this.initMiniProgram();
    }
  }

  /**
   * 需要做初始化前置判断：
   * 1、props传入的needInitInMiniProgram字段判断是否要初始化
   * 2、this._hasInitInMiniProgram判断是否已经初始化过
   * 3、processType判断是否是走动码流程
   */
  initMiniProgram = async () => {
    const { processType, needInitInMiniProgram } = this.props;
    const { hasInitInMiniProgram } = loginStore;
    // 走动码流程，且未进行过小程序初始化则进行初始化
    if (processType === 'SMS_LOGIN' && needInitInMiniProgram && !hasInitInMiniProgram) {
      await loginStore.handleWeappUnionBindAuth(false, this.props);
      // 初始化完成后更新字段
      loginStore.set('hasInitInMiniProgram', true);
    }
  }

  // 按钮回调
  onGetrealtimephonenumber =async (data: any) => {
    loginStore.onGetrealtimephonenumber(data, this.props);
  }

  handleLoginClick = async () => {
    loginStore.handleWeappLoginClick(this.props);
  }


  render() {
    // disabled外部传入，判断按钮是否可以点击，对于前置校验比如协议勾选，外部在按钮外再包一层view，在其上定义点击事件进行校验
    // loginModel：判断走动码还是一键登录，默认是走动码
    // 在微信小程序中，如果对按钮设置disabled，样式会受到平台的影响，与预期有差异，因此需要手动模拟disabled的情况
    const { disabled, mainButtonText, mainButtonBubbleText, processType } = this.props;
    return (
      <MUView className="login-view">
        {this.props.children}
        <MUView className="login-btn-view">
          {
            mainButtonBubbleText && (
              <MUView className="login-btn-view__bubble">{mainButtonBubbleText}</MUView>
            )
          }
          <Button
            className={classNames('login-button-btn brand-bg', {
              'at-button--disabled': disabled,
            })}
            open-type={(disabled || processType !== 'SMS_LOGIN') ? '' : 'getRealtimePhoneNumber'}
            onGetrealtimephonenumber={this.onGetrealtimephonenumber}
            onClick={this.handleLoginClick}
          >
            {mainButtonText}
          </Button>
        </MUView>
      </MUView>
    );
  }
}

export default WeappLogin;


