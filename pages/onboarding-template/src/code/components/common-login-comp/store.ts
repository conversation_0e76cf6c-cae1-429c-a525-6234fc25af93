// 登录组件store（控制层）
import { observable, action } from 'mobx';
import { LoginService } from '../../domain/services/login-service';
import { getLoginInstance } from '../../domain/models/login/login';
import onePassPlugin from './one-pass-plugin/index';
import { Url } from '@mu/madp-utils';
import { sendTrackBeacon } from '../../utils';
import { EventTypes } from '@mu/madp-track';

class LoginStore {
  /**
   * 登录领域模型
   */
  @observable loginModel = getLoginInstance();

  @observable isOpenLoginDialog: boolean = false;

  /** 动码输入内容 */
  @observable authCodeInput: string = '';

  /** 动码token */
  @observable authCodeToken: string = '';

  /** 动码按钮标题 */
  @observable authCodeTitle: string = '获取验证码';

  /** 动码按钮是否禁用 */
  @observable authButonDisabled: boolean = false;

  /** 是否允许获取动码，避免用户重复获取 */
  @observable allowGetCode: boolean = true;

  /** 动码倒计时时间 */
  @observable timerCount: number = 60;

  /** 动码倒计时定时器 */
  @observable interval: any = null;

  /** 极验插件ref */
  @observable geetestPlugin: any = null;

  /** 展示身份证后四位校验组件 */
  @observable idShow: boolean = false;

  /** 身份证后四位校验组件的掩码姓名 */
  @observable maskRealName: string = '';

  /** 展示活体验证弹窗 */
  @observable openFaceDialog: boolean = false;

  /** 是否已经初始化过小程序 */
  @observable hasInitInMiniProgram: boolean = false;

  @action.bound
  updateLoginProperty = (key: string, value: any) => {
    LoginService.updateLoginProperty(key, value);
  }

  /**
   * 一键登录处理
   */
  @action.bound
  doStartOnePass = (props: any) => {
    const { startOnePass } = onePassPlugin;
    const { loginMobileNum } = this.loginModel;
    const { handleSendTrackBeacon } = props;
    startOnePass({
      phoneNum: loginMobileNum,
      scene: 'SCENE_LOGIN',
      onError: ({ errCode, errMsg }) => {
        console.log('本机校验失败原因:', errCode, errMsg);
        // 上送一键登录失败埋点
        if (typeof handleSendTrackBeacon === 'function') {
          handleSendTrackBeacon('OnepassFail');
        }
        LoginService.updateLoginProperty('onePassLogin', false);
        LoginService.updateLoginProperty('needAutoSendCode', true);
      },
      onOk: async (token) => {
        // 本机器校验成功，用token来做登录
        const params = {
          loginType: 'LOCALVERI',
          token,
        };
        await this.handleUnionLogin(params, props);
        console.log('本机校验:', token);
      }
    });
  }

  /**
   * 处理联合登录
   * @param data 登录参数
   * @param props 组件props
   */
  @action.bound
  handleUnionLogin = async (data: any, props: any) => {
    const { extraLoginData, handleSendTrackBeacon } = props;
    const { loginMobileNum, onePassLogin } = this.loginModel;
    const params = {
      mobileNo: loginMobileNum,
      verifyScene: true,
      faceScene: true,
      needBind: true,
      ...data,
      ...extraLoginData
    };
    const res = await LoginService.doUnionLogin(params);
    // 登录接口成功返回内容
    if (res) {
      // 是底部弹窗的模式则关闭弹窗
      const { h5LoginViewMode } = props;
      if (h5LoginViewMode === 'bottom-popup') {
        this.set('isOpenLoginDialog', false);
      }
    } else {
      const beaconId = onePassLogin ? 'OnepassFail' : 'SmsLoginFail';
      // 上送开始一键登录埋点
      if (typeof handleSendTrackBeacon === 'function') {
        handleSendTrackBeacon(beaconId, { failReason: 'loginApiFail' });
      }
      Madp.showToast({ title: '登录失败，请重试', icon: 'none', duration: 2000 });
    }
    // 处理登录接口调用结果
    const { handleLoginResult } = props;
    handleLoginResult(res);
  }

  /**
  * 验证码发送：分为初始化和正式发送两步，调用的是同一个接口
  * 初始化：返回结果分为两种情况：
  *  - 如果有1分钟内未验动码会返回对应的authCodeToken以及剩余有效时间，直接返回，不必再继续
  *  - 如果没有1分钟内未验动码依旧可以调用接口成功，只是authCodeToken为空，需要继续进行第二步
  * 正式发送：
  */
  @action.bound
  sendSmsCodeFore = async () => {
    if (!this.allowGetCode) {
      return;
    }
    this.allowGetCode = false;

    const { loginMobileNum } = this.loginModel;
    // 初始化
    try {
      const initParams = {
        firstScene: 'SCENE_INIT_ONE_MINUTE',
        tokenScene: 'SCENE_LOGIN',
        verifyType: 'SMP',
        mobile: loginMobileNum
      };
      const data = await LoginService.sendSmsCode(initParams);
      const { intervalSecs, smsToken } = data || {};
      // 有一分钟内未验动码，可以直接返回，不必再继续
      if (smsToken) {
        this.set('authCodeToken', smsToken);
        this.set('allowGetCode', true);
        this.startCountAction(intervalSecs);
        return;
      }
    } catch (err) {
      console.log('动码初始化失败:', err);
    }
    
    // 正式发送
    try {
      // 使用统一封装的极验验证组件，调用其内部的函数进行极验
      if (this.geetestPlugin) {
        this.geetestPlugin.startGeetest();
      }
    } catch (e) {
      this.set('allowGetCode', true);
    }
  }

  /**
   * 动码倒计时
   * @param intervalSecs 动码倒计时时间
   */
  @action.bound
  startCountAction = (intervalSecs?: number) => {
    if (this.authButonDisabled) {
      return;
    }
    const codeTime = intervalSecs || this.timerCount;
    const now = Date.now();
    const overTimeStamp = now + (codeTime * 1000) + 100;

    /* 过期时间戳（毫秒） +100 毫秒容错 */
    this.interval = setInterval(() => {
      const nowStamp = Date.now();
      if (nowStamp >= overTimeStamp) {
        /* 倒计时结束，清除定时器 */
        clearInterval(this.interval);
        this.interval = null;
        this.set('timerCount', codeTime);
        this.set('authCodeTitle', '重新获取');
        this.set('authButonDisabled', false);
      } else {
        // @ts-ignore
        const leftTime = parseInt((overTimeStamp - nowStamp) / 1000, 10);
        this.set('timerCount', leftTime);
        this.set('authCodeTitle', `重新获取 ${leftTime.toString()} `);
        this.set('authButonDisabled', true);
      }
    }, 1000);
  }

  /**
   * 正式发送动码
   */
  @action.bound
  sendSmsCodeAction = async (token: string) => {
    const { loginMobileNum } = this.loginModel;
    const params = {
      mobile: loginMobileNum,
      firstScene: 'COMMON_MASKVALUE_SMS_ONE_MINUTE',
      tokenScene: 'SCENE_LOGIN',
      verifyType: 'SMP',
      geetestToken: token
    };
    try {
      const data = await LoginService.sendSmsCode(params);
      const {
        smsToken
      } = data || {};
      if (smsToken) {
        this.set('authCodeToken', smsToken);
        this.set('allowGetCode', true);
        this.startCountAction();
      } else {
        this.set('allowGetCode', true);
        Madp.showToast({
          title: '验证码发送失败',
          icon: 'none',
          duration: 2000
        });
      }
    } catch (e) {
      this.set('allowGetCode', true);
      Madp.showToast({
        title: '验证码发送失败',
        icon: 'none',
        duration: 2000
      });
    }
  }

  /**
   * 处理微信小程序联合绑定
   * @param needHandleResult 是否需要处理返回值
   * @param props 组件props
   */
  @action.bound
  handleWeappUnionBindAuth = async (needHandleResult: boolean, props: any) => {
    // extraLoginData中需要传入商户号、协议信息
    const { extraLoginData, handleLoginResult, processType } = props;
    const params = {
      useWxMp: 'Y',
      openId: Url.getParam('openId') || '',
      token: Url.getParam('token') || '',
      needBindUser: processType === 'ONE_CLICK_UNION' || processType === 'ONE_CLICK_LOGIN',
      ...extraLoginData
    };
    const res = await LoginService.getUnionBindAuth(params);
    // 不需要对返回值做处理，直接返回
    if (!needHandleResult) {
      return;
    }
    handleLoginResult(res);
    return res;
  }

  /**
   * 微信小程序登录点击处理
   * @param props 组件props
   */
  @action.bound
  handleWeappLoginClick = async (props: any) => {
    const { disabled, processType, securityLevel, beaconContentCus, handleBeforeLoginCheck, track } = props;
    const { pageId } = track || {};
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: 'LoginClick',
      beaconContent: {
        cus: {
          disabled,
          securityLevel,
          ...beaconContentCus
        }
      }
    });
    // 登录前校验
    if (typeof handleBeforeLoginCheck === 'function') {
      const flag = handleBeforeLoginCheck();
      if (!flag) return;
    }
    if (processType === 'SMS_LOGIN' || disabled) {
      return;
    }
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: 'StartLogin',
      beaconContent: {
        cus: {
          disabled,
          processType,
          securityLevel,
          ...beaconContentCus
        }
      }
    });
    // 不走动码流程，且允许点击，走一键登录流程
    this.handleWeappUnionBindAuth(true, props);
  }

  /**
   * 微信小程序获取手机号回调处理
   * @param data 回调数据
   * @param props 组件props
   */
  @action.bound
  onGetrealtimephonenumber =async (data: any, props: any) => {
    const { disabled, processType, securityLevel, beaconContentCus, track } = props;
    const { pageId } = track || {};
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: 'StartLogin',
      beaconContent: {
        cus: {
          disabled,
          processType,
          securityLevel,
          ...beaconContentCus
        }
      }
    });
    const { detail: dataObj } = data || {};
    const { code, errMsg } = dataObj || {};
    // 未成功、未获取到code，调用传递过来的失败的回调函数，返回
    if (errMsg !== 'getPhoneNumber:ok' || !code) {
      // 失败
      return;
    }
    // 成功，用code做登录,此时调用unionLogin接口
    const params = {
      mobileNo: '',
      token: code,
      useWxMp: 'Y',
      verifyScene: true,
      faceScene: true,
      loginType: 'SMS',
      verifyType: 'WECHAT_VERIFY',
    };
    const res = await LoginService.doUnionLogin(params);
    const { handleLoginResult } = props;
    handleLoginResult(res);
  }

  /**
   * 处理抖音小程序联合绑定
   */
  @action.bound
  handleTTUnionBindAuth = async () => {
    const params = {
      useWxMp: 'N',
      contractTypeList: ['REGIST', 'PRIVACY'],
    };
    await LoginService.getUnionBindAuth(params);
    this.set('hasInitInMiniProgram', true);
  }

  /**
   * 抖音小程序登录点击处理
   * @param props 组件props
   */
  @action.bound
  handleTTLoginClick = async (props: any) => {
    const { securityLevel, beaconContentCus, disabled, handleBeforeLoginCheck, track } = props;
    const { onePassLogin, loginMobileNum } = this.loginModel;
    const { pageId } = track || {};
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: 'LoginClick',
      beaconContent: {
        cus: {
          securityLevel,
          onePassLogin,
          ...beaconContentCus
        }
      }
    });
    // 登录前校验
    if (typeof handleBeforeLoginCheck === 'function') {
      const flag = handleBeforeLoginCheck();
      if (!flag) return;
    }
    if (onePassLogin || disabled) return;
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: 'StartLogin',
      beaconContent: {
        cus: {
          securityLevel,
          onePassLogin,
          ...beaconContentCus
        }
      }
    });
    // 非一键登录，走动码流程
    const token = await LoginService.verifySmsCode(this.authCodeInput, this.authCodeToken);
    if (token) {
      const params = {
        mobileNo: loginMobileNum,
        verifyScene: true,
        faceScene: true,
        loginType: 'SMS',
        token,
      };
      const res = await LoginService.doUnionLogin(params);
      const { handleLoginResult } = props;
      handleLoginResult(res);
    } else {
      Madp.showToast({ title: '手机验证码验证失败，请重新获取', icon: 'none', duration: 2000 });
    }
  }

  /**
   * 抖音小程序获取手机号回调处理
   * @param e 回调数据
   * @param props 组件props
   */
  @action.bound
  handleTTGetPhoneNumber = async (e: any, props: any) => {
    const { securityLevel, beaconContentCus, track } = props;
    const { onePassLogin } = this.loginModel;
    const { pageId } = track || {};
    sendTrackBeacon(pageId, {
      event: EventTypes.EV,
      beaconId: 'StartLogin',
      beaconContent: {
        cus: {
          securityLevel,
          onePassLogin,
          ...beaconContentCus
        }
      }
    });

    const { startOnePass } = onePassPlugin;
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const { encryptedData, iv } = e.detail;
      const data = await LoginService.getOauthAccounts({ encryptedData, iv, operType: 'TTSMALL' });
      if (data && data.mobile) {
        LoginService.updateLoginProperty('loginMobileNum', data.mobile);
        if (onePassLogin) {
          startOnePass({
            scene: 'SCENE_LOGIN',
            phoneNum: '',
            onError: ({ errCode, errMsg }) => {
              LoginService.updateLoginProperty('onePassLogin', false);
              LoginService.updateLoginProperty('needAutoSendCode', true);
              console.log('本机校验失败原因:', errCode, errMsg);
            },
            onOk: async (token) => {
              // 本机器校验成功，用token来做登录
              const params = {
                mobileNo: data.mobile,
                verifyScene: true,
                faceScene: true,
                loginType: 'LOCALVERI',
                token,
              };
              await LoginService.doUnionLogin(params);
              console.log('本机校验:', token);
            }
          });
        } else {
          LoginService.updateLoginProperty('onePassLogin', false);
        }
      }
      LoginService.updateLoginProperty('onePassLogin', false);
    }
  }

  @action.bound
  set = (key: string, value: any) => {
    this[key] = value;
  }
}

// 导出单例实例
const loginStore = new LoginStore();
export default loginStore;
