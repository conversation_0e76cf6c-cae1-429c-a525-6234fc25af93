// @import '../onboarding/risk-toast/alert.scss';
@import '~@mu/login-popup/dist/style/index.scss';
.login-comp{
  width:100%;
  background-color: white;
  padding-bottom: 20px;
  border-radius: 16px;
}
.margin-left-right{
  margin:0 30px !important;
}
.sms-login{
  background-color: white;
  // 不加这个会导致z-index无用，导致部分内容被上面的背景覆盖
  position:relative;
}

.midMargin {
  margin-top: 20px;
}


.login-input-view {
  width: calc(100% - 40px);
  margin: 20px 20px;
  background-color: #f3f3f3;
  border-radius: 50px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  .innerImage {
    width: 32px;
    height: 36px;
    margin-left: 30px;
  }

  .innerInput {
    background-color: #f3f3f3;
    flex: 1;
    text-align: left;
  }

  .innerInputTemp {
    padding-left: 24px;
  }

  .phone {
    width: 544px;
  }

  .authCode {
    width: 304px;
  }
}

.login-view{
  background-color: white;
  // 不加这个会导致z-index无用，导致部分内容被上面的背景覆盖
  position:relative;
  .login-button{
    margin: 0 20px;
  }
  .login-input-content{
    margin:30px 0;
  }
}
.close-btn{
  position: absolute;
  right: 30px;
  top: 30px;
}

.login-btn-view{
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  &__bubble{
    align-self: flex-end;
    width: fit-content;
    padding: 0 15px;
    background-color: #FF8844;
    border-radius: 18px 18px 18px 2px;
    color: #ffffff;
    font-size: 22px;
    font-weight: 400;
    line-height: 22px;
    height: 38px;
    display: flex;
    align-items: center;
    z-index: 1;
    position: absolute;
    right: 20px;
    top: -20px;
  }
}



.login-button-btn {
  background-color: #3477ff;
  margin:0;
  padding: 0;
  color: white !important;
  font-size: 36px;
  font-weight: 600;
  line-height: 54px;
  height: 100px;
  width: calc(100% - 40px);
  border: 0 solid #05050514;
  margin:0 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px !important;
}

// 微信小程序中样式调整
.mu-radio--row{
  display: flex;
  justify-content: center;
}