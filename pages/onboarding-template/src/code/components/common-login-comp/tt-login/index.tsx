/**
 * 动码登录组件（本机校验流程失败后会走到这里）
 */
import { Component, } from '@tarojs/taro';
import { Button } from '@tarojs/components';
import { observer } from '@tarojs/mobx';
import classNames from 'classnames';
import { MUView } from '@mu/zui';
import loginStore from '../store';
import SMSVerify from '../sms-verify/index';

interface State {
}

@observer
class TTLogin extends Component<any, State> {
  componentDidMount() {
    this.initMiniProgram();
  }

  componentDidUpdate(prevProps) {
    // props中控制初始化的变量有变化，则再次进行小程序初始化判断
    if ((this.props.processType !== prevProps.processType) || (this.props.needInitInMiniProgram !== prevProps.needInitInMiniProgram)) {
      this.initMiniProgram();
    }
  }

  initMiniProgram = async () => {
    const { needInitInMiniProgram } = this.props;
    const { hasInitInMiniProgram } = loginStore;
    if (needInitInMiniProgram && !hasInitInMiniProgram) {
      await loginStore.handleTTUnionBindAuth();
    }
  }

  getPhoneNumber = (e: any) => {
    loginStore.handleTTGetPhoneNumber(e, this.props);
  }

  handleLoginClick = async () => {
    loginStore.handleTTLoginClick(this.props);
  }


  render() {
    const { onePassLogin } = loginStore.loginModel;
    const { mainButtonText, mainButtonBubbleText, disabled } = this.props;
    return (
      <MUView className="login-view">
        {!onePassLogin && <SMSVerify {...this.props} />}
        {this.props.children}
        <MUView className="login-btn-view">
        {
          mainButtonBubbleText && (
            <MUView className="login-btn-view__bubble">{mainButtonBubbleText}</MUView>
          )
        }
        <Button
          className={classNames('login-button-btn brand-bg', {
            'at-button--disabled': disabled,
          })}
          openType={(onePassLogin && !disabled) ? 'getPhoneNumber' : ''}
          onGetPhoneNumber={this.getPhoneNumber}
          onClick={this.handleLoginClick}
        >
          {mainButtonText}
        </Button>
      </MUView>
      </MUView>
    );
  }
}

export default TTLogin;


