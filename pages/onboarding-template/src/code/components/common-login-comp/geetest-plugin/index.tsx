import { Component } from '@tarojs/taro';
import { WeappGeetest } from '@mu/geetest-shell';
import { startGeetest } from '@mu/geetest-shell/interfaces';
import { MUView } from '@mu/zui';

const isH5 = process.env.TARO_ENV === 'h5';

class GeetestPlugin extends Component {
  constructor(props) {
    super(props);
    this.state = {
      weappGeetestOpen: false
    };
  }

  /**
   * 对于极验而言，在H5环境和非H5环境，使用的方式不一样，因此统一封装一下
   * 1、H5:通过startGeetest方法启动
   * 2、非H5:通过WeappGeetest组件启动
   */
  startGeetest = async () => {
    const { scene } = this.props;
    if (isH5) {
      try {
        const geestestRet = await startGeetest({ scene });
        this.handleGeetestResult(geestestRet);
      } catch (e) {
        this.props.onError(e);
      }
    } else {
      this.setState({ weappGeetestOpen: true });
    }
  };

  handleGeetestResult = (result) => {
    if (result && result.verifyResult === 1 && result.token) {
      this.props.onSuccess(result);
    } else {
      this.props.onError(new Error('Geetest verification failed'));
    }
  };

  render() {
    return !isH5 ? (
      <WeappGeetest
        isOpen={this.state.weappGeetestOpen}
        scene={this.props.scene}
        onSuccess={this.handleGeetestResult}
        onFail={this.props.onError}
        onClose={() => {
          this.setState({ weappGeetestOpen: false });
          this.props.onError('User canceled verification');
        }}
      />
    ) : <MUView />;
  }
}
export default GeetestPlugin;