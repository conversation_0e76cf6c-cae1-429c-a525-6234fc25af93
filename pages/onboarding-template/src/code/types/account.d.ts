// 账户领域类型定义

/**
 * 账户信息值对象
 */
export interface AccountInfo {
  /**
   * 总额度
   */
  limit: number;
  /**
   * 额度类型
   */
  limitType: string;
  /**
   * 额度状态
   */
  limitStatus: string;
  /**
   * 额度关闭原因
   */
  closeReason: string;
  /**
   * 额度列表
   */
  limitInfoList: any[];
  /** 是否有额度 */
  isCredit: boolean;
  /** 额度是否失效 */
  limitExpired: boolean;
  /** 失效原因是否清退 */
  limitTerminated: boolean;
} 