// 合同相关领域服务
import { getContractInstance } from '../models/contract/contract';
import { queryContractTemplate, queryNewContractConfig } from '../../api/contract/api';

/**
 * 合同服务
 * 处理合同相关的业务逻辑
 */
export class ContractService {
  /**
   * 查询合同配置信息
   * @param params 合同配置信息查询参数
   * @returns 合同配置信息
   */
  static async queryNewContractConfig(params: any) {
    const res = await queryNewContractConfig(params);
    const { contractCfgList } = res || {};
    const contractModel = getContractInstance();
    contractModel.setContractInfo({
      contractParams: contractCfgList
    }); 
    return res;
  }

  /**
   * 标准承接页查询合同模板信息
   * @param params 合同模板信息查询参数
   * @returns 合同模板信息
   */
  static async queryContractTemplate() {
    const contractModel = getContractInstance();
    const { contractParams: contractCfgList } = contractModel;
    const promiseList: any[] = [];
    const contractNameList: string[] = [];
    let forceFlag = false;
    let forceTime = 0;
    if (contractCfgList && contractCfgList.length !== 0) {
      // 使用合同3.0接口成功获取到数据
      contractCfgList.forEach((item) => {
        const { contractCode, contractName, forceReadFlag, forceReadDuration } = item;
        // 是否强制阅读以及强制阅读时间
        if (forceReadFlag === 'Y' || forceReadFlag === 'P') {
          forceFlag = true;
          forceTime = Math.max(forceTime, forceReadDuration || 0);
        }
        contractNameList.push(contractName);
        const contractsParams = {
          contractCode,
          scene: 'PREVIEW',
          interfaceVersion: '3.0',
          contractPreviewData: {}
        };
        promiseList.push(queryContractTemplate(contractsParams));
      });
      const res = await Promise.all(promiseList);
      // 拼接最终的协议
      const contractInfoList: any[] = [];
      res.forEach((item, index) => {
        if (!item) {
          return;
        }
        contractInfoList.push({
          title: contractNameList[index],
          htmlFile: item
        });
      });
      contractModel.setContractInfo({
        forceRead: forceFlag,
        forceReadDuration: forceTime || 5,
        contractText: contractNameList.join('、'),
        contractInfoList,
      });
    }
  }
}