// 登录服务
import Madp from '@mu/madp';
import { unionLogin, getUnionBindAuth, verifySmsCode, getThirdUserInfo, sendSmsCode, getOauthAccounts } from '../../api/login/api';
import { Url } from '@mu/madp-utils';
import { getLoginInstance } from '../models/login/login'

export class LoginService {
  /**
   * 联合登录
   * @param params 登录参数
   * @returns 登录结果
   */
  static async doUnionLogin(params: any): Promise<any> {
    return await unionLogin(params);
  }

  /**
   * 获取对应小程序环境的code，调用unionBindAuth接口或者查询第三方信息接口时需要用到
   */
  static loginApi = () => new Promise((resolve) => {
    let code = '';
    let env = {};
    switch (process.env.TARO_ENV) {
      case 'weapp':
        // @ts-ignore
        env = wx;
        break;
      case 'tt':
        // @ts-ignore
        env = tt;
      default:
        break;
    }
    // @ts-ignore
    if (env && typeof env.login === 'function') {
      // @ts-ignore
      env.login({
        success: (res) => {
          ({ code } = res || {});
          resolve(code);
        },
        fail: () => {
          resolve(code);
        }
      });
    } else {
      resolve(code);
    }
  });

  /**
   * 联合绑定
   * @param params 绑定参数
   * @returns 绑定结果
   */
  static async getUnionBindAuth(params: any): Promise<any> {
    const channel = Madp.getChannel();
    const code = await this.loginApi();
    if (!code) return;
    const unionBindParam = {
      channel,
      code,
      ...params
    };
    const configParam = {
      autoLoading: true,
      autoToast: true
    };
    const res = await getUnionBindAuth(unionBindParam, configParam);
    return res;
  }

  /**
   * 校验验证码
   * @param authCodeInput 验证码
   * @param authCodeToken 验证码token
   * @returns 验证码结果
   */
  static async verifySmsCode(authCodeInput: string, authCodeToken: string): Promise<any> {
    if (!authCodeToken) {
      return;
    }
    try {
      const paraDic = { code: authCodeInput, token: authCodeToken };
      const data = await verifySmsCode(paraDic);
      if (data) {
        // 校验成功，返回对应的动码token
        return authCodeToken;
      } else {
        return;
      }
    } catch (error) {
      return;
    }
  }

  /**
   * 获取第三方用户信息
   * @param data 数据
   * @returns 第三方用户信息
   */
  static async getThirdUserInfo(data: any): Promise<any> {
    const code = await this.loginApi();
    const paramsValue = {
      scene: 'BIND_PROCESS_CHECK',
      code,
      ...data
    };
    const res = await getThirdUserInfo(paramsValue) || {};
    const { processType = 'SMS_LOGIN' } = res || {};
    return processType;
  };

  /**
   * 获取登录手机号
   */
  static getLoginMobileNum = () => {
    const mobileNum = Url.getParam('mobileNum') || '';
    const loginModel = getLoginInstance();
    loginModel.setLoginInfo({ loginMobileNum: mobileNum });
    return mobileNum;
  }

  /**
   * 更新登录领域模型属性
   */
  static updateLoginProperty = (key: string, value: any) => {
    const loginModel = getLoginInstance();
    loginModel.set(key, value);
  }

  /**
   * 发送动码
   * @param params 动码参数
   * @returns 动码结果
   */
  static sendSmsCode = (params: any) => {
    return sendSmsCode(params);
  }

  /**
   * 查询信息（在登录过程中可以使用token来换取掩码手机号）
   * @param params 查询参数
   * @returns 查询结果
   */
  static getOauthAccounts = async (params: any) => {
    const res = await getOauthAccounts(params);
    return res;
  }
}