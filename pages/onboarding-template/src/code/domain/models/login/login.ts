// 登录领域模型
import { observable, action } from 'mobx';

export class LoginModel {
  /** 是否本机校验登录 */
  @observable onePassLogin = false;

  /** 是否需要自动发送动码，主要用于在抖音小程序中，当点击按钮获取到了手机号且本机校验失败时，需要进行动码登录，此时自动完成动码发送 */
  @observable needAutoSendCode = false;

  /** 登录手机号 */
  @observable loginMobileNum = '';

  /**
   * 批量设置登录信息
   * @param data 登录数据（支持部分更新）
   */
  @action.bound
  setLoginInfo = (data: Partial<LoginModel>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }

  @action.bound
  set = (key: string, value: any) => {
    this[key] = value;
  }
}

let loginInstance: LoginModel | null = null;

export function getLoginInstance(): LoginModel {
  if (!loginInstance) {
    loginInstance = new LoginModel();
  }
  return loginInstance;
}