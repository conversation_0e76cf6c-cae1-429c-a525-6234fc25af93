// @ts-ignore
import { apiHost, fetch } from '@mu/business-basic';

/**
 * 联合登录
 */
async function unionLogin(params?: any, envParams?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.loginRegister.unionLogin`, {
        data: {
          data: params,
        },
        reqEnvParams: envParams,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 查询联合绑定信息
 */
async function getUnionBindAuth(params?: any, envParams?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.loginRegister.unionBindAuth`, {
        data: {
          data: params,
        },
        reqEnvParams: envParams,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 校验验证码
 */
async function verifySmsCode(params?: any, envParams?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.basic.sms.verify`, {
        data: {
          data: params,
        },
        reqEnvParams: envParams,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 获取第三方信息
 */
async function getThirdUserInfo(params?: any, envParams?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.infoMaintain.getThirdUserInfo`, {
        data: {
          data: params,
        },
        reqEnvParams: envParams,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 发送动码
 */
async function sendSmsCode(params?: any, envParams?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.verifyIdentify.commonSendSms`, {
        data: {
          data: params,
        },
        reqEnvParams: envParams,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

/**
 * 查询信息（在登录过程中可以使用token来换取掩码手机号）
 */
async function getOauthAccounts(params?: any, envParams?: any) {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.user.loginRegister.getOauthAccounts`, {
        data: {
          data: params,
        },
        reqEnvParams: envParams,
        autoLoading: false,
        autoToast: false,
      },
    );
    return response;
  } catch (error) {
    throw error;
  }
}

export {
  unionLogin,
  getUnionBindAuth,
  verifySmsCode,
  getThirdUserInfo,
  sendSmsCode,
  getOauthAccounts,
}; 