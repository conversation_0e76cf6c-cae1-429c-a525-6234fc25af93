import { apiHost, fetch } from '@mu/business-basic';
import { accountInfoTranslator } from './translator';

/**
 * 查询账户信息
 */
async function queryAccountInfo(params?: any): Promise<any> {
  try {
    const response = await fetch(
      `${apiHost.mgp}?operationId=mucfc.loan.account.getAccount`, {
        data: {
          data: params,
        },
        autoLoading: false,
        autoToast: false,
      },
    );
    return accountInfoTranslator(response);
  } catch (error) {
    throw error;
  }
}

export {
  queryAccountInfo,
};