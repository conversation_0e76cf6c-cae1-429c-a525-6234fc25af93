function accountInfoTranslator(response: any): any {
  const { accountList = [] } = response || {};
  // 额度列表
  const limitInfoList = accountList && accountList[0] && accountList[0].limitInfoList || [];

  let hasLimit = false;
  let limitExpired = false;
  let limitTerminated = false;
  if (limitInfoList && limitInfoList.length > 0) {
    const limit = limitInfoList[0];
    hasLimit = true
    const { status, closeReason } = limit;
      if (status === 'X') {
        limitExpired = true;
        // 更新清退时候的判断，此处之所以用排除非清退的方法来判断是因为closeReason可能是一段文字描述
        const notTerminatedList = ['02', '03'];
        if (closeReason && !notTerminatedList.includes(closeReason)) {
          limitTerminated = true;
        }
      }
  }
  return {
    limitInfoList,
    isCredit:hasLimit,
    limitExpired,
    limitTerminated,
  };
}

export {
  accountInfoTranslator,
}