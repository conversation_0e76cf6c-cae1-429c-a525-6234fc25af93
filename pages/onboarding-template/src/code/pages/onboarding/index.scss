@import '../../components/common-login-comp/index.scss';
@import '~@mu/agreement/dist/style/components/drawer.scss';
// @import './tab-imgs/index.scss';
.scroll-content{
  height:100vh;
  position: relative;
  .onboarding-page1{
    background-color: whitesmoke;
    height:100vh; 
    .page-image{
      width:750px;
      height:auto;
      display: block;
    }
    .custom-icon{
      position: fixed;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      width:72px;
      height:72px;
      z-index:1;
      border-radius: 36px;
    }
    &__rule-entry{
      width: 80px;
      height: 80px;
      display: block;
      position: absolute;
      z-index: 1;
      right: 30px;
      top: 0;
    }
    &__content{
      margin-top:-20px;
      display: flex;
      flex-direction: column;
      &__login{
        margin:0 20px;
        border-radius:16px;
        background-color: white;
      }
      &__contract-checker{
        margin:0;
        // .mu-contract-checker{
        //   margin: 30px 0 !important;
        // }
      }
    }
    &__bottom-btn{
      padding: 20px;
      padding-bottom: 40px;
      height: 100px;
      
      position: fixed; 
      bottom: 0;      
      display: flex;
      justify-content: center; 
      z-index: 100;   
      width: calc(100vw - 40px);
      background-color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      .login-comp{
        width: calc(100vw - 40px);
      }
    }
  }
  .need-bottom-btn{
    padding-bottom: 150px;
  }
  
}
