import { Component } from '@tarojs/taro';
import { observer } from '@tarojs/mobx';
import { toJS } from 'mobx';
import { View } from '@tarojs/components';
import {
  MUView, 
  MUImage, 
  MUScrollView, 
  MUModal,
} from '@mu/zui';
import { debounce, Url, Validator } from '@mu/madp-utils';
import { AgreementDrawer } from '@mu/agreement';
// @ts-ignore
import { getLocalConstant, opService } from '@mu/business-basic';
// @ts-ignore
import { OpApply } from '@mu/op-comp';
import { track } from '@mu/madp-track';
import onboardingStore from './store';
import './index.scss';
import CommonLoginComp from '../../components/common-login-comp';
import ContractChecker from '../../components/contract-checker';
import { isWeapp } from '../../utils';

const pageId = '77033c63-e559-40a1-b15d-576aa1888d11';
const mobileNum = Url.getParam('mobileNum') || '';

interface OnboardingState {
  showBottomBtn: boolean;
  showContracts: boolean;
  hasCountDown: boolean;
  disabledLogin: boolean;
  contractChecker: boolean;
  isRuleDialogShow: boolean;
  isMask: boolean;
  showMoreDetailsContentModel: boolean;
}

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'Onboarding', // 就是当前组件类名
}))
@observer
class Onboarding extends Component<{}, OnboardingState> {
  timeout: any;
  openFlag: boolean;

  constructor(props) {
    super(props);
    this.state = {
      showBottomBtn: false, // 是否展示底部吸底按钮
      showContracts: false, // 是否展示协议弹窗
      hasCountDown: false, // 是否完成协议阅读
      disabledLogin: true, // 登录按钮是否禁用
      contractChecker: false, // 协议勾选框是否勾选
      isRuleDialogShow: false, // 规则弹窗是否展示
      isMask: false, // 是否展示掩码手机号
      showMoreDetailsContentModel: false // 是否展示更多产品信息弹窗
    };
  }

  componentDidMount() {
    onboardingStore.initData();
    this.resetTimeout();
  }

  /**
   * 监听30s无操作事件
   * 初始化时调用、用户点击页面时调用、用户滑动时调用
   */
  resetTimeout = debounce(() => {
    clearTimeout(this.timeout);
    // 如果已经弹出过停留30s的交互式运营，则不再监听
    if (this.openFlag) {
      return;
    }
    // 30s后触发交互式运营弹窗
    this.timeout = setTimeout(async () => {
      const { productParams } = onboardingStore;
      const { stayPopupCode } = productParams || {};
      if (!this.openFlag) {
        this.triggerOperation('opPageStay', stayPopupCode);
        this.openFlag = true;
        clearTimeout(this.timeout);
      }
    }, 30000);
  }, 100, { leading: true, trailing: false })

  // 触发交互运营
  triggerOperation(eventName: string, interactionEventCode: string, callback?: Function) {
    opService.process({
      eventName,
      data: {
        interactionEventCode,
        pageId
      },
      callback
    });
  }

  beforeRouteLeave = (from, to, next) => {
    // if (isBackFromExternalSys()) {
    //   return;
    // }
    // if (!isRouteLeaveToLastPageByPush(from, to, next)) { // 非返回上一页，正常跳转不拦截
    //   return;
    // }
    const { productParams } = onboardingStore;
    const { exitPopupCode } = productParams || {};
    // from.path === to.path 才触发交互式运营弹窗
    if (from.path === to.path) {
      this.triggerOperation('opPageLeave', exitPopupCode, () => {});
      next(false);
      return;
    }
    next(true);
  }

  // 处理滚动事件
  handleScroll = () => {
    if (isWeapp) {
      const observer = Taro.createIntersectionObserver(this);
      // 开始观察目标节点
      observer.relativeToViewport().observe('#login-btn-view', (res) => {
        if (res.intersectionRatio > 0) {
          // 元素至少有一部分在可视区域内，隐藏底部按钮
          this.setState({ showBottomBtn: false });
        } else {
          this.setState({ showBottomBtn: true });
        }
      });
    }
  }

  // 协议勾选框点击
  handleContractCheckboxClick = () => {
    const { contractInfoList, contractText, forceRead} = onboardingStore.contractModel;
    const { contractChecker, hasCountDown } = this.state;
    // 避免协议获取失败，但是依旧可以点击协议勾选框
    if ((contractInfoList || []).length === 0 || !contractText) {
      Madp.showToast({
        title: '获取协议失败，请返回重试',
        icon: 'none'
      });
      return;
    }
    // 若需要强读协议，且未勾选协议勾选框，且未完成协议阅读，则展示协议弹窗
    if (forceRead && !contractChecker &&!hasCountDown) {
      this.setState({ showContracts: true });
      return;
    }
    this.setState({ contractChecker: !contractChecker, disabledLogin: contractChecker });
  }

  // 协议名称点击
  handleContractNameClick = () => {
    this.setState({ showContracts: true });
  }

  // 登录成功处理
  handleLoginSuccess = () => {
    onboardingStore.handleLoginSuccess();
  }

  // 登录前校验
  handleBeforeLoginCheck = () => {
    const { contractChecker } = this.state;
    if (!contractChecker) {
      Madp.showToast({
        title: '请先阅读并勾选协议',
        icon: 'none'
      });
      return false;
    }
    return true;
  }

  handleContractClose = () => {
    this.setState({ showContracts: false });
  }

  handleContractSubmit = () => {
    this.setState({ hasCountDown: true, contractChecker: true, showContracts: false, disabledLogin: false });
  }

  goCustom = () => {
    const configLink = getLocalConstant({ nameSpace: 'link', configName: 'customService' }) || '';
    Madp.navigateTo({
      url: configLink
    });
  }

  // 渲染产品内容区域
  renderProductContent = () => {
    const { productParams } = onboardingStore;
    const {
      applyStep1,
      applyStep2,
      proDetail,
      moreDetailButton,
      bottomImg,
      tabList,
      callEntry
    } = productParams;

    const customIcon = 'https://file.mucfc.com/cop/51/52/202505/2025050811550523fc94.png';

    return (
      <MUView className="product-content">
        {/* 客服图标 */}
        {callEntry === '1' && (
          <MUImage 
            mode="widthFix" 
            className="custom-icon" 
            src={customIcon}
            onClick={this.goCustom}
          />
        )}
        
        {/* Tab图片列表 */}
        {tabList && tabList.length > 0 && tabList.map((tab, index) => (
          <MUView key={index} className="tab-item">
            <MUView className="tab-title">{tab.title}</MUView>
            <MUImage mode="widthFix" className="page-image" src={tab.imgUrl} />
          </MUView>
        ))}
        
        {/* 申请步骤图片 */}
        {applyStep1 && <MUImage mode="widthFix" className="page-image" src={applyStep1} />}
        {applyStep2 && <MUImage mode="widthFix" className="page-image" src={applyStep2} />}
        
        {/* 产品详情图片 */}
        {proDetail && <MUImage mode="widthFix" className="page-image" src={proDetail} />}
        
        {/* 更多详情按钮 */}
        {proDetail && moreDetailButton && (
          <MUImage 
            mode="widthFix" 
            className="page-image" 
            src={moreDetailButton}
            onClick={() => this.setState({ showMoreDetailsContentModel: true })}
          />
        )}
        
        {/* 底部图片 */}
        {bottomImg && <MUImage mode="widthFix" className="page-image" src={bottomImg} />}
      </MUView>
    );
  }

  render() {
    const {
      showBottomBtn,
      contractChecker,
      showContracts,
      hasCountDown,
      disabledLogin,
      isRuleDialogShow,
      showMoreDetailsContentModel,
    } = this.state;

    // 从产品运营参数获取数据
    const { productParams, processType, merchantId } = onboardingStore;
    const {
      topImg,
      ruleEntry,
      mainButtonText,
      mainButtonBubbleText,
      appealFailedText,
      ruleText,
      moreDetailDialogText,
    } = productParams;
    const { contractText, contractInfoList, forceRead, forceReadDuration } = onboardingStore.contractModel;
    const isMask = mobileNum && Validator.isMobilePhoneNumber(mobileNum);

    return (
      <MUScrollView
        scrollY
        onScroll={this.handleScroll}
        className="scroll-content"
      >
        <MUView className="onboarding-page1" onClick={this.resetTimeout} onTouchMove={this.resetTimeout}>
          {/* 规则入口图标 */}
          {ruleEntry && (
            <MUImage 
              className="onboarding-page1__rule-entry" 
              src={ruleEntry}
              onClick={() => { this.setState({ isRuleDialogShow: true }); }}
            />
          )}
          
          {/* 顶部主图 */}
          {topImg && (
            <MUImage 
              mode="widthFix" 
              className="page-image" 
              src={topImg} 
            />
          )}
          
          <MUView className={showBottomBtn ? 'onboarding-page1__content need-bottom-btn' : 'onboarding-page1__content'}>
            {/* 登录区域 */}
            <View className="onboarding-page1__content__login" id="login-btn-view">
              <CommonLoginComp
                handleBeforeLoginCheck={this.handleBeforeLoginCheck}
                securityLevel="normal-low-medium"
                isMask={isMask}
                disabled={disabledLogin || !contractChecker}
                processType={processType}
                needInitInMiniProgram
                onSuccess={this.handleLoginSuccess}
                mainButtonText={mainButtonText}
                mainButtonBubbleText={mainButtonBubbleText}
                appealFailedText={appealFailedText}
                extraLoginData={{
                  merchantId,
                  contractTypeList: ['REGIST', 'PRIVACY']
                }}
              >
                {/* 协议勾选区域 */}
                <MUView className="onboarding-page1__content__contract-checker">
                  <ContractChecker
                    checkedValue={contractChecker}
                    outerControl
                    beforeContractText="我已阅读并同意"
                    contractText={contractText}
                    contracts={[{}]}
                    onContractClick={this.handleContractNameClick}
                    handleCheckboxClick={this.handleContractCheckboxClick}
                  />
                </MUView>
              </CommonLoginComp>
            </View>
            
            {/* 产品内容区域 */}
            {this.renderProductContent()}
          </MUView>
          
          {/* 协议弹窗 */}
          <AgreementDrawer
            agreementViewProps={{
              list: toJS(contractInfoList),
            }}
            show={showContracts}
            submit={this.handleContractSubmit}
            close={this.handleContractClose}
            totalCount={forceRead ? forceReadDuration : 0}
            showBtn={!hasCountDown}
          />
         
          {/* 底部吸底按钮 */}
          {showBottomBtn && (
            <MUView className="onboarding-page1__bottom-btn">
              <CommonLoginComp
                handleBeforeLoginCheck={this.handleBeforeLoginCheck}
                securityLevel="normal-low-medium"
                needInitInMiniProgram={false}
                processType={processType}
                disabled={disabledLogin || !contractChecker}
                onSuccess={this.handleLoginSuccess}
                mainButtonText={mainButtonText}
                mainButtonBubbleText={mainButtonBubbleText}
                appealFailedText={appealFailedText}
                extraLoginData={{
                  merchantId,
                  contractTypeList: ['REGIST', 'PRIVACY']
                }}
              />
            </MUView>
          )}
          
          {/* 活动规则弹窗 */}
          <MUModal
            type="text"
            isOpened={ruleText && isRuleDialogShow}
            title="活动规则"
            content={ruleText}
            confirmText={'我知道了'}
            closeOnClickOverlay={false}
            onConfirm={() => { this.setState({ isRuleDialogShow: false }); }}
          />
          
          {/* 更多产品信息弹窗 */}
          <MUModal
            type="text"
            isOpened={moreDetailDialogText && showMoreDetailsContentModel}
            title="更多产品信息"
            content={moreDetailDialogText}
            confirmText="知道了"
            closeOnClickOverlay={false}
            onConfirm={() => { this.setState({ showMoreDetailsContentModel: false }); }}
          />
          
          
          <OpApply pageId={pageId} opEventKey="opPageLeave" />
          <OpApply pageId={pageId} opEventKey="opPageStay" />
         
        </MUView>
      </MUScrollView>
    );
  }
}

export default Onboarding;
