// 承接页store（控制层） 
import { observable, action } from 'mobx';
// @ts-ignore
import { getProductAllParams, jumpToHomePage, urlDomain } from '@mu/business-basic';
import { Url } from '@mu/madp-utils';
import { getLoanUrl, merchantIdToMsg } from '../../utils/constants';
import { ContractService } from '../../domain/services/contract-service';
import { getContractInstance } from '../../domain/models/contract/contract';
import { isWeapp } from '../../utils';
import { LoginService } from '../../domain/services/login-service';
import { AccountService } from '../../domain/services/account-service';
import { getAccountInstance } from '../../domain/models/account/account';
import { getLoginInstance } from '../../domain/models/login/login';

class OnboardingStore {
  @observable productParams: any = {};

  @observable merchantId: string = Url.getParam('merchantId') || '';

  @observable productCode: string = Url.getParam('productCode') || (merchantIdToMsg[this.merchantId] || {}).productCode || '';

  @observable mapCode: string = Url.getParam('mapCode') || (merchantIdToMsg[this.merchantId] || {}).mapCode || '';

  @observable openId: string = Url.getParam('openId') || '';

  @observable token: string = Url.getParam('token') || '';

  @observable contractModel = getContractInstance();

  @observable accountModel = getAccountInstance();

  @observable loginModel = getLoginInstance();

  @observable processType: string = '';

  /**
   * 获取产品运营参数
   * @param productCode 产品code
   */
  @action.bound
  getProductParams = async (productCode: string) => {
    const result = await getProductAllParams(productCode) || {};
    const {
      companyIntroduction1Text,
      companyIntroduction2Text,
      companyIntroduction3Text,
      companyIntroduction1,
      companyIntroduction2,
      companyIntroduction3
    } = result;

    const tabList: { title: string; imgUrl: string }[] = [];
    if (companyIntroduction1Text && companyIntroduction1) {
      tabList.push({
        title: companyIntroduction1Text,
        imgUrl: companyIntroduction1
      });
    }
    if (companyIntroduction2Text && companyIntroduction2) {
      tabList.push({
        title: companyIntroduction2Text,
        imgUrl: companyIntroduction2
      });
    }
    if (companyIntroduction3Text && companyIntroduction3) {
      tabList.push({
        title: companyIntroduction3Text,
        imgUrl: companyIntroduction3
      });
    }

    const productParams = {
      ...result,
      tabList
    };
    this.set('productParams', productParams);
  }

  /**
   * 获取合同信息
   */
  @action.bound
  getContract = async () => {
    await ContractService.queryNewContractConfig({ sceneCode: 'onboarding'});
    ContractService.queryContractTemplate();
  }

  /**
   * 初始化数据
   */
  @action.bound
  initData = async () => {
    await this.getProductParams(this.productCode);
    this.getContract();
    if (isWeapp) {
      const processType = await LoginService.getThirdUserInfo({
        openId: this.openId,
        token: this.token
      });
      this.set('processType', processType);
    }
  }

  /**
   * 登录成功处理
   */
  @action.bound
  handleLoginSuccess = async () => {
    await AccountService.queryAccountInfo({ queryScene: '10' });
    const { isCredit, limitExpired, limitTerminated } = this.accountModel;
    const { validLimitDistribute } = this.productParams;
    // 无额度，则进行建案
    // 额度失效但不是清退，则进行建案判断引导
    if (!isCredit || (limitExpired && !limitTerminated)) {
      // 去建案……
      // await createApplyCase({ storeParams: { mapCode: this.mapCode, busiType: 'apply' } });
      return;
    }

    // 额度失效且清退，或者额度未失效，但是要求分发到首页
    if (limitTerminated || validLimitDistribute === '1') {
      // @ts-ignore
      jumpToHomePage({ jumpMethod: 'redirectTo' });
      return;
    }

    // 有额度且额度未失效，且validLimitDistribute表明需要分发到借款
    if (validLimitDistribute === '0') {
      let configLink = getLoanUrl();
      // 跳借款时，如果是H5需要在后面加上redirectUrl，这样在结果页点击返回首页才会返回到redirectUrl，否则会返回到承接页来
      if (process.env.TARO_ENV === 'h5') {
        configLink = `${configLink}&redirectUrl=${encodeURIComponent(`${urlDomain}/${Madp.getChannel()}/homepage/#/pages/index/index`)}`;
      }
      Madp.redirectTo({
        url: configLink
      });
      return;
    }
  }
  
  /**
   * 设置属性
   * @param key 属性名
   * @param value 属性值
   */
  @action.bound
  set = (key: string, value: any) => {
    this[key] = value;
  }
}

// 导出单例实例
const onboardingStore = new OnboardingStore();
export default onboardingStore; 