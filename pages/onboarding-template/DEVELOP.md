# 开发指南 - 基于 DDD 的前端工程

本项目采用领域驱动设计（Domain-Driven Design）架构，旨在构建可维护、可扩展的前端应用。

## 🏗️ 架构概述

分层架构和目录规范请参考 https://alidocs.dingtalk.com/i/nodes/YndMj49yWjMvmKb5izpoPDl983pmz5aA

## 🛠️ 开发规范

### 1. 命名规范

- **文件命名**：kebab-case（短横线分隔）
- **类命名**：PascalCase（大驼峰）
- **变量/方法**：camelCase（小驼峰）
- **常量**：UPPER_SNAKE_CASE（大写下划线）

### 2. 代码组织

#### 实体设计
```typescript
export class User implements UserInfo {
  // 1. 属性定义（使用 @observable）
  @observable userId = '';
  
  // 2. 业务方法（使用 @action）
  @action.bound
  setState = (props: Partial<UserInfo>) => {
    // 实现
  };
  
  // 3. 私有方法
  private adapterProps = (key: string, val: any) => {
    // 实现
  };
}
```

#### 页面 Store 设计
```typescript
class PageStore {
  // 1. 状态定义
  @observable data = [];
  
  // 2. 计算属性
  @computed get filteredData() {
    return this.data.filter(item => item.visible);
  }
  
  // 3. 异步方法
  async loadData() {
    // 调用领域服务
  }
}
```

### 3. 最佳实践

#### ✅ 推荐做法

1. **单一职责**：每个类/函数只负责一个职责
2. **依赖注入**：通过参数传递依赖，而不是直接引用
3. **纯函数**：尽可能使用纯函数，便于测试
4. **类型安全**：充分利用 TypeScript 的类型系统
5. **响应式**：合理使用 MobX 的响应式特性

```typescript
// ✅ 好的实践
@action.bound
updateUser = (userInfo: Partial<UserInfo>) => {
  this.user.setState(userInfo);
};

// ✅ 纯函数转换器
export function userInfoTranslator(data: any): UserInfo {
  return {
    name: data.nickName || '',
    // ...
  };
}
```

#### ❌ 避免的做法

1. **跨层调用**：视图层直接调用 API
2. **业务逻辑泄露**：在视图层进行数据转换
3. **状态分散**：在多个地方管理同一份数据
4. **循环依赖**：层与层之间的循环引用

```typescript
// ❌ 错误的做法
class HomePage extends Component {
  async componentDidMount() {
    // 不应该在视图层直接调用 API
    const data = await fetch('/api/user');
    // 不应该在视图层进行数据转换
    this.setState({ 
      userName: data.nickName || '未知用户' 
    });
  }
}
```

## 🧪 测试策略

### 1. 单元测试

- **领域层**：重点测试业务逻辑
- **转换器**：测试数据转换的正确性
- **Store**：测试状态管理逻辑

### 2. 集成测试

- **API 层**：测试接口调用和数据转换
- **页面流程**：测试完整的用户操作流程

## 🚀 开发流程

### 1. 新增功能

1. **分析需求**：确定涉及的领域和实体
2. **设计模型**：定义或扩展领域模型
3. **实现服务**：编写领域服务
4. **添加接口**：实现 API 和转换器
5. **创建 Store**：编写控制层逻辑
6. **开发视图**：实现用户界面

### 2. 代码审查要点

- [ ] 是否遵循分层架构
- [ ] 是否违反依赖规则
- [ ] 业务逻辑是否在正确的层
- [ ] 是否有充分的类型定义
- [ ] 是否有必要的注释

## 🔧 常用工具和命令

### 开发命令

```bash
# 安装依赖
npm install

# 启动开发环境
ENV=st1 CH=0WEC npm run dev:h5

# 构建生产版本
npm run build:h5

# 代码检查
npm run lint

# 代码格式化
npm run prettier
```

## 📚 参考资料

- [阮一峰 TypeScript 教程](https://wangdoc.com/typescript/)
- [MobX 官方文档](https://mobx.js.org/)
- [Taro 官方文档](https://taro-docs.jd.com/)

## 🤝 贡献指南

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 新增功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

---

**记住：好的架构不是一蹴而就的，需要在实践中不断完善和优化。保持代码整洁，遵循设计原则，让项目更易维护和扩展。**
