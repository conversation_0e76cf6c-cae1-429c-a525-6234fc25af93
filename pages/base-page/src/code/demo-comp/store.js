import {
  observable, flow, action, configure
} from 'mobx';
import { beaconIds } from '@comp/comp-utils';
import Madp from '@mu/madp';
// TODO
// 手动埋点的beaconid 需要在@comp/comp-utils中配置该页面的beaconid
const BEACON_IDS = beaconIds.demoPage;
// 埋点的pageid
let BEACON_PAGE_ID = BEACON_IDS.pageId();
configure({
  enforceActions: 'observed'
});
class DemoStore {
  constructor() {
    // 再次取pageId更新，兼容小程序初始预加载拿不到url参数的问题
    BEACON_PAGE_ID = BEACON_IDS.pageId();
  }

  @observable title = '';

  /**
   * 初始化页面
   */
  @action.bound
  initComp = flow(function* initComp() {
    yield this.title = '示例页面';
    Madp.eventCenter.trigger('CARD_INIT_DONE');
  });

  @action.bound
  changeTitle() {
    this.title += '!';
  }
}

// 因为小程序重新获取BEACON_PAGE_ID，所以前面用let不能直接export
DemoStore.BEACON_IDS = BEACON_IDS;
DemoStore.BEACON_PAGE_ID = BEACON_PAGE_ID;

export default DemoStore;
