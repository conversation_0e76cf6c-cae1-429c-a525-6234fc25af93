import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import { MUView } from '@mu/zui';
import cardCompHoc from '@comp/card-comp-hoc';
// 只小程序用到，但只能import(编译时解析)，不能运行时require
import CardCompTpl from '@comp/card-comp-hoc/tpl';
import { injectState } from '@mu/leda';
import DemoStore from './store';
import './index.scss';

const { BEACON_IDS, BEACON_PAGE_ID } = DemoStore;

const propTypes = {
  applyGlobalStore: PropTypes.shape({
    getGlobalApplyConfig: PropTypes.func.isRequired,
    setGlobalData: PropTypes.func.isRequired,
    getGlobalData: PropTypes.func.isRequired
  }).isRequired,
  demoStore: PropTypes.shape({
    title: PropTypes.string.isRequired,
    changeTitle: PropTypes.func.isRequired,
    initComp: PropTypes.func.isRequired
  }).isRequired
};
// TODO
// 展位页面id
const ledaPageId = '';
@cardCompHoc(
  {
    needCardWrap: true,
    title: '示例页面',
    background: '#FFF',
    pageId: BEACON_PAGE_ID,
    getPageId: BEACON_IDS.pageId,
    showChatEntry: false,
    footerText: '底部文字',
    showProgress: false,
    showSafeFooter: true,
    legaoPageId: ledaPageId
  },
  (props) => ({
    demoStore: new DemoStore(props)
  })
)
/**
 * 如果页面不接入leda 可以将injectState代码以及cardCompHoc中的legaoPageId配置删除
 */
@injectState({
  pageId: () => ledaPageId,
  stateKeys: [],
  didShowRefresh: true
})
class DemoComp extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  async componentDidMount() {
    PropTypes.checkPropTypes(propTypes, this.props, 'prop', 'DemoComp');
    const {
      applyGlobalStore,
      demoStore: { initComp }
    } = this.props;
    // 从全局applyGlobalStore中获取数据 这是一个示例
    applyGlobalStore.getGlobalData('dataKey');
    await initComp();
  }

  render() {
    const {
      demoStore: { title, changeTitle }
    } = this.props;
    const cardView = (
      <MUView
        className="demo"
        onClick={changeTitle}
      >
        {title}
      </MUView>
    );

    return process.env.TARO_ENV === 'h5' ? (
      cardView
    ) : (
      <CardCompTpl
        {...this.props}
        {...this.state}
      >
        {cardView}
      </CardCompTpl>
    );
  }
}

DemoComp.propTypes = propTypes;

export default DemoComp;
export { BEACON_PAGE_ID };
