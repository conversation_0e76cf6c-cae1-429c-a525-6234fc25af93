{"name": "@mu/apply-base-page", "version": "1.0.1", "description": "申请业务基础页面模板,使用申请页面的统一开发模式,集成了统一的hoc", "files": ["src/code", "screenshot.png", "README.md"], "sideEffects": ["dist/*", "*.scss", "*.less", "*.css"], "scripts": {"dev:weapp": "npm run build:weapp -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:h5": "npm run build:h5 -- --watch debug", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "build:weapp": "madp build --type weapp", "build:qq": "madp build --type qq", "build:h5": "madp build --type h5", "build:swan": "madp build --type swan", "build:alipay": "madp build --type alipay", "lint": "eslint ./src --fix", "lint:style": "stylelint \"src/**/*.scss\" --syntax scss", "lint:style-fix": "stylelint \"src/**/*.scss\" --syntax scss --fix", "test": "NODE_ENV=test && jest --coverage", "test:ci": "npm run build:h5 && npm run test", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "handle-tag": "node ./script/handle-tag.js", "release": "standard-version && npm run handle-tag", "release:beta": "standard-version -p beta --release-as patch --skip.changelog && npm run handle-tag", "release:alpha": "standard-version -p alpha --release-as patch --skip.changelog && npm run handle-tag", "publish:npm": "npm run release && git push --follow-tags && npm publish", "publish:npm-patch": "npm run release -- --release-as patch && git push --follow-tags && npm publish", "publish:npm-alpha": "npm run release:alpha && git push --follow-tags && npm publish", "publish:npm-beta": "npm run release:beta && git push --follow-tags && npm publish"}, "keywords": ["taro", "business component", "Multiple platform", "material"], "dependencies": {"@mu/basic-library": "1.3.28-beta.2"}, "standard-version": {"skip": {"tag": true}}, "devDependencies": {"@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-react-jsx": "7.9.4", "@babel/preset-env": "7.9.0", "@mu/madp-cli": "1.8.0-beta.10", "@types/node": "12.12.31", "@types/react": "16.9.25", "@types/webpack-env": "1.15.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "babel-loader": "8.1.0", "babel-plugin-syntax-dynamic-import": "6.18.0", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-plugin-transform-export-extensions": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "1.7.0", "build-plugin-block": "1.0.0", "classnames": "2.2.6", "conventional-changelog-cli": "2.0.31", "cross-env": "5.2.1", "cz-conventional-changelog": "2.1.0", "enzyme": "3.10.0", "enzyme-adapter-react-16": "1.14.0", "fs-extra": "8.1.0", "husky": "1.3.1", "jest": "23.6.0", "mini-css-extract-plugin": "0.9.0", "nerv-server": "1.5.7", "nerv-test-utils": "1.5.7", "nervjs": "1.5.7", "standard-version": "5.0.2", "typescript": "3.8.3", "vconsole": "3.3.4", "vconsole-webpack-plugin": "1.5.1", "webpack": "4.42.1", "webpack-bundle-analyzer": "3.6.1", "webpack-merge": "4.2.2"}, "pageConfig": {"name": "ApplyBasePage", "title": "申请业务基础页面模板", "category": "Others", "screenshot": "", "repository": "", "source": {"type": "npm", "npm": "@mu/apply-base-page", "version": "1.0.0", "registry": ""}}, "publishConfig": {"registry": "http://npm.mucfc.com"}}