{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "jsxFactory": "Taro.createElement", "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "preserve", "typeRoots": ["node_modules/@types", "global.d.ts", "src/types"], "paths": {"@src/*": ["./src/*"], "@api/*": ["./src/api/*"], "@comp/*": ["./src/components/*"], "@constants/*": ["./src/constants/*"], "@utils/*": ["./src/utils/*"], "@models/*": ["./src/domain/models/*"], "@services/*": ["./src/domain/services/*"], "@types/*": ["./src/types/*"], "@assets/*": ["./src/assets/*"]}, "resolveJsonModule": true}, "compileOnSave": false}