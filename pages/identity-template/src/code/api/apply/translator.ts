// 申请数据转换器 

import {
  PersonalInfo, 
  MerchantInfo,
  IdentityCardInfo,
} from '../../types/apply';
import { ContractInfo } from '../../types/contract';
import { CardInfoResponse } from '../../types/api';
import { contractCodeMap } from '../../utils/contract-params';

/**
 * 卡片信息查询数据转换器
 *
 * 将后端 mucfc.apply.apply.queryCardInfo 接口返回的数据
 * 转换为前端可直接使用的结构化数据。
 *
 * @param response 后端原始响应数据
 * @returns 转换后的卡片信息对象
 *
 * @example
 * ```typescript
 * const rawData = await fetch('/api/queryCardInfo');
 * const cardInfo = cardInfoTranslator(rawData);
 * console.log(cardInfo.personalInfo.custName); // 用户姓名
 * ```
 */
function cardInfoTranslator(response: any): CardInfoResponse {
  const { applyCardBaseInfo = {}, extraMap = {}, merchantInfo: merchantData = {}, cardPropertyInfo = {}, contracts } = response || {};
  const { applyPersonalInfo = {} } = applyCardBaseInfo || {};
  const { custName = '', certId = '', custTypeSelected = '' } = applyPersonalInfo || {};
  const { custMobile = '' } = extraMap || {};
  
  // 商户机构信息解构
  const { 
    merchantId = '', 
    merchantName = '', 
    partnerType = '', 
    partnerId = '', 
    partner = '', 
    orgId = '', 
    orgName = '', 
    custservicePhoneNo = '' 
  } = merchantData || {};
  
  // 身份信息页卡片属性解构
  const { cardExtraProperty = {} } = cardPropertyInfo || {};
  const { 
    identityInfoModifiable = '', 
    BASIC_loanNotice = '', 
    identityButtonText = '下一步', 
    showOcr = '', 
    custTypeValue = '',
    hideContract = '',
    contractGroupId,
  } = cardExtraProperty || {};

  // 计算是否展示合同
  let isShowContract = hideContract !== 'Y';
  let finalContractGroupId = contractGroupId || 'XYDC';
  // 当为标准协议，且为非自营渠道，协议为 XYDCFZY
  if (finalContractGroupId === 'XYDC' && !(merchantId === '10000' || merchantId === '10001')) {
    finalContractGroupId = 'XYDCFZY';
  }
  // 合同名称拼接
  let contractText = '';
  let contractCheckItem = {};
  if (contracts && contracts.length > 0) {
    const contractTextArr: string[] = [];
    isShowContract = contracts && contracts.length > 0;
    contracts.forEach((item: any) => {
      // 过滤出贷款告知书
      if (item.contractType !== 'LOAN_APPLY_NOTICE') {
        contractTextArr.push(item.contractText);
      }
    });
    contractText = contractTextArr.join('、');
    contractCheckItem = contracts[0];
  } else {
    const { contractText: contractTextValue, contracts: contractCheckItemValue } = contractCodeMap[finalContractGroupId] || {};
    contractText = contractTextValue;
    contractCheckItem = (contractCheckItemValue || [])[0];
  }

  // 个人信息转换
  const personalInfo: Partial<PersonalInfo> = {
    custName,
    certId,
    custTypeSelected,
    custMobile,
  };

  // 商户机构信息转换
  const merchantInfo: Partial<MerchantInfo> = {
    merchantId,
    merchantName,
    partnerType,
    partnerId,
    partner,
    orgId,
    orgName,
    custservicePhoneNo,
  };

  // 身份信息页卡片属性转换
  const identityCard: Partial<IdentityCardInfo> = {
    identityInfoModifiable,
    BASIC_loanNotice,
    identityButtonText,
    showOcr,
    custTypeValue,
  };

  // 合同信息转换
  const contractInfo: Partial<ContractInfo> = {
    isShowContract,
    contractInfoList: contracts,
    contractText,
    contractGroupId: finalContractGroupId,
    oldContractFlag: !(contracts && contracts.length > 0 && contracts[0].contractCode),
    contractCheckItem,
  };

  return {
    personalInfo,
    merchantInfo,
    identityCard,
    contractInfo,
  };
}

export {
  cardInfoTranslator,
}; 