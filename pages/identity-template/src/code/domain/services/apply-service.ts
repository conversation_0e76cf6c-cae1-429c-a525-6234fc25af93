import { queryCardInfo } from '../../api/apply/api';
import { CardInfoResponse, QueryCardInfoParams } from '../../types/api';
import { getPersonalInfoInstance } from '../models/apply/personal-info';
import { getMerchantInstance } from '../models/apply/merchant';
import { getIdentityCardInstance } from '../models/apply/identity-card';
import { getContractInstance } from '../models/contract/contract';

export class ApplyService {
  /**
   * 获取卡片信息并更新相关模型
   *
   * 这是一个复合业务操作，包含：
   * 1. 调用数据接口层获取卡片信息
   * 2. 通过领域实体更新状态
   * 3. 返回更新后的卡片信息
   *
   * 业务流程：
   * API调用 -> 数据转换 -> 实体更新 -> 返回结果
   *
   * @param params 查询参数对象
   * @param params.cardCode 卡片编码，用于标识具体的卡片类型
   * @returns 包含所有相关信息的卡片数据
   * @throws 当API调用失败时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const cardInfo = await ApplyService.getCardInfo({ 
   *     cardCode: 'IDENTITY_CARD_01' 
   *   });
   *   console.log('个人信息:', cardInfo.personalInfo);
   *   console.log('商户信息:', cardInfo.merchantInfo);
   *   console.log('卡片配置:', cardInfo.identityCard);
   * } catch (error) {
   *   console.error('获取卡片信息失败:', error);
   * }
   * ```
   */
  static async getCardInfo(params: QueryCardInfoParams): Promise<CardInfoResponse> {
    const cardInfo = await queryCardInfo(params);
    
    // 更新个人信息模型
    const personalInfoModel = getPersonalInfoInstance();
    personalInfoModel.setPersonalInfo(cardInfo.personalInfo);
    
    // 更新商户信息模型
    const merchantModel = getMerchantInstance();
    merchantModel.setMerchantInfo(cardInfo.merchantInfo);
    
    // 更新身份卡片配置模型
    const identityCardModel = getIdentityCardInstance();
    identityCardModel.setIdentityCardInfo(cardInfo.identityCard);

    // 更新合同信息模型
    const contractModel = getContractInstance();
    contractModel.setContractInfo(cardInfo.contractInfo);
    
    return cardInfo;
  }
}