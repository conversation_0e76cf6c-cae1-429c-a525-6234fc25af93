// 合同相关领域服务
import { getPersonalInfoInstance } from '../models/apply/personal-info';
import { getMerchantInstance } from '../models/apply/merchant';
import { getContractInstance } from '../models/contract/contract';
import { queryContractConfig } from '../../utils';
import { queryContractTemplate } from '../../api/contract/api';

/**
 * 获取合同模板
 * @param params 合同查询参数
 * @param title 合同标题
 * @returns 合同模板信息
 */
const getContractTemplate = async (params: any, title: string) => {
  const htmlFile = await queryContractTemplate(params);
  return { title, htmlFile };
};

/**
 * 合同服务
 * 处理合同相关的业务逻辑
 */
export class ContractService {
  /**
   * 获取旧合同协议模板参数
   * @returns 合同参数配置
   */
  static getOldContractParams() {
    // 获取领域模型实例
    const personalInfoModel = getPersonalInfoInstance();
    const merchantModel = getMerchantInstance();
    const contractModel = getContractInstance();

    // 从个人信息模型获取数据
    const {
      custName: name,
      certId
    } = personalInfoModel;

    // 从商户信息模型获取数据
    const {
      merchantId: partnerId,
      merchantName: partner,
      orgId,
      orgName,
      custservicePhoneNo: partnerContact
    } = merchantModel;

    // 从合同信息模型获取数据
    const { contractInfoList } = contractModel;

    const date = new Date();

    // 基础参数配置
    let params: any = {
      name: name || '',
      certId: certId || '',
      yearNow: date.getFullYear(),
      monthNow: date.getMonth() + 1,
      dayNow: date.getDate(),
      contractDate: `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`,
      // TODO: 以下字段在当前领域模型中缺失，需要补充
      // mobile: '', // 手机号 - 需要从个人信息模型中添加
      // phoneNo: '', // 联系电话 - 需要从个人信息模型中添加
      // email: '/', // 邮箱 - 需要从个人信息模型中添加
      notarizeType: '1',
      email: '/', // 默认值，实际应从模型获取
      partner: partner || '',
      partnerId: partnerId || '',
      partnerContact: partnerContact || '/',
      orgId: orgId || '',
      orgName: orgName || '',
      // 联合贷目前只有浦发一个，所以第二个合作方默认传 /
      twoPartner: '/',
      twoPartnerId: '/',
      twoPartnerContact: '/',
      twoOrgId: '/',
      twoOrgName: '/',
      insureOrgName: '/',
      insureOrgContact: '/',
      twoInsureOrgName: '/',
      twoInsureOrgContact: '/'
    };

    const contractParams: any[] = [];
    const forceReadContractParams: any[] = [];
    let readDuration = '';

    // 处理合同信息列表
    const contracts = contractInfoList || [];

    // TODO: 需要从配置获取渠道合同配置
    const channelContractConfigList = queryContractConfig();
    const channelContractConfig = channelContractConfigList.slice(0, channelContractConfigList.length);

    contracts && contracts.length > 0 && contracts.forEach((item: any) => {
      if (item.contractEdition) { 
        // 合同2.0
        params = {
          ...params,
          contractVersion: item.autoUpdateFlag ? '' : item.contractVersion, // 版本号 自动更新时不传表示用最新的
          contractEdition: item.contractEdition, // 版别号
          contractCategory: item.contractType, // 合同类型
          isNeedArbitration: item.disputeResolution === '1',
          isNeedCompanySignature: item.needCompanySignatureFlag, // 公司签章
          isNeedCustSignature: item.needCustSignatureFlag, // 个人签章
          arbitrationName: item.arbitrationOrg, // 仲裁机构
          signAddress: item.signAddr, // 签订地址
          arbitrateName: item.arbitrationOrg, // 仲裁机构 兼容旧模版
          legalType: item.disputeResolution === '0' ? '1' : '2', // 仲裁方式 兼容旧模版
          courtType: item.disputeResolution === '0' ? '1' : '2', // 仲裁方式 兼容旧模版
          bringParam: '1', // 走预览合同接口
          insureCompany: item.insuranceCompany
        };
      } else { 
        // 合同2.0以前
        params = {
          ...params,
          contractType: item.contractType,
        };
      }

      if (item.contractType && item.contractType !== 'LOAN_APPLY_NOTICE') {
        contractParams.push({
          title: item.text,
          params
        });
        
        if (item.forceReadFlag === 'Y') {
          readDuration = item.readDuration; // 保存强制阅读时间
        }
      }

      // 从接口返回的协议合同配置里过滤出当前页面需要强制阅读的协议
      const checkNewContract = false;
      channelContractConfig.forEach((ContractConfigItem) => {
        if (item.contractType === ContractConfigItem.contractType
          && checkNewContract === ContractConfigItem.contractTypeFlag
          && ContractConfigItem.forceReadFlag === 'Y') {
          readDuration = ContractConfigItem.readDuration;
          ContractConfigItem.contractType !== 'LOAN_APPLY_NOTICE' && forceReadContractParams.push({
            title: item.text,
            params
          });
        }
      });
    });

    // 更新合同领域模型
    contractModel.setContractInfo({
      contractParams,
      forceReadContractParams,
      forceReadDuration: readDuration
    });

    return { 
      contractParams, 
      readDuration, 
      forceReadContractParams 
    };
  }

  /**
   * 获取新合同协议模板参数
   * @returns 新合同参数配置
   */
  static async getNewContractParams() {
    // 获取领域模型实例
    const personalInfoModel = getPersonalInfoInstance();
    const merchantModel = getMerchantInstance();
    const contractModel = getContractInstance();

    // 从个人信息模型获取数据
    const {
      custName: name,
      certId
      // TODO: 缺失字段 - 需要从个人信息模型中添加
      // mobile: '', // 手机号
    } = personalInfoModel;

    // 从商户信息模型获取数据
    const {
      custservicePhoneNo,
      merchantId,
      merchantName,
      orgName,
      partnerType,
      partnerId,
      partner
    } = merchantModel;

    // 从合同信息模型获取数据
    const { contractInfoList } = contractModel;

    // 基础合同信息配置
    let BaseContractInfo = {
      name: name || '',
      certId: certId || '',
      dateNow: `${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`, // YYYYMMDD格式
      // TODO: 缺失字段 - 需要从个人信息模型中添加
      mobile: '', // 手机号 - 当前模型中缺失
      signDate: `${new Date().toISOString().slice(0, 10).replace(/-/g, '')}`, // YYYYMMDD格式
      partnerInfoList: [{
        partnerId: partnerId || merchantId, // 机构编号
        partner: partner || orgName || merchantName, // 机构名称
        partnerType: partnerType || '01', // 机构类型 01-流量、02-资方、03-保险
        partnerContact: custservicePhoneNo // 联系方式
      }]
    };

    const contracts = contractInfoList || [];
    const promiseList: any[] = [];
    let forceContractTemplateList: any[] = [];
    let isForceReadContract = false;
    let newReadDuration = '';
    const forceContractTitle: string[] = []; // 强读协议标题

    // 处理每个合同
    if (contracts && contracts.length > 0) {
      for (const item of contracts as any[]) {
        // TODO: 缺失字段 - 需要从其他地方获取
        const rechargeInfo = {}; // 充值信息 - 当前模型中缺失

        let params = {
          contractPreviewData: { 
            baseContractInfo: { ...BaseContractInfo }, 
            rechargeInfo: { ...rechargeInfo } 
          },
          contractVersion: item.autoUpdateFlag ? '' : item.contractVersion, // 版本号 自动更新时不传表示用最新的
          contractCode: item.contractCode, // 合同编码
          contractCategory: item.contractType, // 合同类型
          interfaceVersion: '3.0', // 合同改造接口版本，新版合同改造版本传入3.0
          scene: 'PREVIEW',
        };

        promiseList.push(getContractTemplate(params, item.text || ''));

        if (!isForceReadContract && item.forceReadFlag === 'Y') {
          // 是否有强读协议,获取强读时间
          isForceReadContract = true;
          newReadDuration = item.readDuration || '5';
        }

        if (item.forceReadFlag === 'Y') {
          forceContractTitle.push(item.text || ''); // 记录强读协议的标题
        }
      }
    }

    // 解决并发请求协议模板数据返回顺序乱序问题
    const contractTemplateList = await Promise.all(promiseList);
    
    // 根据标题筛选出强读协议，避免重复查询
    forceContractTemplateList = contractTemplateList.filter(item =>
      forceContractTitle.includes(item.title)
    );

    // 更新合同领域模型
    contractModel.setContractInfo({
      contractParams: contractTemplateList,
      forceReadContractParams: forceContractTemplateList,
      forceReadDuration: newReadDuration
    });

    return { 
      contractTemplateList, 
      forceContractTemplateList, 
      newReadDuration, 
      isForceReadContract 
    };
  }
}