// 表单验证领域服务 
import Madp from '@mu/madp';
import { Validator } from '@mu/madp-utils';
import { PersonalInfo } from '../../types/apply';
import { getPersonalInfoInstance } from '../models/apply/personal-info';
import { checkName, getCertIdAge } from '../../utils';

/**
 * 表单验证领域服务
 * 
 * 负责表单数据的验证和更新，协调领域模型的操作
 */
export class FormValidationService {
  /**
   * 更新个人信息属性
   * @param key 属性名
   * @param value 属性值
   */
  static updatePersonalInfoProperty(key: keyof PersonalInfo, value: any) {
    const personalInfoModel = getPersonalInfoInstance();
    personalInfoModel.setProperty(key, value);
    
    // 身份证号特殊处理逻辑
    if (key === 'certId' && value.length === 18) {
      this.checkCertIdFormat(value);
    }
  }

  /**
   * 校验身份证号格式是否正确
   * @param certId 身份证号
   * @returns 是否为合法身份证号
   */
  static checkCertIdFormat(certId: string): boolean {
    // 检查是否为空
    if (!certId) {
      Madp.showToast({
        icon: 'none',
        title: '请输入你的身份证号',
      });
      return false;
    }
    if (!Validator.isIdentityNumber(certId)) {
      Madp.showToast({
        icon: 'none',
        title: '请填写正确的身份号',
      });
      return false;
    }
    return true;
  }

  /**
   * 表单完整性校验
   * @returns 校验是否通过
   */
  static checkForm(): boolean {
    const personalInfoModel = getPersonalInfoInstance();

    // 定义字段校验规则
    const validationRules = [
      {
        key: 'custName',
        value: personalInfoModel.custName,
        message: '请输入你的姓名'
      },
      {
        key: 'certId',
        value: personalInfoModel.certId,
        message: '请输入你的身份证号'
      },
      {
        key: 'custTypeSelected',
        value: personalInfoModel.custTypeSelected,
        message: '请选择你的身份'
      },
      {
        key: 'careerType',
        value: personalInfoModel.careerType,
        message: '请选择你的职业'
      },
      {
        key: 'incomeRange',
        value: personalInfoModel.incomeRange,
        message: '请选择你的收入范围'
      },
      {
        key: 'highestDegree',
        value: personalInfoModel.highestDegree,
        message: '请选择你的学历'
      },
      {
        key: 'schoolName',
        value: personalInfoModel.schoolName,
        message: '请输入你的学校名称'
      },
      {
        key: 'graduateYear',
        value: personalInfoModel.graduateYear,
        message: '请选择你的毕业年份'
      }
    ];

    // 逐一校验字段
    for (const rule of validationRules) {
      if (!rule.value || rule.value.trim() === '') {
        Madp.showToast({
          icon: 'none',
          title: rule.message,
        });
        return false;
      }
      if (rule.key === 'custName' && checkName(rule.value)) {
        Madp.showToast({
          icon: 'none',
          title: '填写的姓名不正确，请重新填写！',
        });
        return false;
      }
    }

    // 身份证号格式校验
    if (!this.checkCertIdFormat(personalInfoModel.certId)) {
      return false;
    }

    // 身份证号年龄校验
    const age = getCertIdAge(personalInfoModel.certId);
    if (age < 18 || age > 60) {
      Madp.showToast({
        icon: 'none',
        title: '很抱歉，你的年龄暂不符合申请要求',
      });
      return false;
    }

    return true;
   }

   /**
    * 组装提交参数
    * 收集表单数据并组装成提交格式
    * @returns 提交参数对象
    */
   static assembleSubmitParams(): any {
     const personalInfoModel = getPersonalInfoInstance();
     
     // 使用解构语法从领域模型中获取属性
     const {
       custName,
       certId,
       custTypeSelected,
       careerType,
       incomeRange,
       highestDegree,
       schoolName,
       graduateYear
     } = personalInfoModel;
     
     // 组装提交参数
     const submitParams = {
       applyPersonalInfo: {
         custName,
         certId,
         custTypeSelected
       },
       applyCompanyInfo: {
         careerType,
         incomeRange,
       },
       applyEducationInfo: {
         highestDegree,
         schoolName,
         graduateYear,
       },
     };
     
     console.log('📋 [FormValidationService] 组装提交参数:', submitParams);
     return submitParams;
   }

   /**
    * 提交表单
    * 集成表单校验和提交逻辑
    * @returns 提交是否成功
    */
   static async submitForm(): Promise<boolean> {
     // 执行表单校验
     if (!this.checkForm()) {
       return false;
     }
     
     try {
       // 组装提交参数
       const submitParams = this.assembleSubmitParams();
       
       // TODO: 实际调用API提交
       // const result = await submitApplyInfo(submitParams);
       const result = await Promise.resolve({ success: true, message: '提交成功' });
       
       console.log('✅ [FormValidationService] 提交成功:', result);
       Madp.showToast({
         icon: 'none',
         title: '提交成功',
       });
       return true;
     } catch (error) {
       return false;
     }
   }
 } 