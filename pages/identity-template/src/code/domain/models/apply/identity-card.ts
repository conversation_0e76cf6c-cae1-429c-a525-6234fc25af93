import { observable, action } from 'mobx';
import { IdentityCardInfo } from '../../../types/apply';

/**
 * 身份信息页卡片属性值对象
 */
export class IdentityCardModel implements IdentityCardInfo {
  /**
   * 身份信息是否可修改
   */
  @observable identityInfoModifiable = '';

  /**
   * 是否展示贷款告知书
   */
  @observable BASIC_loanNotice = '';

  /**
   * 提交按钮文案
   */
  @observable identityButtonText = '';

  /**
   * 是否展示OCR
   */
  @observable showOcr = '';

  /**
   * 身份选择器类型
   */
  @observable custTypeValue = '';

  /**
   * 批量设置身份信息页卡片属性
   * @param data 身份信息页卡片数据（支持部分更新）
   */
  @action.bound
  setIdentityCardInfo = (data: Partial<IdentityCardInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 身份信息页卡片单例实例
let identityCardInstance: IdentityCardModel | null = null;

/**
 * 获取身份信息页卡片单例实例
 * @returns IdentityCardModel实例 - 全局唯一的身份信息页卡片实例
 */
export const getIdentityCardInstance = (): IdentityCardModel => {
  if (!identityCardInstance) {
    identityCardInstance = new IdentityCardModel();
  }
  return identityCardInstance;
}; 