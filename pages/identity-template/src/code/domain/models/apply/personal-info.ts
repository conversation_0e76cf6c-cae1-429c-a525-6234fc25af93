import { observable, action } from 'mobx';
import { PersonalInfo } from '../../../types/apply';

/**
 * 个人信息值对象
 */
export class PersonalInfoModel implements PersonalInfo {
  /**
   * 用户名
   */
  @observable custName = '';

  /**
   * 身份证号
   */
  @observable certId = '';

  /**
   * 手机号
   */
  @observable custMobile = '';

  /**
   * 用户身份
   */
  @observable custTypeSelected = '';

  /**
   * 学历
   */
  @observable highestDegree = '';

  /**
   * 学校名称
   */
  @observable schoolName = '';

  /**
   * 学校编号
   */
  @observable schoolCode = '';

  /**
   * 入学年份
   */
  @observable enrollYear = '';

  /**
   * 毕业年份
   */
  @observable graduateYear = '';

  /**
   * 职业
   */
  @observable careerType = '';

  /**
   * 收入范围
   */
  @observable incomeRange = '';

  /**
   * 批量设置个人信息
   * @param data 个人信息数据（支持部分更新）
   */
  @action.bound
  setPersonalInfo = (data: Partial<PersonalInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }

  /**
   * 通用属性设置方法
   * @param key 属性名
   * @param value 属性值
   */
  @action.bound
  setProperty = (key: keyof PersonalInfo, value: any) => {
    if (this[key] !== value) {
      this[key] = value;
    }
  }
}

// 个人信息单例实例
let personalInfoInstance: PersonalInfoModel | null = null;

/**
 * 获取个人信息单例实例
 *
 * 采用单例模式确保全局个人信息状态的一致性。
 * 这是获取个人信息的唯一入口，避免创建多个个人信息实例。
 *
 * 使用场景：
 * - 在领域服务中操作用个人信息
 * - 在控制层 Store 中获取个人信息状态
 * - 在视图层中展示个人信息
 *
 * @returns PersonalInfoModel实例 - 全局唯一的个人信息实例
 *
 * @example
 * ```typescript
 * // 在领域服务中使用
 * const personalInfo = getPersonalInfo();
 * personalInfo.setPersonalInfo({ custName: '张三' });
 *
 * // 在 Store 中使用
 * @observable personalInfo = getPersonalInfo();
 * ```
 */
export const getPersonalInfoInstance = (): PersonalInfoModel => {
  if (!personalInfoInstance) {
    personalInfoInstance = new PersonalInfoModel();
  }
  return personalInfoInstance;
};