import { observable, action } from 'mobx';
import { StandardResultCardInfo } from '../../../types/apply';

/**
 * 申请结果页卡片属性值对象
 */
export class StandardResultCardModel implements StandardResultCardInfo {
  /**
   * 审批倒计时配置（秒）
   */
  @observable finalLoadingTime = '';

  /**
   * 审批通过是否展示提额入口
   */
  @observable passShowIncrementEntry = '';

  /**
   * 审批通过是否重点引导分期花
   */
  @observable passFocusFQH = '';

  /**
   * 是否跳导流页-非自动跳转
   */
  @observable GUIDE_needJumpToDiversionPage_SWITCH = '';

  /**
   * 是否自动跳转导流页
   */
  @observable supportJumpToDiversionPage = '';

  /**
   * 是否展示招联微管家
   */
  @observable resultWGJ = '';

  /**
   * 是否展示防欺诈提示
   */
  @observable resultFQZTS = '';

  /**
   * 是否自动跳借款
   */
  @observable GUIDE_autoSkipBorrowing_SWITCH = '';

  /**
   * 审批通过&审批中-引导下载
   */
  @observable GUIDE_download_SWITCH = '';

  /**
   * 批量设置申请结果页卡片属性
   * @param data 申请结果页卡片数据（支持部分更新）
   */
  @action.bound
  setStandardResultCardInfo = (data: Partial<StandardResultCardInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 申请结果页卡片单例实例
let standardResultCardInstance: StandardResultCardModel | null = null;

/**
 * 获取申请结果页卡片单例实例
 * @returns StandardResultCardModel实例 - 全局唯一的申请结果页卡片实例
 */
export const getStandardResultCardInstance = (): StandardResultCardModel => {
  if (!standardResultCardInstance) {
    standardResultCardInstance = new StandardResultCardModel();
  }
  return standardResultCardInstance;
}; 