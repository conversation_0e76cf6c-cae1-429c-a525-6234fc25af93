import { observable, action } from 'mobx';
import { ContractInfo } from '../../../types/contract';

/**
 * 合同实体（聚合根）
 */
export class ContractModel implements ContractInfo {
  /**
   * 强读标识
   */
  @observable forceReadFlag = '';

  /**
   * 强读时间
   */
  @observable forceReadDuration = '';

  /**
   * 是否展示协议
   */
  @observable isShowContract = false;

  /**
   * 合同组id
   */
  @observable contractGroupId = '';

  /**
   * 是否是旧合同
   */
  @observable oldContractFlag = false;

  /**
   * 合同信息列表
   */
  @observable contractInfoList = null;

  /**
   * 合同名称
   */
  @observable contractText = '';

  /**
   * 合同配置数据列表
   */
  @observable contractParams: any[] = [];

  /**
   * 强读合同配置数据列表
   */
  @observable forceReadContractParams: any[] = [];

  /**
   * 合同勾选框需要传入的合同项
   */
  @observable contractCheckItem: any = {};

  /**
   * 批量设置合同信息
   * @param data 合同数据（支持部分更新）
   */
  @action.bound
  setContractInfo = (data: Partial<ContractInfo>) => {
    Object.keys(data || {}).forEach((k) => {
      if (data[k] !== this[k]) {
        this[k] = data[k];
      }
    });
  }
}

// 合同聚合单例实例
let contractInstance: ContractModel | null = null;

/**
 * 获取合同聚合单例实例
 * @returns ContractModel实例 - 全局唯一的合同聚合实例
 */
export const getContractInstance = (): ContractModel => {
  if (!contractInstance) {
    contractInstance = new ContractModel();
  }
  return contractInstance;
};
