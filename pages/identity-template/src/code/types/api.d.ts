// 后端接口相关类型定义（入参、响应结果）

import { PersonalInfo, MerchantInfo, IdentityCardInfo } from './apply';
import { ContractInfo } from './contract';

/**
 * 查询卡片信息接口参数
 */
export interface QueryCardInfoParams {
  /**
   * 卡片编码，用于标识具体的卡片类型
   */
  cardCode: string;
}

/**
 * 卡片信息查询接口返回数据类型
 * 
 * 包含身份信息页所需的所有数据：
 * - 个人信息
 * - 商户机构信息 
 * - 身份信息页卡片属性
 * - 合同信息
 */
export interface CardInfoResponse {
  /**
   * 个人信息值对象
   */
  personalInfo: Partial<PersonalInfo>;
  /**
   * 商户机构值对象
   */
  merchantInfo: Partial<MerchantInfo>;
  /**
   * 身份信息页卡片属性值对象
   */
  identityCard: Partial<IdentityCardInfo>;
  /**
   * 合同信息值对象
   */
  contractInfo: Partial<ContractInfo>;
} 