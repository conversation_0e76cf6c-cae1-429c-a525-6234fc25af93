// 合同领域类型定义

/**
 * 合同信息值对象
 */
export interface ContractInfo {
  /**
   * 强读标识
   */
  forceReadFlag: string;
  /**
   * 强读时间
   */
  forceReadDuration: string;
  /**
   * 是否展示协议
   */
  isShowContract: boolean;
  /**
   * 合同组id
   */
  contractGroupId: string;
  /**
   * 是否是旧合同
   */
  oldContractFlag: boolean;
  /**
   * 合同信息列表
   */
  contractInfoList: any[] | null;
  /**
   * 合同文本（多个合同名称用"、"连接）
   */
  contractText: string;
  /**
   * 合同配置数据列表
   */
  contractParams: any[];
  /**
   * 强读合同配置数据列表
   */
  forceReadContractParams: any[];
  /**
   * 合同勾选框需要传入的合同项
   */
  contractCheckItem: any;
} 