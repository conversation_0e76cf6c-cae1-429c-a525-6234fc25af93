import { useState, useEffect } from '@tarojs/taro';
import { observer, PropTypes as MobxPropTypes } from '@tarojs/mobx';
import PropTypes from 'prop-types';
import {
  MUForm,
  MURadio,
} from '@mu/zui';
import ClickSelector from '@mu/click-selector';
import './index.scss';

const propTypes = {
  formData: PropTypes.oneOfType([
    MobxPropTypes.arrayOrObservableArray,
    MobxPropTypes.objectOrObservableObject
  ]),
  range: MobxPropTypes.objectOrObservableObject,
  updateForm: PropTypes.func.isRequired,
  beaconContent: MobxPropTypes.objectOrObservableObject,
  ifShowStar: PropTypes.bool.isRequired,
  ifSingleRow: PropTypes.bool.isRequired,
  titleType: PropTypes.string.isRequired,
};

const defaultProps = {
  beaconContent: null,
  range: {},
  formData: [],
  ifShowStar: false,
  ifSingleRow: false,
  titleType: 'list'
};

function IdentityChoice(props) {
  const {
    formData,
    updateForm,
    ifShowStar,
    ifSingleRow,
    showTitle = true,
    count,
    needFixWidth = true
  } = props;

  const [formDataNew, changeFormData] = useState(formData);

  useEffect(() => {
    PropTypes.checkPropTypes(propTypes, props, 'prop', 'IdentityChoice');
    changeFormData(formData);
  }, [formData, props]);

  const onClickSelector = (val) => {
    updateForm(val);
  };
  const rangMap = [
    {
      key: 'C01001',
      value: '我已工作'
    },
    {
      key: 'C01002',
      value: '我是学生'
    }
  ];

  return (
    <MUForm titleType="list" title={showTitle ? '身份选择' : ''} ifShowStar={ifShowStar}>
      {ifSingleRow
        ? <MURadio
            ifShowStar={ifShowStar}
            count={2}
            title="身份选择"
            type="badge"
            className="identity-choice-radio"
            options={rangMap && rangMap.map(
              (o) => ({ value: o.key, label: o.value })
            )}
            value={formDataNew.custTypeSelected}
            onClick={(val) => { onClickSelector(val); }}
        />
        : <ClickSelector
            count={count || 2}
            needFixWidth={needFixWidth}
            type={'IdentityChoice'}
            range={rangMap || []}
            value={formDataNew.custTypeSelected}
            onClickItem={(val) => { onClickSelector(val); }}
        />}
    </MUForm>
  );
}
IdentityChoice.options = {
  addGlobalClass: true
};
IdentityChoice.config = {
  styleIsolation: 'shared'
};
IdentityChoice.propTypes = propTypes;
IdentityChoice.defaultProps = defaultProps;

export default observer(IdentityChoice);
