import { useState, useEffect, useRef } from '@tarojs/taro';
import { observer, PropTypes as MobxPropTypes } from '@tarojs/mobx';
import PopupPicker from '../popup-picker';
import PropTypes from 'prop-types';
import {
  MUForm,
  MUView,
  MUListItem,
  MUPicker,
  MUText
} from '@mu/zui';
import ClickSelector from '@mu/click-selector';
import VagueSearch from '@mu/vague-search';
import dayjs from 'dayjs';
import {
  findObjFromArr
} from '../../utils';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('./index.scss');
}

const propTypes = {
  formName: PropTypes.string.isRequired,
  formData: PropTypes.oneOfType([
    MobxPropTypes.arrayOrObservableArray,
    MobxPropTypes.objectOrObservableObject
  ]),
  range: MobxPropTypes.objectOrObservableObject,
  studentDegreeList: MobxPropTypes.objectOrObservableObject,
  updateForm: PropTypes.func.isRequired,
  schoolNameBeaconId: PropTypes.string.isRequired,
  beaconId: PropTypes.string.isRequired,
  originGraduateYear: PropTypes.string,
  halfScreenScroll: PropTypes.bool.isRequired
};

const defaultProps = {
  beaconContent: null,
  range: {},
  formData: [],
  needShowChoice: false,
  originGraduateYear: '',
  halfScreenScroll: false
};

function SchoolInfo(props) {
  const {
    formName,
    formData,
    updateForm,
    studentDegreeList,
    updateSchoolNameCode,
    schoolNameValue: schoolNameValueProps,
    schoolNameBeaconId,
    beaconId,
    originGraduateYear,
    ifShowStar,
    halfScreenScroll
  } = props;

  const [formDataNew, changeFormData] = useState(formData);

  // 是否回显日
  const [showDay, changeShowDay] = useState(false);

  const schoolNameCodeList = useRef([]);

  useEffect(() => {
    changeFormData(formData);
  }, [formData]);

  /**
   * 处理学校名称更新
   * @param {string} val 学校名称
   */
  const onSchoolNameChange = (val) => {
    const { target } = findObjFromArr(schoolNameCodeList.current, { schoolName: val });
    updateForm('schoolName', val);
    updateForm('schoolCode', target.schoolCode || '00000'); // 取不到则为其他学校，兜底学校编码
  };

  useEffect(() => {
    PropTypes.checkPropTypes(propTypes, props, 'prop', 'SchoolInfo');
  }, [props]);

  const onClickSelector = (val) => {
    updateForm('highestDegree', val);
  };

  const onClickGraduateYear = (val) => {
    updateForm('graduateYear', val);
    if (val) {
      changeShowDay(true);
    }
  };

  const getMaxDay = (year, month) => {
    if (month === 4 || month === 6 || month === 9 || month === 11) return 30;
    if (month === 2) {
      if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) return 29;
      else return 28;
    }
    return 31;
  };

  /**
   * 获取年份列表
   * @param {number} time 毕业时间
   */
  const getYearList = (time) => {
    const graduateTime = dayjs(time).format();
    let nowDate = new Date();
    const newGraduateTime = new Date(graduateTime);
    const getNewDateTime = newGraduateTime.getTime();
    // 毕业时间小于今天，才使用毕业时间做开始时间
    if (time && getNewDateTime && dayjs(newGraduateTime).isBefore(nowDate)) {
      nowDate = newGraduateTime;
    }
    // 当前年份
    const currentYear = nowDate.getFullYear();
    // 当前月份
    const currentMonth = (`0${nowDate.getMonth() + 1}`).substr(-2);
    // 当前日期
    const beginYear = `${currentYear}-${currentMonth}-` + '01';
    // 未来十年日期
    const day = process.env.TARO_ENV === 'alipay' ? getMaxDay(currentYear + 10, currentMonth) : '01';
    const endYear = `${currentYear + 10}-${currentMonth}-${day}`;

    return {
      beginYear,
      endYear
    };
  };

  const getGraduateYearValue = () => {
    // 支付宝日期组件模拟器格式是xxxx-xx-xx,真机是xxxx/xx/xx,如果遇到模拟器日期显示有问题，就不要管了，日期这里看真机就好
    const formYearAndMonthValue = process.env.TARO_ENV === 'alipay' || (formDataNew.graduateYear && formDataNew.graduateYear.indexOf('/') > -1)
      ? (formDataNew.graduateYear && formDataNew.graduateYear.split('/'))
      : (formDataNew.graduateYear && formDataNew.graduateYear.split('-'));
    const year = formYearAndMonthValue && formYearAndMonthValue[0] || '';
    const month = formYearAndMonthValue && formYearAndMonthValue[1] || '';
    const date = formYearAndMonthValue && formYearAndMonthValue[2] || '';
    let graduateYearValue = '';
    if (!year) {
      graduateYearValue = '';
    } else if (year && !month) {
      graduateYearValue = `${year}年`;
    } else if (year && month) {
      let day = process.env.TARO_ENV === 'alipay' ? (date) : '';
      if (showDay) {
        day = process.env.TARO_ENV === 'alipay' ? (`${date}日`) : '';
      }
      graduateYearValue = `${year}年${month}月${day}`;
    } else {
      graduateYearValue = '';
    }
    return graduateYearValue;
  };

  // 格式化毕业时间
  const formatGraduateYear = (date) => {
    if (process.env.TARO_ENV === 'alipay') {
      return dayjs(date).format('YYYY/MM/DD');
    }
    return dayjs(date).format('YYYY-MM-DD');
  };

  const { beginYear, endYear } = getYearList(originGraduateYear);
  let title = null;
  if (halfScreenScroll) {
    title = (
      <MUView>
        <MUView className="school-info__title">学校信息</MUView>
        {ifShowStar && <MUView className="school-info__star">*</MUView>}
      </MUView>
    );
  } else {
    title = '学校信息';
  }
  return (
    <MUForm titleType={halfScreenScroll ? 'default' : 'list'} title={title} className="studentView" ifShowStar={ifShowStar}>
      {
        halfScreenScroll
          ? <PopupPicker
            title="在读学历"
            placeholder="请选择你的在读学历"
            labelFieldName="value"
            valueFieldName="key"
            selectorTitle="请选择你的在读学历"
            ifSingleRow={false}
            value={formDataNew.highestDegree}
            range={studentDegreeList || []}
            height={450}
            onClickItem={(val) => { onClickSelector(val); }}
          />
          : <ClickSelector
            type={formDataNew.highestDegree}
            title="在读学历"
            count={3}
            range={studentDegreeList || []}
            value={formDataNew.highestDegree}
            onClickItem={(val) => { onClickSelector(val); }}
          />
      }

      <VagueSearch
        className="school-search"
        forbidInput
        title="学校名称"
        placeholder="请输入所在学校"
        errorText="请输入正确的学校名称"
        beaconId={schoolNameBeaconId}
        value={formDataNew.schoolName}
        handleChange={onSchoolNameChange}
        optionList={[]}
      />
      {/* 预计毕业年份：选择范围为当前年月至未来十年之间
          * 支付宝小程序必须要有日，不然结束和开始日期会不生效，主要是支付宝原生日期的bug，
        */}
      <MUPicker
        beaconId={`${beaconId}Popup`}
        mode="date"
        value={formatGraduateYear(formDataNew.graduateYear)}
        fields={process.env.TARO_ENV === 'alipay' ? 'day' : 'month'}
        onChange={(e) => { onClickGraduateYear(e.detail.value); }}
        start={beginYear}
        end={endYear}
      >
        <MUListItem
          className="yearList"
          arrow="mu-icon-arrow-right"
          title="预计毕业年份"
          content={getGraduateYearValue()}
          renderContent={<MUText className="placeholder">{getGraduateYearValue() ? '' : '请选择预计毕业年份'}</MUText>}
          beaconId={`${beaconId}Item`}
        />
      </MUPicker>
    </MUForm>
  );
}

SchoolInfo.options = {
  addGlobalClass: true
};
SchoolInfo.config = {
  styleIsolation: 'shared'
};
SchoolInfo.propTypes = propTypes;
SchoolInfo.defaultProps = defaultProps;

export default observer(SchoolInfo);
