<!DOCTYPE html>
<html>
<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
  <meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes">
  <meta name="format-detection" content="telephone=no,address=no">
  <meta name="apple-mobile-web-app-status-bar-style" content="white">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" >
  <link rel="preload" as="script" href="../config/local-config.sc.js">
  <link rel="preload" as="script" href="../muwa/madp-fe-script1.sc.js?version=1.0.0">
  <link rel="preload" as="script" href="../muwa/madp-fe-script2.sc.js?version=1.0.0">
  <link rel="preload" as="script" href="../muwa/madp-fe-script3.1.sc.js">
  <link rel="preconnect" href="https://picasso.mucfc.com">
  <link rel="preconnect" href="https://das.api.mucfc.com">
  <link rel="preconnect" href="https://mgp.api.mucfc.com">
  <link rel="preconnect" href="https://file.mucfc.com">
  <title>index</title>
  <script>
  window._MU_MODULE_VERSION = '1.0.0';

  function getAllCookies() {
    var cookies = document.cookie.split(";");
    var cookieObj = {};
    for (var i = 0; i < cookies.length; i++) {
      var cookieArr = cookies[i].split("=");
      var key = cookieArr[0].replace(/^s+|s+$/g, '');
      var value = cookieArr[1];
      cookieObj[key] = value;
    }
    return cookieObj;
  }

  var cookies = getAllCookies();
  var greyCookie = cookies.grey;
  var date = new Date();
  date.setTime(date.getTime() + (7 * 24 * 60 * 60 * 1000));
  if (!greyCookie) {
    if (Math.random() <= 0.0001) {
      document.cookie = 'grey=1;expires=' + date.toUTCString() + ';path=/;domain=.mucfc.com';
      location.reload();
    } else {
      document.cookie = 'grey=0;expires=' + date.toUTCString() + ';path=/;domain=.mucfc.com';
    }
  }

    !function(x){function w(){var v,u,t,tes,s=x.document,r=s.documentElement,a=r.getBoundingClientRect().width;if(!v&&!u){var n=!!x.navigator.appVersion.match(/AppleWebKit.*Mobile.*/);v=x.devicePixelRatio;tes=x.devicePixelRatio;v=n?v:1,u=1/v}if(a>=640){r.style.fontSize="40px"}else{if(a<=320){r.style.fontSize="20px"}else{r.style.fontSize=a/320*20+"px"}}}x.addEventListener("resize",function(){w()});w()}(window);
  </script>
</head>
<body>
  <div id="app"></div>
  <script>
    //定义是否需要执行document.write xxx.js的条件函数
    function needDocumentWriteJS() {
      var ua = window.navigator.userAgent.toLowerCase();
      var munaRegex = /muna\((\d+\.\d+\.\d+)\)/; //取ua中muna版本号的正则表达式
      var munaMatch = ua.match(munaRegex); //正则匹配
      var currentMuna = '0.0.0'; //设置初始值
      if (munaMatch) {
        currentMuna = munaMatch[1]; //正则提取出当前muna版本号
      }
      var targetMuna = '4.12.0'; //优化后的muna版本为4.12.0

      //只有muapp渠道且muna版本号小于4.12.0，才需要document.write xxx.js
      if ((/muapp\//i).test(ua) && compareVersion(currentMuna, targetMuna) === -1) {
        return true;
      }
      //其余情况都不需要
      return false;
    }
    if (needDocumentWriteJS()) {
      document.write("<script src=\'https://munaResource/HttpRequestPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/UiKitPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/PagerJumpPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/NotificationPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/BankCardPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/ContactPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/H5TitleBarPlugin.js\'><\/script>"
        + "<script src=\'https://munaresource/GeolocationPlugin.js\'><\/script>"
        + "<script src=\'https://munaresource/CameraPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/SharePlugin.js\'><\/script>"
        + "<script src=\'https://munaresource/DeviceFingerPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/HtmlCopyPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/IdScannerPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/LocalBiometricPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/RemotePreferencePlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/WebViewLongPressPlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/UploadFilePlugin.js\'><\/script>"
        + "<script src=\'https://munaResource/AppPlugin.js\'><\/script>"
      );
    }
  </script>
  <script src="../config/local-config.sc.js"></script>
  <script src="../muwa/madp-fe-script1.sc.js?version=1.0.0"></script>
  <script src="../muwa/madp-fe-script2.sc.js?version=1.0.0"></script>
  <script src="../muwa/madp-fe-script3.1.sc.js"></script>
</body>
</html>
