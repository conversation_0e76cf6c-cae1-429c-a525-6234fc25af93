module.exports = {
  extends: ["stylelint-config-standard", "stylelint-config-recommended-scss"],
  rules: {
    // 缩进
    // 用两个空格作为缩进
    "indentation": 2,

    // 分号
    // 所有声明都应该以分号结尾，不能省略
    "declaration-block-trailing-semicolon": "always",
    // 避免出现额外的分号
    "no-extra-semicolons": true,

    // 空格
    // 函数的逗号之后必须有一个空格，逗号前没有空格
    "function-comma-space-after": "always",
    "function-comma-space-before": "never",
    // 函数之间应该有空格隔开
    "function-whitespace-after": "always",
    // 属性值的逗号前面没有空格，后面有空格
    "value-list-comma-space-before": "never",
    "value-list-comma-space-after": "always",
    // 属性名和冒号之间无空格，冒号和属性值之间保留一个空格
    "declaration-colon-space-before": "never",
    "declaration-colon-space-after": "always",
    // 选择器和大括号之间保留一个空格
    "block-opening-brace-space-before": "always",
    // @media 括号中的冒号后面需要一个空格，前面不需要空格
    "media-feature-colon-space-before": "never",
    "media-feature-colon-space-after": "always",
    // @media 的范围操作符前后需要一个空格
    "media-feature-range-operator-space-after": "always",
    "media-feature-range-operator-space-before": "always",
    // @media 中，括号内不允许有空白符
    "media-feature-parentheses-space-inside": "never",
    // 注释内容和注释符之间留有一个空格
    "comment-whitespace-inside": "always",
    // 函数括号内侧不能有空白符
    "function-parentheses-space-inside": "never",
    // 在组合选择器之间必须有一个空格
    "selector-combinator-space-before": "always",
    "selector-combinator-space-after": "always",

    // 换行
    // 在块的右括号后要求有换行
    "block-closing-brace-newline-after": "always",
    // 选择器之间不能有空行
    "selector-max-empty-lines": 0,
    // 在 @ 规则的分号后需要换行符
    "at-rule-semicolon-newline-after": "always",
    "at-rule-semicolon-space-before": "never",
    // 在多行块中，开大括号后必须始终有一个换行符，闭大括号之前必须有一个换行符
    "block-opening-brace-newline-after": "always-multi-line",
    "block-closing-brace-newline-after": "always",
    "block-closing-brace-newline-before": "always-multi-line",
    // 在多行块中，声明块的分号之后必须有一个换行符
    "declaration-block-semicolon-newline-after": "always-multi-line",

    // 属性和属性值
    // 不允许属性存在冗余值
    "shorthand-property-no-redundant-values": true,
    // 指定 16 进制值使用小写字母
    "color-hex-case": ["lower", {
        "severity": "warning"
      }
    ],
    // 指定 16 进制颜色使用简写
    "color-hex-length": ["short", {
        "severity": "warning"
      }
    ],
    // CSS 属性值为小写
    "value-keyword-case": "lower",
    // CSS 属性为小写
    "property-case": "lower",
    // 长度值是 0 时，省略长度单位
    "length-zero-no-unit": true,
    // 小于1的值，小数点前需要有0
    "number-leading-zero": "always",
    // 在数字中不允许尾随 0
    "number-no-trailing-zeros": true,
    // 单位应该统一为小写
    "unit-case": ["lower", {
        "disableFix": true,
        "severity": "warning"
      }
    ],
    // @media 中属性名使用小写字母
    "media-feature-name-case": "lower",
    // 不要使用 !important重写样式
    "declaration-no-important": [true, {
        "severity": "warning"
      }
    ],

    // 选择器
    // 伪类选择器使用小写字母
    "selector-pseudo-class-case": "lower",
    // 伪元素选择器使用小写字母
    "selector-pseudo-element-case": "lower",
    // 类型选择器使用小写字母
    "selector-type-case": "lower",
    // 属性选择器使用双引号
    "selector-attribute-quotes": "always",
    // 禁止未知的类型选择器
    "selector-type-no-unknown": [
      true,
      {
        "ignoreTypes": [
          "page",
          "scroll-view"
        ]
      }
    ],
    // 禁止样式表中的重复选择器
    "no-duplicate-selectors": true,
    // 指定类选择器的模式。
    "selector-class-pattern": null,

    // @规则
    // @规则使用小写字母
    "at-rule-name-case": "lower",
    // @import规则指定字符串
    "import-notation": "string",

    // 函数
    // 函数名是小写字母
    "function-name-case": "lower",
    // url() 中需要加引号
    "function-url-quotes": "always",

    // 代码长度最大不超过 120 个字符
    "max-line-length": [120, {
        "severity": "warning"
      }
    ],
    // 禁止未知的单位
    "unit-no-unknown": [
      true,
      {
        "ignoreUnits": [
          "rpx"
        ]
      }
    ],
    // 禁止空源码。
    "no-empty-source": true,
    // 禁止可合并为一个简写属性的扩写属性。
    "declaration-block-no-redundant-longhand-properties": true,
    // 禁止声明块的重复属性。
    "declaration-block-no-duplicate-properties": true,
    // 指定颜色函数的现代或传统表示法
    "color-function-notation": "legacy",
    // 禁止属性的浏览器引擎前缀。
    "property-no-vendor-prefix": [
      true,
      {
        "ignoreProperties": [
          "text-fill-color",
          "background-clip"
        ]
      }
    ],

    // 需关闭的规则
    // 禁止 CSS 不支持并可能导致意外结果的双斜杠注释（//...）
    "no-invalid-double-slash-comments": null,
    // 禁止在字体族名称列表中缺少通用字体族关键字
    "font-family-no-missing-generic-family-keyword": null,
    // 禁止在具有较高优先级的选择器后出现被其覆盖的较低优先级的选择器。
    "no-descending-specificity": null,

    // scss 规则
    // 禁止使用缺少占位符的@extend, 我们用的 sass@1 还没有placeholder
    "scss/at-extend-no-missing-placeholder": null,
  },
  overrides: [
    {
      "files": "**/*.scss",
      "customSyntax": "postcss-scss"
    }
  ]
};
