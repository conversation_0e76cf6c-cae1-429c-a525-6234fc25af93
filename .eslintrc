{
  "extends": ["o2team", "taro", "airbnb"],
  "parser": "babel-eslint",
  "env": {
    "browser": true,
    "node": true,
    "jest": true,
    "es6": true
  },
  "rules": {
    "react/jsx-filename-extension": [1, { "extensions": [".js", ".jsx", ".tsx"] }],
    "class-methods-use-this": "off",
    "prefer-rest-params": "off",
    "arrow-body-style": "warn",
    "taro/custom-component-children": "off",
    "taro/no-spread-in-props": "off",
    "taro/render-props": "off",
    "no-unused-vars": ["error", { "varsIgnorePattern": "Taro" }],
    "quotes": [
      "error",
      "single"
    ],
    "react/jsx-max-props-per-line": [ 1, { "maximum": 1 }],
    "max-len" : ["error", { "code" : 160 }],
    "object-curly-spacing": ["error", "always"],
    // warn
    "react/no-this-in-sfc": "warn",
    "arrow-parens": "warn",
    "no-restricted-properties": "warn",
    "no-unused-expressions": "warn",
    "no-underscore-dangle": "warn",
    "operator-linebreak": "warn",
    "react/self-closing-comp": "warn",
    "getter-return": "warn",
    "no-mixed-operators": "warn",
    "react/forbid-prop-types": "warn",
    "radix": "warn",
    "prefer-destructuring": "warn",
    "no-return-assign": "warn",
    "react/jsx-props-no-spreading": "warn", // TODO delete items
    "react/no-access-state-in-setstate": "warn", // TODO delete items
    // off
    "no-plusplus": "off",
    "no-bitwise": "off",
    "jsx-quotes": "off",
    "react/static-property-placement": "off",
    "no-param-reassign": "off",
    "react/no-array-index-key": "off",
    "quote-props": "off",
    "semi": 0,
    "space-before-function-paren": "error",
    "import/no-unresolved": "off",
    "import/no-extraneous-dependencies": "off",
    "import/extensions": "off",
    "import/no-dynamic-require": "off",
    "global-require": "off",
    "linebreak-style": "off",
    "comma-dangle": ["error", "only-multiline"],
    "consistent-return": "off",
    "react/prefer-stateless-function": "off",
    "react/react-in-jsx-scope": "off",
    "prefer-promise-reject-errors": "off",
    "no-else-return": "off",
    "react/style-prop-object": "off",
    "import/prefer-default-export": "off",
    "react/jsx-no-bind": "off",
    "generator-star-spacing": "off"
  },
  "overrides": [
    {
      "files": ["build/*.js"],
      "rules": {
        "import/no-commonjs": "off"
      }
    },
    {
      "files": ["src/components/**/*.test.js", "src/components/**/test.js"],
      "rules": {
        "taro/no-stateless-component": "off",
        "react/react-in-jsx-scope": "off",
        "react/no-find-dom-node": "off",
        "no-unused-vars": ["error", { "varsIgnorePattern": "Nerv" }]
      }
    },
    {
      "files": ["**/*.ts","**/*.tsx"],
      "parser": "@typescript-eslint/parser",
      "plugins": ["react", "typescript"],
      "env": {
        "jest": true
      },
      "parserOptions": {
        "ecmaFeatures": {
          "jsx": true
        },
        "useJSXTextNode": true,
        "project": "./tsconfig.json"
      },
      "rules": {
        "no-undef": 0,
        "no-unused-vars": 0,
        "typescript/class-name-casing": 2,
        "class-methods-use-this": "off",
        "prefer-rest-params": "off",
        "arrow-body-style": "warn",
        "react/jsx-filename-extension": [
          2,
          { "extensions": [".js", ".jsx", ".ts", ".tsx"] }
        ],
        "taro/custom-component-children": "off"
      }
    }
  ]
}
