{"miniprogramRoot": "./dist", "projectname": "id-card-ocr", "description": "intro component", "appid": "", "setting": {"urlCheck": false, "es6": false, "enhance": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": false, "useApiHook": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": false, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "libVersion": "2.14.0", "condition": {}}