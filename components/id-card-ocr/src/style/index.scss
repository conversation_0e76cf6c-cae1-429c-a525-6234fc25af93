/* write style here */
.id-card-ocr {
  .certId_tab {
  background-color: #fff;
  width: 690px;
  margin: 0 auto;
.input{
 &-custname-onlyRead {
    .at-input__input {
      opacity: 0.5;
      -webkit-text-fill-color: #333;
    }
  }
 &-certId {
    padding-right: 18px;
  }

  &-certId-onlyRead {
    padding-right: 18px;

    .bankcard_idcard_input {
      opacity: 0.5;
    }

    .at-input__customize-keyboard-input .taro-text {
      opacity: 0.5;
    }
  }
}
  &_header {
    height: 100px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    font-size: 34px;
    font-weight: 500;
    font-family: "PingFang SC";
    color: #a6a6a6;
    line-height: 43px;
    // border-bottom: 1PX solid #E5E5E5;;
        &_left{
    height: 100px;
    line-height: 100px;
    text-align: center;
    // flex: 1;
  }
    &_right{
    height: 100px;
    line-height: 100px;
    text-align: center;

    // flex: 1;
  }
    &_border_center {
      background-color: #e5e5e5;
      width: 2px;
      height: 80px;
    }
    .active {
      color: #3477ff;
    }
  }
  &_border_center {
    background-color: #e5e5e5;
    width: 650px;
    height: 2px;
    margin: 0 auto;
  }
  .has_certId {
    display: flex;
    justify-content: space-around;
    padding: 30px 0;
    .add-icon {
      width: 80px;
      height: 80px;
      background: url("https://file.mucfc.com/abf/1/0/202309/202309261425311feea9.png")
        center center / 100% 100% no-repeat;
      margin-bottom: 20px;
    }
    .front {
      width: 288px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      
      &_bg {
        position: relative;
        width: 168px;
        height: 107px;
          background: linear-gradient(#3477FF, #3477FF) left top,
      linear-gradient(#3477FF, #3477FF) left top,
      linear-gradient(#3477FF, #3477FF) right top,
      linear-gradient(#3477FF, #3477FF) right top,
      linear-gradient(#3477FF, #3477FF) right bottom,
      linear-gradient(#3477FF, #3477FF) right bottom,
      linear-gradient(#3477FF, #3477FF) left bottom,
      linear-gradient(#3477FF, #3477FF) left bottom;
    background-repeat: no-repeat;
    background-size: 4px 20px, 20px 4px;
    border-radius: 4%;
        //     .camera{

        // }
        // .reload{
        //   width: 40px;
        //   height: 40px;
        // }
      &_in{
        margin: auto;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
       width: 128px;
        height: 87px;
      }
      &_icon{
           margin: auto;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
          width: 56px;
height: 42px;
      }
      .reload{
          width: 50px;
          height: 50px;
        }

      }
    
      &_tips {

        margin-top: 20px;
        color: #a6a6a6;
        text-align: center;
        font-size: 24px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 32px;
      }
             .success{
          color: #03D560;
        }
        .fail{
        color: #FE5A5F;}
    }
    .back {
      width: 288px;


      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &_bg {
        position: relative;
        width: 168px;
        height: 107px;
          background: linear-gradient(#3477FF, #3477FF) left top,
      linear-gradient(#3477FF, #3477FF) left top,
      linear-gradient(#3477FF, #3477FF) right top,
      linear-gradient(#3477FF, #3477FF) right top,
      linear-gradient(#3477FF, #3477FF) right bottom,
      linear-gradient(#3477FF, #3477FF) right bottom,
      linear-gradient(#3477FF, #3477FF) left bottom,
      linear-gradient(#3477FF, #3477FF) left bottom;
    background-repeat: no-repeat;
    background-size: 4px 20px, 20px 4px;
    border-radius: 4%;
//         .camera{
//           width: 56px;
// height: 42px;
//         }

         &_in{
        margin: auto;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
       width: 128px;
        height: 87px;
      }
      &_icon{
           margin: auto;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
          width: 56px;
height: 42px;
      }
        .reload{
          width: 50px;
          height: 50px;
        }
      }
      &_tips {
        margin-top: 20px;
        color: #a6a6a6;
        text-align: center;
        font-size: 24px;
        font-weight: 400;
        font-family: "PingFang SC";
        line-height: 32px;
      }
             .success{
          color: #03D560;
        }
               .fail{
        color: #FE5A5F;}
    }
    }
  }
}

.has_cert_card_result{
  margin-left: 40px;
  margin-top: 30px;
&_title{
   color: #000000;
 text-align: left;
 font-size: 32px;
 font-weight: 600;
 font-family: "PingFang SC";
 line-height: 32px;
}
&_content{
  box-sizing: border-box;
  margin-top: 20px;
  width: 670px;
height: 280px;
padding: 20px 30px;
border-radius: 8px;
opacity: 1;
border: 0 solid #979797;
background: #f3f3f3;
 color: #808080;
 text-align: left;
 font-size: 22px;
 font-weight: 400;
 font-family: "PingFang SC";
 line-height: 24px;
 &_tips{
  margin-bottom: 20px;

  // margin-left: 30px;
   height: 24px;
 opacity: 1;
 color: #808080;
 text-align: left;
 font-size: 22px;
 font-weight: 400;
 font-family: "PingFang SC";
 line-height: 24px;
}
 &_inputs{
  // margin-top: 20px;
  // margin-left: 20px;
  // margin: 20px ;
  .at-input{
    padding: 20px;
  }
  .at-input__title{
    margin-right: 0;
  }
  .at-input__icon{
  margin-left: 0;}
}
}

}


  