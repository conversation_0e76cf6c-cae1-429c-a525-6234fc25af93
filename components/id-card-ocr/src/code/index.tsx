import { Component } from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUInput, MUImage } from '@mu/zui';
import { observer } from '@tarojs/mobx';
import { MUIdCardOcrProps, MUIdCardOcrState } from './types/component';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}
import { chooseImage as basicChooseImage } from '@mu/business-basic';
import { uploadPicture } from '../code/api/index.js'
const initFrontBg = 'https://file.mucfc.com/abf/1/0/202311/20231102112927451313.png';
const initBackBg = 'https://file.mucfc.com/abf/1/0/202311/20231102112927adac61.png';
const camera = 'https://file.mucfc.com/abf/1/0/202311/20231102112927c2f7bc.png';
const reLoad = 'https://file.mucfc.com/abf/1/0/202311/2023110211292714dcec.png';

@track((props: { beaconId: any; beaconContent: any; }) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'IdCardOcr', // 就是当前组件类名
}))
@observer
export default class IdCardOcr extends Component<MUIdCardOcrProps, MUIdCardOcrState> {
  public static options = {
    addGlobalClass: true
  }
  config: any = {
    styleIsolation: 'shared'
  }

  public static defaultProps: MUIdCardOcrProps;

  public static propTypes: InferProps<MUIdCardOcrProps>;
  trackedBeaconId: any;

  public constructor(props: MUIdCardOcrProps) {
    super(props);
    this.state = {
      hasChoosedFront: false, // 是否已上传身份证人像面
      hasChoosedBack: false, // 是否已上传身份证国徽面
      frontSrc: '',
      backSrc: '',
      currentIndex: 0,
      frontPhotoStatus: 'normal',
      backPhotoStatus: 'normal'
    };
    this.trackedBeaconId = this.props.trackedBeaconId;//h5在初始化赋值，否则导致拼接缺失

  }

  public async componentDidMount() {
    this.trackedBeaconId = this.props.trackedBeaconId;//小程序在componentDidMount赋值，否则导致拼接缺失
  }


  takePhoto = async (type: string) => {
    // h5用base64，小程序用tempFilePaths或tempFiles（小程序base64返回''）
    const res = await basicChooseImage({
      sourceType: ['album', 'camera'],
      eventType: '13A',
      count: 1,
    });

    let src = '';// h5和小程序的src不一样
    if (process.env.TARO_ENV === 'h5') {
      // Madp.chooseImage在各app中返回字段不一致，且安卓微信返回的base64带空格，故兼容处理
      const srcBase64 = res.base64 && res.base64.replace(/\s*/g, '');
      src = (res.src && res.src.replace(/\s*/g, '')) || srcBase64;
      if (!/base64/.test(src) && srcBase64) {
        src = srcBase64;
      }
    } else if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      src = process.env.TARO_ENV === 'alipay' ? res.tempFilePaths[0] : res.tempFiles[0].path;
    }
    // scene-照片来源
    let params = {
      frontImageUrl: src
    }
    // 回显照片，区分身份证正反面
    if (type === 'front') {
      params = {
        name: 'frontImageUrl',
        imageUrl: src,
        cardType: 'idCard',
        frontImageUrl: src
      }
      dispatchTrackEvent({
        target: this,
        event: EventTypes.BC,
        beaconId: 'takePhotoClick',
        beaconContent: {
          cus: {
            cardType: 'idCard',
            uploadType: 'front'
          }
        }
      });
    } else {
      params = {
        name: 'backImageUrl',
        imageUrl: src,
        cardType: 'idCard',
        backImageUrl: src
      }
      dispatchTrackEvent({
        target: this,
        event: EventTypes.BC,
        beaconId: 'takePhotoClick',
        beaconContent: {
          cus: {
            cardType: 'idCard',
            uploadType: 'back'
          }
        }
      });
    }
    const uploadResult = await uploadPicture(
      // 获取图片上传解析结果
      params);
    const { name, ocrCardNoResult, ocrResultType } = uploadResult || {};
    if (type === 'front') {
      this.setState({
        frontSrc: src,
        hasChoosedFront: true,
      });

      const { onChangeName, onChangeCertId } = this.props;
      if (ocrResultType === 'VALID_IDCARD') {//识别成功是有效身份证，更新状态
        this.setState({
          frontPhotoStatus: 'success'
        })
        if (uploadResult && typeof onChangeName === 'function' && typeof onChangeCertId === 'function') {//更新表单数据
          onChangeName(name);
          onChangeCertId(ocrCardNoResult);
        }
      } else {

        this.setState({
          frontPhotoStatus: 'fail'
        })
      }


    } else {
      this.setState({
        backSrc: src,
        hasChoosedBack: true,
      });
      if (ocrResultType === 'VALID_IDCARD') {
        this.setState({
          backPhotoStatus: 'success'
        })
      }
      else {

        this.setState({
          backPhotoStatus: 'fail'
        })
      }
    }
  }
  //tab切换事件
  tabChange = (index: any) => {
    const { tabOnClick } = this.props;
    this.setState({ currentIndex: index })
    if (tabOnClick && typeof tabOnClick === 'function') {
      tabOnClick(index) // 执行业务回调
    }
  }

  public render(): JSX.Element {
    const srcImgStyle = {
      width: '100%',
      height: '100%',
      display: 'block'
    };
    // if (process.env.TARO_ENV === 'h5') {
    //   srcImgStyle.height = '100%';
    // }
    const { hasChoosedFront, hasChoosedBack, frontSrc, backSrc, currentIndex, frontPhotoStatus, backPhotoStatus } = this.state;
    let frontPhotoTip = '上传身份证人像面'
    let backPhotoTip = '上传身份证国徽面'
    let frontImg = initFrontBg;
    let backImg = initBackBg;
    let frontBgIcon = hasChoosedFront ? reLoad : camera,

      backBgIcon = hasChoosedBack ? reLoad : camera;
    if (frontPhotoStatus === 'success') {
      frontPhotoTip = '上传成功';
      frontImg = frontSrc
    }
    if (frontPhotoStatus === 'fail') {
      frontPhotoTip = '请上传正确的身份证人像面';
    }
    if (backPhotoStatus === 'fail') {
      backPhotoTip = '请上传正确的身份证国徽面';
    }
    if (backPhotoStatus === 'success') {
      backPhotoTip = '上传成功';
      backImg = backSrc
    }

    const { custName, certId, isIdentityReadOnly, isneedDetectUserBhvr, checkIdentified, onChangeName, onChangeCertId, checkFormForCertId, applyOcrSwitch } = this.props;
    return <MUView className="id-card-ocr">
      <MUView className="certId_tab">
        {applyOcrSwitch && <MUView> <MUView className="certId_tab_header">
          <MUView parentId={this.trackedBeaconId} beaconId="tabChange"
            beaconContent={
              {
                cus: {
                  type: 'noIdCard'
                }
              }
            }
            onClick={() => {
              this.tabChange(0);
            }} className={`certId_tab_header_left ${!currentIndex ? 'active' : ''}`}>未带身份证</MUView>
          <MUView className="certId_tab_header_border_center" /><MUView parentId={this.trackedBeaconId} beaconId="tabChange"
            beaconContent={
              {
                cus: {
                  type: 'hasIdCard'
                }
              }
            }
            onClick={() => this.tabChange(1)} className={`certId_tab_header_right ${currentIndex ? 'active' : ''}`}>已带身份证</MUView></MUView>
          <MUView className="certId_tab_border_center" />
        </MUView>}
        <MUView className="certId_tab_content">
          {currentIndex === 0 && <MUView className="certId_tab_content_noCerId"> <MUInput name="custName" title="真实姓名" type="text" className={isIdentityReadOnly ? 'input-custname-onlyRead' : ''} placeholder="请输入你的真实姓名"
            parentId={this.trackedBeaconId} needDetectUserBhvr={isneedDetectUserBhvr} clear enableBlurTrim maxLength={50} beaconId="custNameChange"
            beaconContent={
              {
                cus: {
                  type: 'noIdCard'
                }
              }
            }
            editable={!isIdentityReadOnly} value={custName} onClick={checkIdentified} onChange={(val: any) => {
              onChangeName(val);
            }} />
            <MUInput name="certId" title="身份证号" type="idcard" placeholder="同意并输入你的身份证号" className={isIdentityReadOnly ? 'input-certId-onlyRead' : 'input-certId'} needDetectUserBhvr={isneedDetectUserBhvr} clear parentId={this.trackedBeaconId} beaconId="certIdChange"
              beaconContent={
                {
                  cus: {
                    type: 'noIdCard'
                  }
                }
              }
              editable={!isIdentityReadOnly} value={certId || ''} enableFormative onClick={checkIdentified} onChange={(val: any) => {
                onChangeCertId(val);
              }} needCloseKbOnPostFixIconClick onBlur={() => {
                checkFormForCertId();
              }} /></MUView>}
          {currentIndex === 1 && <MUView className="has_certId">
            <MUView className="front">
              <MUView className={`front_bg `}
                parentId={this.trackedBeaconId}
                beaconId='takePhotoClick' beaconContent={
                  {
                    cus: {
                      cardType: 'idCard', uploadType: 'front'
                    }
                  }
                } onClick={() => this.takePhoto('front')}>

                <MUView className="front_bg_in">
                  <MUImage src={frontImg} style={srcImgStyle} />

                </MUView>
                <MUView className={`front_bg_icon ${!hasChoosedFront ? 'camera' : 'reload'}`}>
                  <MUImage src={frontBgIcon} style={srcImgStyle} />

                </MUView>

              </MUView>
              <MUView className={`front_tips ${frontPhotoStatus}`}>{frontPhotoTip}</MUView>
            </MUView>
            <MUView className="back">
              <MUView className={`back_bg`}
                parentId={this.trackedBeaconId}
                beaconId='takePhotoClick' beaconContent={
                  {
                    cus: {
                      cardType: 'idCard', uploadType: 'back'
                    }
                  }
                } onClick={() => this.takePhoto('back')}>
                <MUView className="back_bg_in">
                  <MUImage src={backImg} style={srcImgStyle} />

                </MUView>
                <MUView className={`back_bg_icon ${!hasChoosedBack ? 'camera' : 'reload'}`}>
                  <MUImage src={backBgIcon} style={srcImgStyle} />

                </MUView>
              </MUView>
              <MUView className={`back_tips ${backPhotoStatus}`}>{backPhotoTip}</MUView>
            </MUView>
          </MUView>}

        </MUView>
      </MUView>
      {frontPhotoStatus === 'success' && currentIndex === 1 && <MUView className="has_cert_card_result">
        <MUView className="has_cert_card_result_title">
          信息识别
        </MUView>
        <MUView className="has_cert_card_result_content">
          <MUView className="has_cert_card_result_content_tips">
            识别到的信息有误，请重新拍摄上传或直接在下方修改
          </MUView>
          <MUView className="has_cert_card_result_content_inputs">
            <MUInput name="custName" title="姓名" type="text" className={isIdentityReadOnly ? 'identity4-custname-onlyRead' : ''} placeholder="请输入你的真实姓名" needDetectUserBhvr={isneedDetectUserBhvr} clear enableBlurTrim maxLength={50} parentId={this.trackedBeaconId} beaconId={'custNameChange'}
              beaconContent={
                {
                  cus: {
                    type: 'hasIdCard'
                  }
                }
              }
              editable={!isIdentityReadOnly} value={custName} onClick={checkIdentified} onChange={(val: any) => {
                onChangeName(val);
              }} />
            <MUInput name="certId" title="身份证号" type="idcard" placeholder="同意并输入你的身份证号" className={isIdentityReadOnly ? 'identity4-certId-onlyRead' : 'identity4-certId'} needDetectUserBhvr={isneedDetectUserBhvr} clear parentId={this.trackedBeaconId} beaconId={'certIdChange'}
              beaconContent={
                {
                  cus: {
                    type: 'hasIdCard'
                  }
                }
              }
              editable={!isIdentityReadOnly} value={certId || ''} enableFormative onClick={checkIdentified} onChange={(val: any) => {
                onChangeCertId(val);
              }} needCloseKbOnPostFixIconClick onBlur={() => {
                checkFormForCertId();
              }} />
          </MUView>
        </MUView>
      </MUView>}
    </MUView>
  }
}
