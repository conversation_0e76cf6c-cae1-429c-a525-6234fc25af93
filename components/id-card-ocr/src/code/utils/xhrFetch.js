import { getToken } from '@mu/dev-finger';
import ApiSign from '@mucfc.com/webapp-api-sign/dist/wxapp';
import Madp from '@mu/madp';


const sign = ApiSign.sign()._s || '';
const channel = Madp.getChannel();
const module = Madp.getModuleId();
let token = '';
getToken(true, channel).then((res) => {
  token = res;
});
// 获取时间戳
const clientTime = new Date().getTime();
const FormType = {
  multipart: 'multipart/form-data;charset=UTF-8',
};

const xhrFetch = async (url, params) => {
  Madp.showLoading();
  const method = 'post';
  const timeout = 30000;
  const headers = {
    'content-type': 'multipart/form-data;charset=UTF-8'
  };

  const {
    data,
    fileObj
  } = params;

  function formDataEncode(body) {
    const formdata = new FormData();
    Object.keys(body).forEach(key => formdata.append(key, body[key]));
    return formdata;
  }

  return new Promise(async (resolve, reject) => {
    const body = {
      reqEnvParams: JSON.stringify({
        sign,
        channel,
        appType: 'h5',
        token,
        module,
        clientTime
      }),
      data: JSON.stringify({
        ...data
      }),
      ...fileObj
    }

    const xhr = new XMLHttpRequest();
    xhr.withCredentials = true; // 解决401
    xhr.open(method, url, true);
    xhr.timeout = timeout;
    xhr.onload = () => {
      Madp.hideLoading();
      const { status } = xhr;
      if (status >= 200 && status < 300) {
        const responseText = xhr.response || xhr.responseText;
        const response = JSON.parse(responseText);
        if (response.ret === '0') {
          resolve(response.data);
        } else {
          reject(response);
        }
      } else if (status === 401) {
        reject({ status: 401 });
      } else {
        reject({ errMsg: '网络请求异常，请重试' });
      }
    };
    xhr.ontimeout = () => {
      Madp.hideLoading();
      reject({ erMsg: '请求超时，请重试' });
    };
    xhr.onerror = () => {
      Madp.hideLoading();
      reject({ errMsg: '网络请求错误，请重试' });
    };
    Object.keys(headers).forEach((name) => {
      if (headers[name] !== FormType.multipart) {
        xhr.setRequestHeader(name, headers[name]);
      }
    });
    xhr.send(formDataEncode(body));
  });
};

export default xhrFetch;
