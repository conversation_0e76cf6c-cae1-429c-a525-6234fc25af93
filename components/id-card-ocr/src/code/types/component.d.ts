import { MouseEvent, ComponentClass } from 'react';
import { CommonEventFunction } from '@tarojs/components/types/common';

import MUComponent from './base';

export interface MUIdCardOcrProps extends MUComponent {
  /**
   * 是否开启
   * @default false
   */
  isOpened?: boolean;
  /**
   * 点击按钮触发事件
   */
  onClick?: CommonEventFunction;
  trackedBeaconId?: string;
  custName?: string;
  certId?: string;
  isIdentityReadOnly?: boolean;
  isneedDetectUserBhvr?: boolean;
  applyOcrSwitch?:boolean,
  beaconIds?: string;
  checkIdentified?: void
  onChangeName?: Function;
  onChangeCertId?: Function;
  checkFormForCertId?: void;
  tabOnClick?: Function;
  /**
   * lui展位数据
   * */
  luiData?: object;
}

export interface MUIdCardOcrState {
  isWEB?: boolean;
  isWEAPP?: boolean;
  isALIPAY?: boolean;
  isShow?: boolean;
  hasChoosedFront?: boolean;
  hasChoosedBack?: boolean;
  frontSrc?: string;
  backSrc?: string;
  currentIndex?: number;
  frontPhotoStatus?: string;
  backPhotoStatus?: string;
}

declare const MUIdCardOcrComponent: ComponentClass<MUIdCardOcrProps>;

export default MUIdCardOcrComponent;
