import  { Component } from '@mu/madp';
import { InferProps } from 'prop-types';
import { MUView, MUImage} from '@mu/zui';
import { SubComp1Props, SubComp1State } from '../types/sub-comp-demo';
if(!['tt','swan','kwai'].includes(process.env.TARO_ENV||'')) {
  require('../../style/sub-comp-demo.scss');
}
  const instructionList = [
  {
    src: 'https://file.mucfc.com/cop/55/0/202109/2021092315435895a582.png',
    icon: 'correct',
    tips: '标准拍摄',
  }, {
    src: 'https://file.mucfc.com/cop/55/0/202109/2021092316144734846d.png',
    icon: 'wrong',
    tips: '边框缺失',
  }, {
    src: 'https://file.mucfc.com/cop/55/0/202109/20210923161447c759b6.png',
    icon: 'wrong',
    tips: '照片模糊',
  }, {
    src: 'https://file.mucfc.com/cop/55/0/202109/2021092316144779bcb7.png',
    icon: 'wrong',
    tips: '闪光强烈',
  }
];

export default class IdCardOcrTipsComp extends Component<SubComp1Props, SubComp1State> {
  public static options = {
    addGlobalClass: true
  }

  config:any = {
    styleIsolation: 'shared'
  }

  public static defaultProps: SubComp1Props;

  public static propTypes: InferProps<SubComp1Props>;

  public constructor(props: SubComp1Props) {
    super(props);
  }

  public render(): JSX.Element {
    return (
        <MUView className="idcard-head-instructions">
          <MUView
            className={'idcard-head-instructions-title'}
            style={{ fontSize: '16px' }}
          >
            身份证拍摄要求
          </MUView>

          <MUView className={'idcard-head-instructions-wp'}>
            {
               instructionList.map((item, idx) => (
                 <MUView
                   className={'idcard-head-instructions-block'}
                   key={`idcard_head_instructions_${idx}`}
                 >
                   <MUImage
                     src={item.src}
                     className={'idcard-head-instructions-MUImage'}
                     style="width:100%;height:46px;margin-right:13px;"
                   />
                   <MUView style={{ marginTop: '5px' }}>
                     <MUImage
                       src={item.icon === 'correct' ? 'https://file.mucfc.com/cop/55/0/202109/2021092316144787a8e8.png' : 'https://file.mucfc.com/cop/55/0/202109/20210923161447e9e168.png'}
                       className={'idcard-head-instructions-icon'}
                       style="width: 12px;height:12px;margin-right:5px;margin-bottom:-2px;position: relative"
                     />
                     <MUView
                       className={'idcard-head-instructions-tips'}
                       style={{ fontSize: '11px' }}
                     >
                       {item.tips}
                     </MUView>
                   </MUView>
                 </MUView>
               ))
             }
          </MUView>

        </MUView>
    );
  }
}
