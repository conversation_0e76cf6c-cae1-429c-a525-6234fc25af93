import { observable, action, computed } from 'mobx';
import { commonStore } from '@mu/business-basic';

class ComponentStore {
  @observable localThing = 1;
  @observable sub1 = 1;

  @action.bound
  localAction() {
    this.localThing += 1;
  }

  @action.bound
  localSubAction() {
    this.sub1 *= 2;
  }

  @computed get localComputedThing() {
    return `component computed ${this.localThing + 1}`;
  }

  @computed get localSubComputedThing() {
    return `demo subcomponent computed ${this.sub1}`;
  }
}

export default function () {
  return {
    commonStore: commonStore(),
    componentStore: new ComponentStore(),
  };
}
