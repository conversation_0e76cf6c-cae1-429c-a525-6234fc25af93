import { ComponentClass } from 'react';

import MUComponent from './base';

interface RangeItem {
  key?: string
  value?: string
}

export interface MUActionSheetPickerProps extends MUComponent {
  /**
   * 是否展示星号
   */
   ifShowStar?: boolean;

  /**
   * 标题
   */
   title?: string;

  /**
   * 输入框默认提示
   */
   placeholder?: string;

  /**
   * 范围
   */
   range: Array<RangeItem>

  /**
   * 已选选项
   */
   selectedItem: RangeItem

  /**
   * 选项点击回调
  */
  handleItemClick?: Function
}

export interface MUActionSheetPickerState {
  /**
   * 选中的 item
   */
  selectedItem: RangeItem;

  /**
   * 弹框是否显示
   */
  isActionsheetOpened: boolean;
}

declare const MUActionSheetPickerComponent: ComponentClass<MUActionSheetPickerProps>;

export default MUActionSheetPickerComponent;
