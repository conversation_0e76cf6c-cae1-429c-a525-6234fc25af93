import Madp, { Component } from '@mu/madp';
import { CommonEvent } from '@tarojs/components/types/common';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import {
  MUView,
  MUListItem,
  MUActionSheet,
  MUActionSheetItem,
  MUText
} from '@mu/zui';
import { MUActionSheetPickerProps, MUActionSheetPickerState } from './types/component';

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'ActionSheetPicker', // 就是当前组件类名
}))
export default class ActionSheetPicker extends Component<MUActionSheetPickerProps, MUActionSheetPickerState> {
  public static defaultProps: MUActionSheetPickerProps;

  public static propTypes: InferProps<MUActionSheetPickerProps>;

  public constructor(props: MUActionSheetPickerProps) {
    super(props);
    const {
      selectedItem = {
        value: '',
        key: ''
      }
    } = this.props;
    this.state = {
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      selectedItem,
      isActionsheetOpened: false
    };
  }

  private onListItemClick = () => {
    const {
      isActionsheetOpened
    } = this.state;
    this.setState({
      isActionsheetOpened: !isActionsheetOpened
    })
  };

  /**
   * 选项点击函数
   * @param index 选项下标
   */
  private onItemClick = (index) => {
    const {
      handleItemClick,
      range
    } = this.props;
    this.setState({
      selectedItem: range[index],
      isActionsheetOpened: false
    }, () => {
      handleItemClick && handleItemClick(range[index]);
    })
  }

  public render(): JSX.Element {
    const {
      isActionsheetOpened,
      selectedItem
    } = this.state;
    const {
      ifShowStar = true,
      beaconId = 'chooseDegree',
      title = '在读学历',
      placeholder = '请选择你的在读学历',
      range = [
        {
          key: '22',
          value: '大专'
        },
        {
          key: '20',
          value: '本科'
        },
        {
          key: '30',
          value: '硕士'
        },
        {
          key: '40',
          value: '博士及以上'
        }
      ]
    } = this.props;
    return (
      <MUView className="actionsheet-picker">
        <MUListItem
          ifShowStar={ifShowStar}
          arrow="mu-icon-arrow-right"
          title={title}
          content={selectedItem.value}
          renderContent={<MUText className="placeholder">{selectedItem.value ? '' : placeholder}</MUText>}
          beaconId={`${beaconId}ListItem`}
          onClick={this.onListItemClick}
        />
        <MUActionSheet
          beaconId={`${beaconId}Sheet`}
          cancelText="取消"
          isOpened={isActionsheetOpened}
          onClose={() => {
            this.setState({
              isActionsheetOpened: false
            })
          }}
        >
          {range && range.map((item, index) => (
            <MUActionSheetItem
              className={selectedItem.value === item.value ? 'brand-text' : ''}
              key={item.key}
              beaconId={`${beaconId}Item`}
              onClick={() => { this.onItemClick(index); }}
            >
              {item.value}
            </MUActionSheetItem>
          ))}
        </MUActionSheet>
      </MUView>
    );
  }
}
