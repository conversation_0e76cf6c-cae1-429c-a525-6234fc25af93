/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView } from '@mu/zui';
import BusinessComponent from '../code';
import './index.scss';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  handleItemClick = (item) => {
    console.log(item);
  };

  render() {
    const studentDegreeList = [
      {
        key: '22',
        value: '大专'
      },
      {
        key: '20',
        value: '本科'
      },
      {
        key: '30',
        value: '硕士'
      },
      {
        key: '40',
        value: '博士及以上'
      }
    ];
    return (
      <MUView
        className="component_demo_page"
      >
        <BusinessComponent
          range={studentDegreeList}
          selectedItem={{
            key: '20',
            value: '本科'
          }}
          handleItemClick={this.handleItemClick}
        />
      </MUView>
    );
  }
}
