# 面板选择

@mu/action-sheet-picker


底部弹框选项

## 预览图

![screenshot.png](http://unpkg.mucfc.com/@mu/action-sheet-picker/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/action-sheet-picker`

### 样式引入

`@import "~@mu/action-sheet-picker/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/action-sheet-picker',
            '@mu\\action-sheet-picker'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\action-sheet-picker',
            '@mu/action-sheet-picker',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/action-sheet-picker':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/action-sheet-picker')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ----  |
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | 'chooseDegree' | 埋点 |
| ifShowStar  | boolean | 否  | false | 是否展示星号 |
| title  | string | 否  | ‘在读学历’ | 输入框标题 |
| placeholder  | string | 否  | '请选择你的在读学历' | 默认提示语 |
| range  | [{key:'', value: ''}] | 否  | 0 | 选项范围 |
| selectedItem  | object | 否  | {key:'', value: ''} | 已选选项 |
| handleItemClick  | Function | 否  | () => {} | 点击选项回调 |
