import Madp, { Component } from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { Url, isMuapp } from '@mu/madp-utils'
import { MUView, MUDialog, MUButton, MUText, MUIcon, MURichText, MUImage } from '@mu/zui';
import { observer } from '@tarojs/mobx';
import { queryCreditStatus } from './api'
import { PROMOTE_PRODUCT_INFO_MAP, urlMap } from './constans'
import componentStore from './store';
import OrderGroup from '@mu/order-group'
import { getLocalConstant } from '@mu/business-basic';


@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'ApplyCaseGuideDialog', // 就是当前组件类名
}))
@observer
export default class ApplyCaseGuideDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      dialogContent: {},
      orderTime: '',
      orderState: 'no_appointment' // 预约状态为未预约
    };
    this.trackedBeaconId = props.trackedBeaconId
    this.beaconId = 'btnClick'
  }
  async componentDidMount() {
    const result = await queryCreditStatus()
    const { initComp } = componentStore
    const { callBack } = this.props
    initComp(result)
    const { custProductCode, dialogType, caseType } = componentStore
    // 获取弹窗文案
    const productInfo = PROMOTE_PRODUCT_INFO_MAP[caseType] || {};
    const dialogContent = productInfo[custProductCode] || productInfo['default'] || {};
    this.setState({ dialogContent, openCaseGuideDialog: !!dialogType, })
    dispatchTrackEvent({
      target: this,
      event: EventTypes.SO,
      beaconId: 'applyGuideDialogShow',
      beaconContent: {
        cus: {
          type: dialogType,
          caseType: caseType
        }
      }
    });
    if (callBack && typeof callBack === 'function') {
      callBack({ showFlag: !!dialogType })
    }
    if (!dialogType) { // 不弹窗时，触发关闭里的回调事件，通知弹窗逻辑已执行结束，可继续判断后续弹窗
      this.handleClose()
    }
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation: 'shared'
  }

  // 预约组件预约回调函数
  callbackStatus = (res) => {
    // 更新预约状态和预约时间
    this.setState({
      orderState: res.orderState,
      orderTime: res.orderTime,
    });
  }

  // 跳转借款
  handleBorrow() {
    const { availLimit } = this.props
    if (availLimit) {
      let loanUrl = getLocalConstant({ configName: 'loan', nameSpace: 'link' });
      loanUrl = `${loanUrl}?cashLoanMode=1`;
      Madp.navigateTo({
        url: loanUrl,
        useAppRouter: isMuapp()
      });
    } else {
      this.setState({ openCaseGuideDialog: false })
    }

  }

  getContentView() {
    const { dialogType } = componentStore
    if (dialogType === 'supply') {
      return this.getSupplyView()
    }
    if (dialogType === 'appointment') {
      return this.getAppointmentView()
    }
    if (dialogType === 'continue') {
      return this.getContinueView()
    }

  }
  getSupplyView() {
    return (
      <MUView className="supply-content">
        <MUText className="supply-item">
          <MUImage className='check-img' src='https://file.mucfc.com/abf/1/25/202408/20240802153925fcdff8.png' mode='widthFix' />
          <MUText>完善信息，加速审批</MUText>
        </MUText>
        <MUText className="supply-item">
          <MUImage className='check-img' src='https://file.mucfc.com/abf/1/25/202408/20240802153925fcdff8.png' mode='widthFix' />
          <MUText>招行与联通共同组建，安全可靠</MUText>
        </MUText>
      </MUView>
    )

  }

  getAppointmentView() {
    const { caseType } = componentStore;
    const { orderTime, orderState } = this.state;
    const { availLimit } = this.props;
    let type = '';
    // 业务类型不同，入参不同
    switch (caseType) {
      case 'apply':
        type = 'APPLY_EXPERT';
        break;
      case 'adjLimit':
        type = 'LIMIT_EXPERT';
        break;
      case 'unFreeze':
        type = 'CONTROL_EXPERT';
        break;
    }
    return (
      <MUView className="apply-case-guide-dialog-content">
        {
          orderState === 'appointment' ? <MUView className="order-success">
            <MUIcon value="success" size="60" className="order-sucess__icon" />
            <MUView className="order-success__title">预约成功</MUView>
            <MUView className="order-success__desc">您已预约{orderTime}的审批专家服务</MUView>
            <MUButton
              type="primary"
              className="order-success__btn"
              onClick={this.handleBorrow}
              beaconId='LoanBtn'
            >
              {availLimit ? '先借一笔' : '我知道了'}
            </MUButton>
          </MUView> :
            <OrderGroup
              orderCaseType={type}
              callbackStatus={this.callbackStatus.bind(this)}
              beaconPageId={this.trackedBeaconId}
            />
        }
      </MUView>
    )
  }

  getContinueView() {
    const { dialogContent } = this.state
    const { caseType, activateLimit } = componentStore
    const { availLimit } = this.props
    let btnText = ''
    if (caseType === 'unFreeze') {
      btnText = ""
      this.beaconId = 'goUnfreeze'
      return (
        <MUView className='apply-case-guide-dialog-content unfreeze-content'>
          {
            (activateLimit || availLimit) &&
            <MUView>
              额度可恢复至<MUText className="number">{(parseFloat(activateLimit || availLimit)).toLocaleString('en-US')}</MUText>元
            </MUView>
          }
        </MUView>
      )
    }
    if (caseType === 'adjLimit') {
      btnText = "去提额降息"
      return (
        <MUView className='apply-case-guide-dialog-content adjLimit-content'>
          <MUView>
            <MUView className="apply-case-guide-dialog-content_number">{dialogContent.maxLimit && dialogContent.maxLimit.toLocaleString('en-US')}</MUView>
            <MUView className="apply-case-guide-dialog-content_label">最高提额(元)</MUView>
          </MUView>
          <MUView className="apply-case-guide-dialog-content_line" />
          <MUView>
            <MUView className="apply-case-guide-dialog-content_number">{dialogContent.maxPrice}</MUView>
            <MUView className="apply-case-guide-dialog-content_label">最多降息</MUView>
          </MUView>
        </MUView>
      )
    }
    if (caseType === 'apply') {
      return (
        <MUView className="supply-content">
          <MUText className="supply-item">
            <MUImage className='check-img' src='https://file.mucfc.com/abf/1/25/202408/20240802153925fcdff8.png' mode='widthFix' />
            <MUText>线上申请无抵押</MUText>
          </MUText>
          <MUText className="supply-item">
            <MUImage className='check-img' src='https://file.mucfc.com/abf/1/25/202408/20240802153925fcdff8.png' mode='widthFix' />
            <MUText>招行与联通共同组建，安全可靠</MUText>
          </MUText>
        </MUView>
      )
    }
  }


  onBtnClick() {
    const { mapCode, dialogType, caseType } = componentStore;
    let params = {
      mapCode
    };
    if (dialogType === 'supply') {
      Madp.navigateTo({ url: Url.addParam(urlMap.supplyUrl, params), useAppRouter: isMuapp() });
      return;
    } else if (dialogType === 'continue' && caseType === 'apply') {
      // 申请续案
      Madp.navigateTo({ url: Url.addParam(urlMap.applyUrl, params), useAppRouter: isMuapp() });
      return;
    } else if (dialogType === 'continue' && caseType === 'unfreeze') {
      // 解管控
      params.busiType = caseType;
      Madp.navigateTo({ url: Url.addParam(urlMap.adjustUrl, params), useAppRouter: isMuapp() });
      return;
    }
    // 提额降息
    Madp.navigateTo({ url: Url.addParam(urlMap.adjustUrl, params), useAppRouter: isMuapp() });
    return;
  }

  handleClose() {
    const { resultCallback } = this.props;
    const { orderState } = this.state;
    if (typeof resultCallback === 'function') {
      resultCallback({ orderState });
    }
  }


  render() {
    const { openCaseGuideDialog, dialogContent, } = this.state
    const { caseType, dialogInfo, dialogType } = componentStore
    const { dialogTitle, dialogDesc, btnText, bubbleText } = dialogInfo
    if (dialogContent && Object.keys(dialogContent).length === 0) {
      return <MUView><MUView /></MUView>
    }
    return (
      <MUView>
        <MUDialog className="apply-case-guide-dialog" isOpened={openCaseGuideDialog} onClose={() => this.handleClose()}>
          <MUIcon
            onClick={() => this.setState({ openCaseGuideDialog: false })}
            value="close2"
            beaconId="KycClosed"
            size={18}
            className="close-icon"
          />
          {dialogDesc && <MURichText className='apply-case-guide-dialog_desc' nodes={dialogDesc} />}
          <MUView className='apply-case-guide-dialog_title'>{dialogTitle}</MUView>
          {this.getContentView()}
          {dialogType !== 'appointment' && <MUView className="btn-area">
            <MUText className="button-bubble">{bubbleText || dialogContent.bubbleText}</MUText>
            <MUButton
              onClick={() => this.onBtnClick()}
              className='detain-btn'
              type='primary'
              beaconId='btnClick'
              parentId={this.trackedBeaconId}
              beaconContent={{ cus: { caseType, dialogType } }}
            >
              {btnText}
            </MUButton>
          </MUView>
          }
        </MUDialog>
      </MUView>
    );
  }
}
