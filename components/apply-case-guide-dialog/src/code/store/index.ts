import { observable, action, computed, } from 'mobx';
import { PROMOTE_PRODUCT_INFO_MAP } from '../constans'

class ComponentStore {
  btnTextMap = {
    'unFreeze': '去评估额度',
    'adjLimit': "去提额降息",
    'apply': '去填写'
  }

  caseTypeDesc = {
    'unFreeze': '额度评估',
    'adjLimit': "提额降息"
  }

  // businessType = {
  //   apply: '额度',
  //   adjLimit: '提额',
  //   unFreeze: '额度评估',
  //   adjPrice: '降价'
  // };
  // APPLY("APPLY", "申请"),CONTROL("CONTROL", "管控"), LIMIT("LIMIT", "调额"),PRICE("PRICE", "调价"), CREDIT("CREDIT", "资信"),
  // 新的bizType映射旧的caseType
  caseTypeMap = {
    APPLY: 'apply',
    LIMIT: 'adjLimit',
    CONTROL: 'unFreeze',
    PRICE:'adjPrice'
  };
  businessType = {
    apply: '额度',
    adjLimit: '提额',
    unFreeze: '额度评估',
    adjPrice: '降价'
};

  @observable applyState = ''

  @observable caseType = ''

  @observable custProductCode = ''

  @observable mapCode = ''

  @observable custProductPromoteName = ''

  @observable activateLimit = ''

  @observable faceCheckFlag = ''

  @observable reservationStatus = ''

  @observable applyProductInfo = {}

  @observable busiType = ''

  @action.bound
  initComp = (data) => {
    const { applyState, mapCode, bizType, activeLimitInfoList = [], custProductCode } = data || {};
    const recycleLimitInfo = activeLimitInfoList && activeLimitInfoList.filter(item => item.poolType === 'RECYCLE_FIX');
    const { activateLimit } = (recycleLimitInfo && recycleLimitInfo[0]) || {};
    this.applyState = applyState
    this.caseType = this.caseTypeMap[bizType] ||''
    this.mapCode = mapCode
    this.custProductCode = custProductCode
    this.activateLimit = activateLimit
    this.busiType = (this.businessType && this.businessType[this.caseType]) || '';
  }

  @computed get dialogType() {
    // if(!this.applyProductInfo || Object.keys(this.applyProductInfo).length === 0) // 没有在途案件
    //   return '' 
    if (this.applyState === '8') {// 引导预约
      return 'appointment'
    }
    if (this.applyState === '4') {// 引导补件
      return 'supply'
    }
    if (this.applyState === '2') { // 继续提交案件
      return 'continue'
    }
    return ''
  }

  @computed get dialogInfo() {
    const productInfo = PROMOTE_PRODUCT_INFO_MAP[this.caseType] || {};
    const content = productInfo[this.custProductCode] || productInfo['default'] || {}
    let dialogTitle = ''
    let dialogDesc = ''
    let btnText = ''
    let bubbleText = ''
    switch (this.dialogType) {
      case 'continue':
        if (this.caseType === 'apply') {
          dialogDesc = `您有一笔<span style="color:#FE8814">${this.busiType || ''}</span>申请待提交`
          bubbleText = `已有${parseInt('3000').toLocaleString('en-US')}万人完成了申请`
          dialogTitle = '完成申请 最高30万'
        }
        else {
          dialogTitle = content.title || '优质客户专享'
          dialogDesc = content.desc || ''
          bubbleText = content.bubbleText
        }
        btnText = this.btnTextMap[this.caseType]
        break
      case 'supply':
        dialogTitle = '完善资料 最高30万'
        dialogDesc = `您有一笔<span style="color:#FE8814">${this.busiType || ''}</span>申请需完善资料`
        btnText = '去完成'
        bubbleText = `超${parseInt('3000').toLocaleString('en-US')}人已完善资料`
        break
      case 'appointment':
        dialogTitle = '可预约专家加快审批'
        dialogDesc = `您有一笔<span style="color:#FE8814">${this.busiType || ''}</span>申请`
        btnText = '确认预约'
        break
      default:
        break
    }
    return { dialogTitle, dialogDesc, btnText, bubbleText }
  }

}

export default new ComponentStore()

