import Madp from '@mu/madp';
import { urlDomain, } from '@mu/business-basic';
const channel = Madp.getChannel();
export const PROMOTE_PRODUCT_INFO_MAP = {
  'adjLimit': {
    'TECP0001': {
      name: '公积金贷',
      desc: '认证公积金可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('123500').toLocaleString('en-US'),
      maxPrice: '21%',
      bubbleText: `超${parseInt('21000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0002': {
      name: '个税易贷',
      desc: '认证个税可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('119600').toLocaleString('en-US'),
      maxPrice: '20%',
      bubbleText: `超${parseInt('11000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0003': {
      name: '工资易贷',
      desc: '认证工资可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('126400').toLocaleString('en-US'),
      maxPrice: '23%',
      bubbleText: `超${parseInt('26000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0004': {
      name: '流水易贷',
      desc: '认证流水可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('147100').toLocaleString('en-US'),
      maxPrice: '23%',
      bubbleText: `超${parseInt('135000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0005': {
      name: '额度大挑战',
      desc: '认证资料可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('132700').toLocaleString('en-US'),
      maxPrice: '22%',
      bubbleText: `超${parseInt('37000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0006': {
      name: '利率大挑战',
      desc: '认证资料可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('118300').toLocaleString('en-US'),
      maxPrice: '25%',
      bubbleText: `超${parseInt('37000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0008': {
      name: '公积金贷',
      desc: '认证公积金可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('123500').toLocaleString('en-US'),
      maxPrice: '21%',
      bubbleText: `超${parseInt('21000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0009': {
      name: '个税易贷',
      desc: '认证个人所得税可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('119600').toLocaleString('en-US'),
      maxPrice: '20%',
      bubbleText: `超${parseInt('11000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0010': {
      name: '工资易贷',
      desc: '认证工资流水可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('126400').toLocaleString('en-US'),
      maxPrice: '23%',
      bubbleText: `超${parseInt('26000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0011': {
      name: '流水易贷',
      desc: '认证收入账单可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('147100').toLocaleString('en-US'),
      maxPrice: '23%',
      bubbleText: `超${parseInt('135000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0013': {
      name: '车主易贷',
      desc: '认证车产可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('131600').toLocaleString('en-US'),
      maxPrice: '22%',
      bubbleText: `超${parseInt('18000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'TECP0014': {
      name: '极速资料认证',
      desc: '认证资料可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('300000').toLocaleString('en-US'),
      maxPrice: '25%',
      bubbleText: `超${parseInt('270000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    },
    'default': {
      name: '流水易贷',
      desc: '认证资料可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: parseInt('147100').toLocaleString('en-US'),
      maxPrice: '25%',
      bubbleText: `超${parseInt('270000').toLocaleString('en-US')}人已成功提额降息`, // 超多少人已成功提额
    }
  },
  'unFreeze': {
    'JKCPD007': {
      name: '公积金、个税',
      title: '优质客户专享通道',
      desc: '认证公积金、个税可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: '',
      maxPrice: '',
      bubbleText: `超${parseInt('30000').toLocaleString('en-US')}人已通过额度评估，成功恢复额度`, // 超多少人已成功提额
    },
    'default': {
      name: '公积金、个税',
      title: '优质客户专享通道',
      desc: '认证公积金、个税可享<span style="color:#FE8814">审批专家1V1服务</span>',
      maxLimit: '',
      maxPrice: '',
      bubbleText: `超${parseInt('30000').toLocaleString('en-US')}人已通过额度评估，成功恢复额度`, // 超多少人已成功提额
    },
  },
  'apply': {
    'default': {
      name: '公积金、个税',
      title: '完成申请 最高30万',
      desc: `您有一笔申请待提交`,
      maxLimit: '',
      maxPrice: '',
      bubbleText: `已有${parseInt('3000').toLocaleString('en-US')}万人完成了申请`, // 超多少人已成功提额
    },
  }
};

export const urlMap = {
  adjustUrl: `${urlDomain}/${channel}/adjust/#/pages/index/index`,
  supplyUrl: `${urlDomain}/${channel}/apply/#/pages/manual-review/index`,
  applyUrl: `${urlDomain}/${channel}/apply/#/pages/index/index`
}

