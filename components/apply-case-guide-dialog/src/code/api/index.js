import { fetch, apiHost } from '@mu/business-basic';

export const getUserInfo = async () => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.userInformation.getUserInfo`);
    return res || {};
  } catch (e) {
    return {};
  }
};

export const getApplyInfo = async () => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.apply.apply.applyInfo`, {
      data: {
        data: {
          queryScene: '33'
        }
      }
    });
    return res || {};
  } catch (e) {
    return {};
  }
};

export const unBind = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.login.logout`, {
      data
    });
    return res;
  } catch (e) {
    return null;
  }
};
// 查询授信状态
export const queryCreditStatus = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.apply.acpt.queryCreditStatus`, {
      autoLoading: false
    });
    return res;
  } catch (error) {
  }
};
