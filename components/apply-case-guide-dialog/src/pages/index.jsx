/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView, } from '@mu/zui';
import ApplyCaseGuideDialog from '../code'
import './index.scss';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {

  render() {
    return (
      <MUView
        className="component_demo_page"
      >
        <ApplyCaseGuideDialog availLimit='100000' />
      </MUView>
    );
  }
}
