/* write style here */
.apply-case-guide-dialog {
  font-family: 'DINAlternate-Bold';

  .mu-dialog__container {
    width: 100%;
    background: linear-gradient(180deg, #ccd8f1, rgb(255, 255, 255) 20%);
    position: fixed;
    top: auto;
    bottom: 0px;
    left: 0;
    transform: none;
    padding: 40px;
    border-radius: 16px 16px 0 0;
    overflow: visible;
    box-sizing: border-box;

    .mu-dialog__content {
      padding: 0;
      overflow: visible;
      height: auto;
      margin-top: auto;
      max-height: none;
    }

    .order-group {
      background-color: transparent;
      margin-left: -40px;
      margin-right: -40px;
    }
  }

  .close-icon {
    position: absolute;
    top: 30px;
    right: 30px;
    color: #A6A6A6;
  }

  &_title {
    margin-top: 10px;
    text-align: left;
    font-size: 40px;
    font-weight: bold;
  }

  &_desc {
    text-align: left;
    font-size: 28px;
    color: #808080;
  }

  &-content {
    margin-top: 30px;

    &_number {
      width: 230px;
      font-size: 60px;
      color: #3477ff;
      font-family: 'DIN Alternate';
    }

    &_label {
      font-size: 26px;
      color: #808080;
    }

    &_line {
      height: 110px;
      border-right: 1PX solid #3477FF;
      opacity: 0.1;
    }
  }

  .supply-content {
    height: 200px;
    background-image: url('https://file.mucfc.com/abf/1/25/202408/20240802153925315391.png');
    background-repeat: no-repeat;
    background-size: contain;
    border-radius: 16px;
    margin-top: 40px;
    padding: 30px 30px 30px 0;
    display: flex;
    flex-wrap: wrap;

    .supply-item {
      height: 64px;
      font-size: 26px;
      background-color: #EBF1FE;
      margin-left: 30px;
      margin-top: 20px;
      display: flex;
      align-items: center;
      border-radius: 50px;
      padding: 0 30px 0 15px;
      text-align: left;
      flex: 0 0 auto;

      .check-img {
        width: 32px;
        height: 32px;
        margin-right: 10px;
      }
    }
  }

  .unfreeze-content {
    line-height: 200px;
    height: 250px;
    text-align: center;
    color: #A9643C;
    border-radius: 16px;
    background-image: url('https://file.mucfc.com/abf/1/25/202408/2024080215392523e4cc.png');
    background-repeat: no-repeat;
    background-size: contain;

    .number {
      font-size: 80px;
      margin: 0 5px;
      color: #FF8844;
      vertical-align: top;
    }
  }

  .adjLimit-content {
    height: 250px;
    border-radius: 16px;
    background-image: url('https://file.mucfc.com/abf/1/25/202408/20240802153925c1e42b.png');
    background-size: contain;
    background-repeat: no-repeat;
    color: #3477FF;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 60px;
    box-sizing: border-box;
  }

  .btn-area {
    position: relative;

    .button-bubble {
      position: absolute;
      top: -20px;
      right: -20px;
      z-index: 200;
      padding: 2px 12px;
      margin: 0 auto;
      border-radius: 16px 16px 16px 0;
      background: linear-gradient(270deg, #ff712f 0%, #ffa654 100%);
      color: #ffffff;
      text-align: center;
      font-size: 20px;
    }

    .detain-btn {
      border-radius: 50px;
      height: 88px;
      margin-top: 20px;
    }

    .sub-btn {
      margin-top: 20px;
      font-size: 24px;
      color: #808080;
    }
  }

  .order-success {
    padding: 0 0 20px;
    color: #333333;
    font-size: 48px;
    line-height: 48px;
    text-align: center;

    &__icon {
      margin-top: 50px;
    }

    &__title {
      margin-top: 40px;
      font-weight: bold;
      padding-bottom: 40px;
    }

    &__desc {
      height: 100px;
      color: grey;
      font-size: 38px;
      line-height: 45px;
      font-size: 40px;
      line-height: 42px;
      font-family: "DINAlternate-Bold";
      padding: 0 20px;
      margin-bottom: 50px;
    }

    &__btn {
      border-radius: 50px;
    }
  }
}