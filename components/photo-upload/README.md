# 图片上传

@mu/photo-upload



图片上传

## 预览图

预览截图，需手动替换为实际的预览图，图片名不可重命名

![screenshot.png](http://unpkg.mucfc.com/@mu/photo-upload/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/photo-upload`

### 样式引入

`@import "~@mu/photo-upload/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/photo-upload',
            '@mu\\photo-upload'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\photo-upload',
            '@mu/photo-upload',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/photo-upload':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/photo-upload')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ----  |
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | '' | 埋点 |
| sourceType  | Array<string> | 否  | ['album', 'camera'] | 照片来源 |
| chooseImage  | Function | 否  | () => {} | 点击图片上传 |
| src  | any | 否  | 默认图片 | 图片 |
| progress  | number | 否  | 0 | 图片上传进度 |
| needWaterMark  | boolean | 否  | false | 是否需要照片水印 |
| showPhotoTips  | boolean | 否  | false | 是否需要提示语 |
| eventType  | string | 否  | '13A' | 产品大类代号 |
| initPhotoTips  | string | 否  | '' | 初始化提示语 |