import { ComponentClass } from 'react';

import MUComponent from './base';
/** 图片的来源 */
// eslint-disable-next-line typescript/class-name-casing
interface sourceType {
  /** 从相册选图 */
  album;
  /** 使用相机 */
  camera;
  /** 使用前置摄像头(仅H5纯浏览器使用) */
  user;
  /** 使用后置摄像头(仅H5纯浏览器) */
  environment;
}

export interface MUPhotoUploadProps extends MUComponent {
  /**
   * 是否开启
   * @default['album', 'camera']
   */
   sourceType?: Array<keyof sourceType>;

  /**
   * 点击图片上传
   */
   chooseImage?: Function;

  /**
   * 展示的图片
   * */
   src?: string;

  /**
   * 展示的图片
   * @default 0
   * */
   progress?: number;

   /**
   * 是否需要照片水印
   * */
   needWaterMark?: boolean;

  /**
   * 是否需要照片水印
   * */
   showPhotoTips?: boolean;

   /**
   * 产品大类代号
   * */
   eventType?: string;

   /**
   * 提示语
   * */
   initPhotoTips?: string;

  /**
   * 照片类型
   * */
   cardType?: string;
}

export interface MUPhotoUploadState {
  isWEB?: boolean;
  isWEAPP?: boolean;
  isALIPAY?: boolean;
  isShow?: boolean;
  src?: any;
}

declare const MUPhotoUploadComponent: ComponentClass<MUPhotoUploadProps>;

export default MUPhotoUploadComponent;
