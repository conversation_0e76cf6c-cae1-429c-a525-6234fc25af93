import Madp, { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUText, MUImage } from '@mu/zui';
import { chooseImage as basicChooseImage } from '@mu/business-basic';
import { MUPhotoUploadProps, MUPhotoUploadState } from './types/component';
if(!['tt','swan','kwai'].includes(process.env.TARO_ENV||'')) {
  require('../style/index.scss');
}

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'PhotoUpload', // 就是当前组件类名
}))
export default class PhotoUpload extends Component<MUPhotoUploadProps, MUPhotoUploadState> {
  public static options = {
    addGlobalClass: true
  }
  config:any = {
    styleIsolation:'shared'
  }
  public static defaultProps: MUPhotoUploadProps;

  public static propTypes: InferProps<MUPhotoUploadProps>;

  _hasChoosed: boolean = false;

  trackedBeaconId: any = '';

  public constructor(props: MUPhotoUploadProps) {
    super(props);
    const { src } = this.props;
    this.state = {
      // eslint-disable-next-line react/no-unused-state, taro/duplicate-name-of-state-and-props
      src,
    };
    // eslint-disable-next-line react/destructuring-assignment
    this.trackedBeaconId = this.props.trackedBeaconId;
  }

  private getMask() {
    const {
      status,
    } = this.updateStatus();
    const {
      needWaterMark,
      beaconId,
    } = this.props;
    // 水印图标，success或has-uploaded显示
    const watermarkIcon = status === 'success' || status === 'has-uploaded'
      ? <MUView className="photo-upload_main_watermark center" />
      : null;
    // 遮罩层-none不需要，uploading遮罩层颜色更深且uploading时显示进度百分比，其他状态浅色遮罩
    const mask = (
      <MUView
        beaconId={`${beaconId}Mask`}
        onClick={this.previewImage}
        className={needWaterMark && (status === 'success' || status === 'has-uploaded') ? 'photo-upload_main_success-mask' : 'photo-upload_main_other-mask'}
      >
        {status === 'uploading' ? <MUText className="loading-text center">图片上传中...</MUText> : null}
        {needWaterMark && watermarkIcon}
      </MUView>
    );
    return status === 'none' ? null : mask
  }

  private getPhotoTips() {
    const {
      status,
    } = this.updateStatus();
    const {
      showPhotoTips,
    } = this.props;
    // 状态提示
    const isSuccess = status === 'success' || status === 'has-uploaded';
    const isFail = status === 'fail';
    const photoTips = (
      <MUView className="photo-upload_tips">
        <MUView>
          {/* 兼容小程序实现 */}
          {isSuccess && (
            <MUText className="photo-upload_tips--success">上传成功</MUText>
          )}
          {isFail && (
            <MUText className="photo-upload_tips--fail">上传失败</MUText>
          )}
        </MUView>
      </MUView>
    );
    return showPhotoTips ? photoTips : null;
  }

  // 更新状态
  private updateStatus = () => {
    const {
      progress = 0,
      src,
      tabChange
    } = this.props;
    if (tabChange) {
     this._hasChoosed=false}
    if (progress === 0 && !this._hasChoosed) {
      // 没上传过照片
      return {
        status: 'none',
        src: src || '',
      };
    } else if (progress === 0 && this._hasChoosed) {
      // 照片上传失败
      return {
        status: 'fail',
        src
      };
    } else if (progress > 0 && progress < 100 && this._hasChoosed) {
      // 正在上传照片
      return {
        status: 'uploading',
        src
      };
    } else if (progress === 100 && this._hasChoosed) {
      // 照片上传成功
      return {
        status: 'success',
        src
      };
    } else {
      // 之前上传过照片
      return {
        status: 'has-uploaded',
        src: ''
      };
    }
  }

  // 拍照
  private takePhoto = async () => {
    const {
      chooseImage = () => {},
      sourceType = ['album', 'camera'],
      eventType = '13A'
    } = this.props;
    // h5用base64，小程序用tempFilePaths或tempFiles（小程序base64返回''）
    const res = await basicChooseImage({
      sourceType,
      eventType,
      count: 1,
    });


        let src = '';//h5和小程序的src不一样
    if (process.env.TARO_ENV === 'h5') {
      // Madp.chooseImage在各app中返回字段不一致，且安卓微信返回的base64带空格，故兼容处理
      const srcBase64 = res.base64 && res.base64.replace(/\s*/g, '');
      src = (res.src && res.src.replace(/\s*/g, '')) || srcBase64;
      if (!/base64/.test(src) && srcBase64) {
        src = srcBase64;
      }
    } else if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      src = process.env.TARO_ENV === 'alipay' ? res.tempFilePaths[0] : res.tempFiles[0].path;
    }
    // scene-照片来源
    chooseImage({ src, localId: res.localId, scene: res.scene });
    this.setState({
      // eslint-disable-next-line react/no-unused-state
      src
    });
    this._hasChoosed = true;
  }

  // 点击照片主体调起相机
  private handlePhotoMainClick = () => {
    const { status } = this.updateStatus();
    if (status === 'none') {
      this.takePhoto();
    }
  }


  // 预览照片
  private previewImage = () => {
    const { src } = this.props;
    if (src) {
      Madp.previewImage({ urls: [src] });
    }
  }

  public render(): JSX.Element {
    const {
      className,
      beaconId,
      src,
      cardType,
      initPhotoTips
    } = this.props;
    const srcImgStyle = {
      width: '100%',
      height: '',
      display: 'block'
    };
    const { status } = this.updateStatus();
    if (process.env.TARO_ENV === 'h5') {
      srcImgStyle.height = '100%';
    }
        const iconClassName = status === "none" ? "add-icon" : "reload-icon";
    const iconBeaconId =
      status === "none" ? `${beaconId}PhotoChoose` : `${beaconId}PhotoReload`;
    return (
      <MUView className={`photo-upload ${className}`}>
        <MUView className="photo-upload_main">
          {/* 照片主体 */}
          <MUView
            className="photo-upload_main_img"
            parentId={this.trackedBeaconId}
            beaconId={beaconId}
            beaconContent={{
              cus: {
                cardType: cardType || ''
              }
            }}
            onClick={this.handlePhotoMainClick}
          >
            {src && (
              <MUView className="photo-upload_main_box">
                <MUView className="image">
                  <MUImage src={src} style={srcImgStyle} />
                </MUView>
                <MUView className="status">
                  <MUView
                    className={`center ${iconClassName}`}
                    onClick={e => {
                      e.stopPropagation();
                      this.takePhoto();
                    }}
                    beaconId={iconBeaconId}
                  />
                { status === "none"&& <MUText>{initPhotoTips}</MUText>}
                </MUView>
              </MUView>
            )}
          </MUView>
          {/* 遮罩层 */}
          {this.getMask()}
        </MUView>

        {/* 照片状态提示 */}
        {this.getPhotoTips()}
      </MUView>
    );
  }
}
