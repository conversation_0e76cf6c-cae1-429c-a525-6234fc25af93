export default {
  C11: {
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/20221214151307fd43d1.png')\"></div><ul><li>1. 图片请包含<span>植树编号、申请时间、种植地点</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '蚂蚁森林证书 图片样例'
    },
    pageSubTitle: '请上传蚂蚁森林证书',
    guideText: '<div class="guide-text">获取证书路径：打开支付宝，在上方搜索栏搜索“<span>蚂蚁森林</span>”，进入<span>蚂蚁森林小程序</span>，选择<span>左上角证书</span>，并选择具体<span>需要认证的证书</span>。</div>',
    pictureDirection: 'row',
    pageTitle: '上传蚂蚁森林证书',
    cardType: 'C11',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/202212141513078b2ace.png'
  },
  C10: {
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/20221214151324139ac4.png')\"></div><ul><li>1. 图片请包含<span>证书编号、姓名、捐赠金额</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '支付宝公益证书 图片样例'
    },
    pageSubTitle: '请上传支付宝公益证书',
    guideText: '<div class="guide-text">获取证书路径：打开支付宝，在上方搜索栏搜索“<span>公益</span>”，进入支付宝公益品牌，选择右下角“<span>我的</span>”进入我的页面，点击右上角“<span>证书</span>”，并选择具体需要认证的证书。</div>',
    pictureDirection: 'row',
    pageTitle: '上传支付宝公益证书',
    cardType: 'C10',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/202212141513245e43f1.png'
  },
  C02: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/202206151605135f8d36.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/20220615160513ff1d6a.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/202206151605131100db.png'
    }],
    exampleModal: {
      exampleTips: "<div class='popcontent'><p class='desc'>提交虚假证件可能影响信用</p><div class='poopImg column' style='background-image: url(https://file.mucfc.com/abf/1/25/202206/20220615160513b9018d.png)'></div><ul><li>1. 证件图片请包含<span>姓名、经营场所等信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '特许经营许可证 图片样例'
    },
    pageSubTitle: '请上传特许经营许可证',
    pictureDirection: 'column',
    pageTitle: '上传特许经营许可证',
    cardType: 'C02',
    bgImg: 'https://file.mucfc.com/abf/1/25/202206/202206151605133feb38.png'
  },
  C13: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121915463148ed97.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121915462214161d.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154622329c14.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/202212141511479c1cf8.png')\"></div><ul><li>1. 图片请包含<span>姓名、捐赠金额、捐赠日期、盖章</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '慈善捐款证明 图片样例'
    },
    pageSubTitle: '请上传慈善捐款证明',
    pictureDirection: 'row',
    pageTitle: '上传慈善捐款证明',
    cardType: 'C13',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/202212141511474b61bb.png'
  },
  C01: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/20220615161114a4fee2.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/20220615161114f03b87.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/20220615161114cd29ba.png'
    }],
    exampleModal: {
      exampleTips: "<div class='popcontent'><p class='desc'>提交虚假证件可能影响信用</p><div class='poopImg row' style='background-image: url(https://file.mucfc.com/abf/1/25/202206/20220615161114d8fd79.png)'></div><ul><li>1. 证件图片请包含<span>姓名、经营场所等信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '营业执照 图片样例'
    },
    pageSubTitle: '请上传营业执照',
    pictureDirection: 'row',
    pageTitle: '上传营业执照',
    cardType: 'C01',
    bgImg: 'https://file.mucfc.com/abf/1/25/202206/20220615161114d19217.png'
  },
  C12: {
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/20221214151147313983.png')\"></div><ul><li>1. 图片请包含<span>用户名、无偿献血时长、献血总次数、献血总量</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '电子献血证书 图片样例'
    },
    pageSubTitle: '请上传电子献血证',
    guideText: '<div class="guide-text">获取证书路径：打开支付宝，在上方搜索栏搜索“<span>全国电子无偿献血证</span>”，进入对应小程序，授权登录后进行认证。</div>',
    pictureDirection: 'row',
    pageTitle: '上传电子献血证',
    cardType: 'C12',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/2022121415114714c17f.png'
  },
  C04: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121916075940136d.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121916075901500f.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219160759e719ba.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/20221219160759389217.png')\"></div><ul><li>1. 图片请包含<span>店铺LOGO、名称</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '店面招牌 图片样例'
    },
    pageSubTitle: '请上传店铺招牌图片',
    pictureDirection: 'row',
    pageTitle: '上传店铺招牌图片',
    cardType: 'C04',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/202212191607591410f7.png'
  },
  C15: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/202212191546227bd9af.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121915462236b18c.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154622a87267.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/202212141513073c3521.png')\"></div><ul><li>1. 图片请包含<span>姓名、颁发单位、盖章</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '业委会/村委会在职证明 图片样例'
    },
    pageSubTitle: '请上传业委会/村委会在职证明',
    pictureDirection: 'row',
    pageTitle: '上传业委会/村委会在职证明',
    cardType: 'C15',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/20221214151307634571.png'
  },
  C03: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/202206151605134e7d20.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/20220615160512c315e0.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/25/202206/20220615160513160b07.png'
    }],
    exampleModal: {
      exampleTips: "<div class='popcontent'><p class='desc'>提交虚假证件可能影响信用</p><div class='poopImg row' style='background-image: url(https://file.mucfc.com/abf/1/25/202206/20220615160513143aa2.png)'></div><ul><li>1. 店铺图片请包含<span>店铺名称信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '店面门头 图片样例'
    },
    pageSubTitle: '请上传店面门头照片',
    pictureDirection: 'row',
    pageTitle: '上传店面门头照片',
    cardType: 'C03',
    bgImg: 'https://file.mucfc.com/abf/1/25/202206/20220615160512de440a.png'
  },
  C14: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121915463148ed97.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154630926037.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154631b1fe3a.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/20221214151307589314.png')\"></div><ul><li>1. 图片请包含<span>姓名、颁发单位、盖章</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '义工/志愿者证明 图片样例'
    },
    pageSubTitle: '请上传义工/志愿者证明',
    pictureDirection: 'row',
    pageTitle: '上传义工/志愿者证明',
    cardType: 'C14',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/202212141513079f09ae.png'
  },
  C06: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154630dafe7e.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/202212191546302ff199.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/202212191546304f6a67.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/202212141511476adb12.png')\"></div><ul><li>1. 图片请包含<span>店铺收款码信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '店铺收款码 图片样例'
    },
    pageSubTitle: '请上传店铺收款码',
    pictureDirection: 'row',
    pageTitle: '上传店铺收款码',
    cardType: 'C06',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/202212141511470dace4.png'
  },
  C17: {
    errTips: [[{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202309/202309111127413ae310.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202309/20230911112719ad3f20.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202309/202309111127415653b5.png'
    }], [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/2023010/2023100917285666e79e.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/2023010/20231009172856f82e12.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/2023010/202310131744142e7c83.png'
    }]],
    operationGuideContent: {
      operationGuideTitle: '如何获取电子结婚证：',
      needOperationGuide: 'true',
      operationGuideButttonText: '立即前往>>'
    },
    exampleModal: {
      exampleTips: ["<div class='popcontent'><div class='poopImg row' style='background-image: url(https://file.mucfc.com/abf/1/0/202309/202309111127410751e2.png)'></div><ul class='ul'><li class='li'>1. 证件图片请包含<span class='span'>持证人姓名、夫妻双方信息</span></li><li class='li'>2. 拍摄保持<span class='span'>清晰完整</span></li><li class='li'>3. 上传图片时请保证<span class='span'>文字方向朝上</span></li></ul>", "<div class='popcontent'><div class='poopImg row' style='background-image: url(https://file.mucfc.com/abf/1/0/2023010/2023100917285669b849.png)'></div><ul class='ul'><li class='li'>1. 证件图片请包含<span class='span'>持证人姓名、夫妻双方信息</span></li><li class='li'>2. 拍摄保持<span class='span'>清晰完整</span></li><li class='li'>3. 上传图片时请保证<span class='span'>文字方向朝上</span></li></ul>"],
      modalTitle: '结婚证 图片样例'
    },
    pageSubTitle: '请上传本人结婚证',
    introList: {
      swiperImgList: [{
        className: 'swiper-image',
        url: 'https://file.mucfc.com/abf/1/0/202401/20240105162318e269ba.png'
      }, {
        className: 'swiper-image',
        url: 'https://file.mucfc.com/abf/1/0/202401/202401051623232010be.png'
      }, {
        width: '300',
        className: 'swiper-image',
        url: 'https://file.mucfc.com/abf/1/0/2023010/20231013174414155d0f.png',
        height: '300'
      }],
      authType: 'default'
    },
    pictureDirection: 'row',
    pageTitle: '上传结婚证',
    needWaterMark: 'true',
    showPhotoTips: 'true',
    cardType: 'C17',
    bgImg: ['https://file.mucfc.com/abf/1/0/202309/20230911112741534216.png', 'https://file.mucfc.com/abf/1/0/2023010/20231009172856da8b91.png'],
    photoTips: ['上传纸质结婚证', '上传电子结婚证']
  },
  C05: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154622405e5d.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/2022121915462272eae7.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/202212191546223092ac.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/2022121415114793a598.png')\"></div><ul><li>1. 图片请包含<span>店铺门牌号信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '店铺门牌号 图片样例'
    },
    pageSubTitle: '请上传店铺门牌号',
    pictureDirection: 'row',
    pageTitle: '上传店铺门牌号',
    cardType: 'C05',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/20221214151147ed36d7.png'
  },
  C16: {
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/37/202303/202303271740222ae38a.png')\"></div><ul><li>1. 图片请包含<span>授权编码、被授权机构、姓名、证件类型、证件号码</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '公积金授权编码 图片样例'
    },
    pageSubTitle: '上传公积金授权编码截屏图片',
    guideText: '</br></br>',
    pictureDirection: 'row',
    pageTitle: '上传公积金授权编码截屏图片',
    cardType: 'C16',
    bgImg: 'https://file.mucfc.com/abf/1/37/202303/202303271804498baeeb.png'
  },
  C07: {
    errTips: [{
      errDesc: '信息缺失',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154630a308df.png'
    }, {
      errDesc: '图片模糊',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/202212191546304ce15e.png'
    }, {
      errDesc: '文字方向未朝上',
      errBg: 'https://file.mucfc.com/abf/1/0/202212/20221219154630259c15.png'
    }],
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/20221214151307b74cf4.png')\"></div><ul><li>1. 图片请包含<span>姓名、号牌号码、注册日期、发证日期</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '行驶证 图片样例'
    },
    pageSubTitle: '请上传行驶证',
    pictureDirection: 'row',
    pageTitle: '上传行驶证',
    cardType: 'C07',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/2022121415133045b07d.png'
  },
  C09: {
    exampleModal: {
      exampleTips: "<div class=\"popcontent\"><p class=\"desc\">提交虚假证件可能影响信用</p><div class=\"poopImg row\" style=\"background-image: url('https://file.mucfc.com/abf/1/0/202212/202212141513242ecafd.png')\"></div><ul><li>1. 图片请包含<span>证书编号、姓名、捐赠金额</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>",
      modalTitle: '腾讯公益证书 图片样例'
    },
    pageSubTitle: '请上传腾讯公益证书',
    guideText: '<div class="guide-text">获取证书路径：打开微信，在上方搜索栏搜索“<span>腾讯公益</span>”，进入<span>腾讯公益公众号</span>，选择左下角菜单“<span>爱心捐助</span>”->“<span>我的捐款</span>”进入个人信息页面，点击右上角“<span>公益名片</span>”。</div>',
    pictureDirection: 'row',
    pageTitle: '上传腾讯公益证书',
    cardType: 'C09',
    bgImg: 'https://file.mucfc.com/abf/1/0/202212/20221214151324757d1a.png'
  }
};