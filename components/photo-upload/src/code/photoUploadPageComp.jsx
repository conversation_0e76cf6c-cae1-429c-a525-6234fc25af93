import Madp, { Component } from '@mu/madp';
import PropTypes from 'prop-types';
import {
  MU<PERSON>iew,
  MUImage,
  MUModal,
  MURichText,
  MUTabs,
  MUDialog,
  MUSwiper,
  MUButton
} from '@mu/zui';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { uploadPicture, getConfigParams } from './api';
import PhotoUpload from './photoUpload';
import ApplyButton from './sub-comp/button';
import textPool from './constant';
if(!['tt','swan','kwai'].includes(process.env.TARO_ENV||'')) {
  require('../style/index.scss');
}

const propTypes = {
  cardType: PropTypes.string.isRequired,
  submit: PropTypes.func.isRequired,
  initComp: PropTypes.func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types, react/require-default-props
  sourceType: PropTypes.array,
  // eslint-disable-next-line react/forbid-prop-types, react/require-default-props
  initComplete: PropTypes.func
};

@track(props => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'PhotoUploadPageComp', // 就是当前组件类名
}))
class PhotoUploadPageComp extends Component {
  constructor(props) {
    super(props);
    this.state = {
      uploadImgInfo: null,
      imgSrc: '',
      exampleModalIsOpen: false,
      progress: 0,
      disableButton: true,
      uploadImgMap: {},
      current: 0,// 已带结婚证、未带结婚证tab切换栏
      isShowToast: false,
      isMarrify: false,
      initImgSrc: '',//初始化图片，用户已上传图片时切换tab栏恢复初始状态
      isShowMarriagDialog: false,
      dialogTabCurrent: 0, // 查看示例弹框tab切换
      tabChange: false,// 是否tab切换
      tabBeacon: ['noCertificate','hasCertificate'],// 切换栏对应埋点
    };
    // eslint-disable-next-line react/destructuring-assignment, react/prop-types
    this.onTipsClick = this.onTipsClick.bind(this);
    this.closeExampleModal = this.closeExampleModal.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  componentDidMount() {
    const { cardType } = this.props;
    this.trackedBeaconId = this.props.trackedBeaconId;
    if (cardType) {
      const isMarrify = cardType === 'C17';
      this.setState({ isMarrify });
      this.queryConfig();
      dispatchTrackEvent({
        target: this,
        event: EventTypes.SO,
        beaconContent: {
          cus: {
            cardType,
          }
        }
      });
    }
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation:'shared'
  }

  handleClick(value) {
    const { current, initImgSrc, progress, tabBeacon } = this.state;
    if (progress && value!==current) {//tab切换并且当前状态是已上传，回到初始状态
      this.setState({ imgSrc: initImgSrc, progress: 0, disableButton: true })
    }
    const { cardType } = this.props;
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: tabBeacon[value],
      beaconContent: {
        cus: {
          cardType: cardType
        }
      }
    });
    this.setState({
      current: value,
      tabChange: current !== value 
    });
  }
  dialogTabCurrentChange(value) {
    const tabBeacon = ['noCertificateSample','hasCertificateSample'];
    const { cardType } = this.props;
    dispatchTrackEvent({
      target: this,
      event: EventTypes.BC,
      beaconId: tabBeacon[value],
      beaconContent: {
        cus: {
          cardType: cardType
        }
      }
    });
    this.setState({
      dialogTabCurrent: value
    });
  }
  componentDidUpdate(preProps) {
    const { cardType: preCardType } = preProps;
    const { cardType } = this.props;
    if (cardType && cardType !== preCardType) {
      this.queryConfig();
    }
  }

  // 点击打开示例弹框
  onTipsClick() {
    this.setState({
      exampleModalIsOpen: true
    });
  }

  /**
   * 请求客户端开发参数
   */
  queryConfig = async () => {
    const {
      cardType,
      initComplete,
    } = this.props;
    let uploadImgInfo = {};
    const uploadImgMap = textPool;
    if (uploadImgMap && uploadImgMap[`${cardType}`]) {
      uploadImgInfo = uploadImgMap[`${cardType}`];
      this.setState({
        uploadImgInfo,
        imgSrc: uploadImgInfo.bgImg,
        initImgSrc: uploadImgInfo.bgImg
      }, () => {
        initComplete(uploadImgInfo);
      })
    } else {
      Madp.showToast({
        title: '请求配置有误，请退出重试',
        icon: 'none',
        duration: 1000
      });
    }
  };

  // 选择图片回调
  chooseImage(imgInfo) {
    this.setState({
      imgSrc: imgInfo.src,
      progress: 100,
      disableButton: false
    });
  }

  // 关闭示例弹框
  closeExampleModal() {
    this.setState({
      exampleModalIsOpen: false
    });
  }
  // 关闭结婚证示例弹窗
  closeMarriageDialog() {
    this.setState({ isShowMarriagDialog: false });
  }
  // 点击打开示例弹框
  onTipsClick() {
    const { isMarrify } = this.state;
    if (!isMarrify) {
      // 不为结婚证、打开示例弹窗
      this.setState({
        exampleModalIsOpen: true
      });
    } else {
      // 打开结婚证示例弹窗
      this.setState({ isShowMarriagDialog: true });
    }
  }

  // 点击提交照片
  async handleSubmit() {
    const {
      submit,
      cardType,
      tabList
    } = this.props;
    const { imgSrc, tabBeacon, current } = this.state;
    let beaconId = ''
    if (tabList) {
      beaconId = tabBeacon[current];
    }
    try {
    const uploadResult = await uploadPicture({// 获取图片上传解析结果
      imageUrl: imgSrc,
      cardType
    });
    if (uploadResult&&submit && typeof submit === 'function') {// 结果返回正常再提交，不正常为false
      submit(uploadResult, beaconId);
    }
    else {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.BC,
        beaconId: 'errorCardType',
        beaconContent: {
          cus: {
            uploadType: beaconId,
            cardType: cardType
          }
        }
      });
    }
    } catch (e) {
      dispatchTrackEvent({
        target: this,
        event: EventTypes.BC,
        beaconId: 'errorCardType',
        beaconContent: {
          cus: {
            uploadType: beaconId,
            cardType: cardType
          }
        }
      });
    console.log('e',e);}
  }

  render() {
    const {
      sourceType = ['album', 'camera'],
      cardType,
      tabList,
      sampleImgTablist,
      gotoClick
    } = this.props;
    const {
      uploadImgInfo,
      imgSrc,
      exampleModalIsOpen,
      progress,
      disableButton,
      current,
      isMarrify,
      isShowMarriagDialog,
      dialogTabCurrent,
      tabChange,
      tabBeacon
    } = this.state;
    const changeCurrent = current === 1 ? 0 : 1;//由于代码调换了电子结婚证和纸质结婚证顺序，但是远程配置没有更改，故通过取反实现
    const changeDialogTabCurrent = dialogTabCurrent === 1 ? 0 : 1;
    let imgUrl = imgSrc;//背景图片
    if (Array.isArray(imgSrc) && imgSrc.length > 0) {//需要根据tab切换背景图
      imgUrl = imgSrc[changeCurrent];
    }
    let sourceTypeTemp = sourceType;
    if (isMarrify && current === 0) { // 未带结婚证不需要拍照，只能相册
      sourceTypeTemp = ['album']
    }
    if (!uploadImgInfo) {
      return;
    }
    const {
      exampleModal,
      introList = {},
      operationGuideContent = {},
      showPhotoTips,
      needWaterMark,
      pageTitle,
      photoTips
    } = uploadImgInfo;// 获取图片配置信息
    const isShowPhotoTips = showPhotoTips === 'true'; // 是否需要图片提示
    const isNeedWaterMark = needWaterMark === 'true'; // 是否需要水印
    const {
      needOperationGuide,
      operationGuideTitle,
      operationGuideButttonText
    } = operationGuideContent;
    const noCertificate = current === 0; // 第一个tab选项，有特殊展示，当前为未带结婚证tab项
    const { swiperImgList } = introList; // 操作指引轮播图数据
    const exampleModalProps = {
      type: 'text',
      className: 'upload-picture-modal',
      title: exampleModal.modalTitle,
      onConfirm: this.closeExampleModal,
      onClose: this.closeExampleModal,
      content: exampleModal.exampleTips,
      beaconId: 'ExampleModal',
      parentId: this.trackedBeaconId,
      confirmText: '我知道了',
      isOpened: exampleModalIsOpen
    };
    let initPhotoTips = pageTitle;
    if (Array.isArray(imgSrc) && imgSrc.length > 0) {// 上传图片中心引导文案根据tab切换
      initPhotoTips = photoTips[changeCurrent];
    }
    let errTips = uploadImgInfo && uploadImgInfo.errTips;
    if (
      errTips
      && uploadImgInfo.errTips.length === 2
    ) {
      //错误提示图片示例 结婚证有tab切换，所有存两项数据则为需要tab切换的场景
      errTips = uploadImgInfo.errTips[changeCurrent];
    }
    return (
      <MUView className="upload-picture">
        <MUView className="upload-picture-title">
          {uploadImgInfo && uploadImgInfo.pageSubTitle}
        </MUView>
        <MUView className="upload-picture-desc">
          请上传真实信息，否则可能会影响你的信用
          <MUView
            className="upload-picture-desc-tips brand-text"
            parentId={this.trackedBeaconId}
            beaconId="ShowTips"
            onClick={this.onTipsClick}
          >
            查看示例
          </MUView>
        </MUView>
        <MUView className="upload-picture-photo">
          {tabList && tabList.length > 1 && (
            <MUView className="tab">
              <MUTabs
                current={current}
                tabList={tabList}
                parentId={this.trackedBeaconId}
                beaconId={'tabClick'}
                beaconContent={{
                  cus: {
                    cardType
                  }
                }}
                onClick={this.handleClick.bind(this)}
              />
              <MUView className="tab_recommend">推荐</MUView>
            </MUView>
            
          )}
          <PhotoUpload
            initPhotoTips={initPhotoTips}
            className={`upload-picture-${uploadImgInfo.pictureDirection}`}
            sourceType={sourceTypeTemp}
            src={imgUrl}
            progress={progress}
            chooseImage={this.chooseImage.bind(this)}
            showPhotoTips={isShowPhotoTips}
            needWaterMark={isNeedWaterMark}
            parentId={this.trackedBeaconId}
            beaconId="PhotoUpload"
            cardType={cardType}
            tabChange={tabChange}
          />
        </MUView>
        {uploadImgInfo && uploadImgInfo.guideText && (
          <MUView className="upload-picture-guide">
            <MURichText
              parentId={this.trackedBeaconId}
              beaconId="GuideText" nodes={uploadImgInfo.guideText} />
          </MUView>
        )}
        {errTips && errTips.length > 1 && (
          <MUView className="upload-picture-err">
            {errTips.map(item => (
              <MUView className="upload-picture-err-box">
                <MUImage src={item.errBg} className="upload-picture-err-img" />
                <MUView className="upload-picture-err-desc">
                  {item.errDesc}
                </MUView>
              </MUView>
            ))}
          </MUView>
        )}
        <MUView className="button-block">
          <ApplyButton
            type="primary"
            beaconId="SubmitBtn"
            parentId={this.trackedBeaconId}
            beaconContent={{
              cus: {
                uploadType:tabList&&tabBeacon[changeCurrent],
                cardType
              }
            }}
            onClick={this.handleSubmit}
            disabled={disableButton}
          >
            提交照片
          </ApplyButton>
        </MUView>
        {!noCertificate && (
          <MUView className="upload-picture-foot-desc">
            信息安全保护中，未经你授权不对外提供
          </MUView>
        )}
        {exampleModalIsOpen && <MUModal {...exampleModalProps} />}
        {/* 结婚证示例弹框 */}
        {isMarrify && sampleImgTablist && sampleImgTablist.length > 1 && (
          <MUDialog
            isOpened={isShowMarriagDialog}
            className="upload-picture-marriage-dialog"
            title={exampleModal.modalTitle}
          >
            <MUView className="marriage_tab">

              <MUTabs
                current={dialogTabCurrent}
                tabList={sampleImgTablist}
                beaconId={'sampleTabClick'}
                beaconContent={{
                  cus: {
                    cardType
                  }
                }}
                onClick={this.dialogTabCurrentChange.bind(this)}
              />
            </MUView>
            <MURichText nodes={exampleModal.exampleTips[changeDialogTabCurrent]} />
            <MUButton
              type="primary"
              className="upload-picture-marriage-dialog-button button"
              onClick={this.closeMarriageDialog.bind(this)}
            >
              我知道了
            </MUButton>
          </MUDialog>
        )}
        {/* 操作引导内容 */}
        {needOperationGuide === 'true' && noCertificate && (
          <MUView className="upload-picture-operation-guide">
            <MUView className="upload-picture-operation-guide-header">
              <MUView className="upload-picture-operation-guide-header-title">
                {operationGuideTitle}
              </MUView>
              <MUView
                className="upload-picture-operation-guide-header-button"
                onClick={gotoClick}
              >
                {operationGuideButttonText}
              </MUView>
            </MUView>
            {swiperImgList && noCertificate && (
              <MUSwiper
                className="upload-picture-operation-guide-swiper"
                indicatorColor="#999"
                indicatorActiveColor="#333"
                circular
                indicatorDots
                beaconId="SwiperVerifyIntro"
                parentId={this.trackedBeaconId}
                autoplay
                childrenNodes={swiperImgList}
              />
            )}
          </MUView>
        )}
      </MUView>
    );
  }
}

PhotoUploadPageComp.propTypes = propTypes;
export default PhotoUploadPageComp;
