import { useState, useEffect } from '@tarojs/taro';
import PropTypes from 'prop-types';
import { MUButton } from '@mu/zui';
import { dispatchTrackEvent, EventTypes } from '@mu/madp-track';

const propTypes = {
  disabled: PropTypes.bool,
  onClick: PropTypes.func.isRequired
};

// 函数式组件没有生命周期钩子，checkPropTypes会初始多触发一次，定义defaultProps避免未拿到props校验报错
const defaultProps = {
  disabled: false
};

function ApplyButton(props) {
  const {
    disabled,
    onClick,
    // eslint-disable-next-line
    children
  } = props;
  const [disabledNew, changeDisabled] = useState(disabled);

  // 小程序下会预先编译引用的组件，放外面还未拿到props，所以延后执行checkPropTypes（类式didmount）
  useEffect(() => {
    PropTypes.checkPropTypes(propTypes, props, 'prop', 'ApplyButton');
    changeDisabled(disabled);
  }, [disabled, props]);

  const click = async () => {
    try {
      changeDisabled(true);
      await onClick();
    } catch (e) {
      dispatchTrackEvent({
        target: this,
        beaconId: 'submitError',
        event: EventTypes.EV,
        beaconContent: {
          cus: {
            errMessgae: `submit error: ${e}`
          }
        }
      })
    } finally {
      changeDisabled(false);
    }
  };

  return (
    <MUButton
      {...props}
      disabled={disabledNew}
      onClick={click}
    >
      {children}
    </MUButton>
  );
}

ApplyButton.propTypes = propTypes;
ApplyButton.defaultProps = defaultProps;

export default ApplyButton;
