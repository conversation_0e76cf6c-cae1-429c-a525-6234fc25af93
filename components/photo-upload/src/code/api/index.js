import { fetch, apiHost } from '@mu/business-basic';
import ApiSign from '@mucfc.com/webapp-api-sign/dist/wxapp';
import Madp from '@mu/madp';

import { getToken } from '@mu/dev-finger';
const API_URL = {
  // 获取配置接口
  GET_CONFIG_PARAMS: `${apiHost.mgp}/?operationId=mucfc.basic.app.queryConfigParams`,
  // 图片上传接口
  UPLOAD_PICTURE: `${apiHost.mgp}/?operationId=mucfc.basic.ocr.commonCardIdentify`,
}

/**
   * 上传图片
   */
const uploadPicture = async param => {
  if (process.env.TARO_ENV === 'h5') {
    const res = await fetch(API_URL.UPLOAD_PICTURE, {
      data: {
        data: {
          ...param
        },
      },
      autoLoading: false
    });
    return res;
  } else { // 小程序以文件流形式上传，以下参考申请大头照和用户上传身份证代码
  const { imageUrl,
    cardType } = param
  const sign = ApiSign.sign()._s || '';
  const channel = Madp.getChannel();
  const devToken = await getToken(true, channel);
  const cookieStore = Madp.getStorageSync('__cookie_store__') || [];
  const cookieStr = cookieStore.map((o) => `${o.name || ''}=${o.value || ''}`).join('; ');// 解决报未登录问题
  const params = {
    url: API_URL.UPLOAD_PICTURE,
    fileType: 'image',
    name: 'imageUrl',
    filePath: imageUrl,
    header: {
      'content-type': 'multipart/form-data;charset=UTF-8'
      ,
      cookie: cookieStr,
    },
    formData: {
      reqEnvParams: JSON.stringify({
        sign,
        channel,
        appType: 'H5',
        token: devToken,
      }),
      data: JSON.stringify(
        {
          cardType,
        }
      )
    },
  };
  return new Promise((resolve) => {
    try {
      Madp.uploadFile({
        ...params,
        success: (res) => {
          const result = res.data;
          const obj = JSON.parse(result);
          const { ret, errMsg, data={} } = obj
          if (ret === '1') { //接口报错，将报错信息toast提示
            Madp.showToast({ 
              title: errMsg,
              icon: 'none',
            });
            resolve(false)
            return
          }
          resolve(data)
          console.log('图片解析成功', res);
        },
        fail(error) {
          console.log(`图片解析失败: ${error}`);
          resolve(false)
        },
      });
    } catch (e) {
      resolve(false)
      console.log('e', e);
    }
  })
  }
};

export {
  uploadPicture
}
