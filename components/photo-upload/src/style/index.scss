@import "~@mu/zui/dist/style/variables/default.scss";

.photo-upload {
  margin-bottom: $spacing-v-xxl;

  // background: red;
  &_main {
    position: relative;
    width: 480px;
    border-radius: 8px;
    box-shadow: 0 5px 20px 0 rgba(51, 51, 51, 0.1);
    padding: $spacing-v-md;
    margin: 0 auto;
    line-height: 1;

    &_img {
      width: 480px;

      &-src {
        padding: 0;
      }
    }

    &_watermark {
      background: url("https://file.mucfc.com/abf/1/0/202309/2023092614253144b123.png") center center / 100% 100% no-repeat;
      width: 200px;
      height: 200px;
    }

    &_success-mask,
    &_other-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: $spacing-h-sm;
      color: #fff;
      font-size: $font-size-xl;
    }

    &_success-mask {
      background-color: rgba(51, 51, 51, 0.3);
    }

    &_other-mask {
      background-color: rgba(255, 255, 255, 0.3);
    }

    .reload-icon {
      width: 80px;
      height: 80px;
      background: url("https://file.mucfc.com/abf/1/0/202309/20230926142531cc1bbb.png") center center / 100% 100% no-repeat;
      margin-bottom: 20px;
    }

    &_box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }

    .image {
      width: 480px;

      height: 100%;
    }

    .status {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      color: #3477ff;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      margin: auto;
      width: 330px;
      height: 330px;
      z-index: 10;
    }

    .reload {
      width: 130px;
      height: 130px;
    }

    .add-icon {
      width: 80px;
      height: 80px;
      background: url("https://file.mucfc.com/abf/1/0/202309/202309261425311feea9.png") center center / 100% 100% no-repeat;
      margin-bottom: 20px;
    }

    .loading-text {
      position: absolute;
      top: 30%;
      left: 25%;
      font-size: $font-size-h2;
      padding: 18px 36px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 36px;
      white-space: nowrap;
      z-index: 15;
    }
  }

  &_tips {
    font-size: $font-size-sm;
    text-align: center;
    margin-top: $spacing-v-md;
    color: $color-grey-0;

    &--success {
      color: $color-success;
    }

    &--fail {
      color: $color-error;
    }
  }
}

.upload-picture {
  background-color: #fff;
  height: 100%;
  flex: 1;

  &-title {
    font-family: PingFangSC-Semibold, sans-serif;
    font-weight: 600;
    font-size: 48px;
    color: #333;
    margin: 30px 0 12px 20px;
  }

  &-desc {
    font-family: PingFangSC-Regular, sans-serif;
    font-weight: 400;
    font-size: 28px;
    color: #808080;
    margin: 0 0 0 20px;

    &-tips {
      margin-left: 20px;
      color: #3477ff;
      display: inline-block;
    }
  }

  &--upload {
    margin: 60px 85px 90px;
    background-color: red;
  }

  .photo-upload__main {
    padding: 0;
  }

  &-column {
    .photo-upload__main {
      width: 380px;
      padding: 0;
    }
  }

  &-guide {
    margin: 0 40px 40px 40px;
    font-size: 24px;
    font-size: PingFangSC;
    font-weight: 400;
    line-height: 36px;
    letter-spacing: 0;
    -moz-stack-sizing: 0;
    text-align: left;
    color: rgba(128, 128, 128, 1);

    span {
      color: #f80;
    }
  }

  &-err {
    margin: 60px 96px 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    &-box {
      width: 175px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    &-img {
      width: 130px;
      height: 142px;
    }

    &-desc {
      margin-top: 24px;
      font-family: PingFangSC-Regular, sans-serif;
      text-align: center;
      font-weight: 400;
      font-size: 24px;
      color: #808080;
    }
  }

  &-foot-desc {
    font-family: PingFangSC-Regular, sans-serif;
    font-weight: 400;
    font-size: 24px;
    color: #a6a6a6;
    text-align: center;
  }

  .button-block {
    margin-top: 30px;
  }

  .popcontent {
    .desc {
      font-size: $font-size-base;
      color: $color-text-title-secondary;
      text-align: center;
      margin-bottom: $spacing-v-md;
    }

    ul,
    .ul {
      list-style: none;

      li,
      .li {
        list-style: none;
        font-size: $font-size-base;
        color: $color-text-title-secondary;

        span,
        .span {
          color: #f80;
        }
      }
    }

    .poopImg {
      margin: 0 auto $spacing-v-xl auto;
    }

    .row {
      width: 432px;
      height: 296px;
      background: no-repeat center/contain;
    }

    .column {
      width: 230px;
      height: 330px;
      background: no-repeat center/contain;
    }
  }

  .tab {
    height: 100px;
    margin-bottom: 10px;
    position: relative;
      &_recommend {
        position: absolute;
        color: white;
        font-size: 25px;
        width: 80px;
        height: 40px;
        left: 280px;
        top: -6px;
        line-height: 40px;
        text-align: center;
        border-radius: 45px 40px 40px 0;
        background-color: #FE5A5F;
      }
    .at-tabs__item-underline {
      width: 100px;
      height: 8px;
      background: #3477ff;
      border-radius: 4px;
    }
  }

  &-marriage-dialog {
    .popcontent {

      ul,
      .ul {
        list-style: none;
        text-align: left;
        padding: 0;

        li,
        .li {
          list-style: none;
          font-size: $font-size-base;
          color: $color-text-title-secondary;
          text-align: left;

          span,
          .span {
            color: #f80;
          }
        }
      }

      .poopImg {
        margin: 0 auto $spacing-v-xl auto;
      }

      .row {
        width: 432px;
        height: 296px;
        background: no-repeat center/contain;
      }

      .column {
        width: 230px;
        height: 330px;
        background: no-repeat center/contain;
      }
    }
  }

  &-operation-guide {
    margin-top: 40px;
    padding: 0 40px;

    &-header {
      display: flex;
      justify-content: space-between;

      &-title {
        font-family: PingFangSC-Medium;
        font-weight: 500;
        font-size: 34px;
        color: #333333;
        line-height: 34px;
      }

      &-button {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 30px;
        color: #3477ff;
        line-height: 30px;
      }
    }

    &-text {
      margin-top: 40px;
      height: 26px;
      margin-bottom: 50px;

      &-index {
        width: 32px;
        height: 32px;
        float: left;
        background: #3477ff;
        border-radius: 50%;
        font-family: PingFangSC-SNaNpxibold;
        font-weight: 600;
        font-size: 26px;
        color: #ffffff;
        text-align: center;
        line-height: 26px;
      }

      &-words {
        float: left;
        margin-left: 14px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 30px;
        color: #333333;
        line-height: 30px;
      }
    }

    &-swiper {
      width: 670px;
      height: 526px;
      margin: 0 auto;
      margin-top: 40px;
      margin-bottom: 60px;
      .swiper-container {
        z-index: 0;

        .swiper-wrapper {
          z-index: 0;
        }
      }
      .mu-modal__content {
  max-height: 680px;
}

.swiper-expand {
  width: 710px;
  margin: 0 auto;
  height: 750px;

  .swiper-image {
    width: 710px;
    height: 750px;
  }
}

.swiper-container {
  height: 100%;
}

.swiper-image {
  width: 100%;
  height: 526px;
}
    }
  }

  &-button {
    width: 480px;
    height: 88px;
    margin-top: 37px;
  }

  .button {
    width: 480px;
    height: 88px;
    margin-top: 37px;
  }

  .marriage_tab {
    height: 120px;
    margin-bottom: 10px;
    overflow: hidden;

    .at-tabs__item-underline {
      width: 100px;
      height: 8px;
      background: #3477ff;
      border-radius: 4px;
    }
  }

  .mu-dialog__content {
    padding-top: 0;
  }
}

