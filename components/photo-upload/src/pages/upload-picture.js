/* eslint-disable max-len */
const uploadImgMap = {
  'C01': {
    'cardType': 'C01',
    'pictureDirection': 'row',
    'pageTitle': '上传营业执照',
    'pageSubTitle': '请上传营业执照',
    'bgImg': 'https://file.mucfc.com/abf/1/25/202206/20220615161114d19217.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/20220615161114a4fee2.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/20220615161114f03b87.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/20220615161114cd29ba.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '营业执照 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/25/202206/20220615161114d8fd79.png\')"></div><ul><li>1. 工牌图片请包含<span>姓名、执业地点信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C02': {
    'cardType': 'C02',
    'pictureDirection': 'column',
    'pageTitle': '上传特许经营许可证',
    'pageSubTitle': '请上传特许经营许可证',
    'bgImg': 'https://file.mucfc.com/abf/1/25/202206/202206151605133feb38.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/202206151605135f8d36.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/20220615160513ff1d6a.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/202206151605131100db.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '特许经营许可证 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg column" style="background-image: url(\'https://file.mucfc.com/abf/1/25/202206/20220615160513b9018d.png\')"></div><ul><li>1. 工牌图片请包含<span>姓名、经营场所等信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C03': {
    'cardType': 'C03',
    'pictureDirection': 'row',
    'pageTitle': '上传店面门头照片',
    'pageSubTitle': '请上传店面门头照片',
    'bgImg': 'https://file.mucfc.com/abf/1/25/202206/20220615160512de440a.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/202206151605134e7d20.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/20220615160512c315e0.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/25/202206/20220615160513160b07.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '店面门头 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'./img/simple.png\')"></div><ul><li>1. 店铺图片请包含<span>店铺名称信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C04': {
    'cardType': 'C04',
    'pictureDirection': 'row',
    'pageTitle': '上传店铺招牌图片',
    'pageSubTitle': '请上传店铺招牌图片',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/202212191607591410f7.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121916075940136d.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121916075901500f.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219160759e719ba.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '店面招牌 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/20221219160759389217.png\')"></div><ul><li>1. 图片请包含<span>店铺LOGO、名称</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C05': {
    'cardType': 'C05',
    'pictureDirection': 'row',
    'pageTitle': '上传店铺门牌号',
    'pageSubTitle': '请上传店铺门牌号',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/20221214151147ed36d7.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154622405e5d.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121915462272eae7.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/202212191546223092ac.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '店铺门牌号 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/2022121415114793a598.png\')"></div><ul><li>1. 店铺图片请包含<span>店铺门牌号信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C06': {
    'cardType': 'C06',
    'pictureDirection': 'row',
    'pageTitle': '上传店铺收款码',
    'pageSubTitle': '请上传店铺收款码',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/202212141511470dace4.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154630dafe7e.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/202212191546302ff199.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/202212191546304f6a67.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '店铺收款码 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/202212141511476adb12.png\')"></div><ul><li>1. 图片请包含<span>店铺收款码信息</span></li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C07': {
    'cardType': 'C07',
    'pictureDirection': 'row',
    'pageTitle': '上传行驶证',
    'pageSubTitle': '请上传行驶证',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/2022121415133045b07d.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154630a308df.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/202212191546304ce15e.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154630259c15.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '行驶证 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/20221214151307b74cf4.png\')"></div><ul><li>1. 图片请包含<span>姓名、号牌号码、注册日期、发证日期</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C09': {
    'cardType': 'C09',
    'pictureDirection': 'row',
    'pageTitle': '上传腾讯公益证书',
    'pageSubTitle': '请上传腾讯公益证书',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/20221214151324757d1a.png',
    'guideText': '<div class="guide-text">获取证书路径：打开微信，在上方搜索栏搜索“<span>腾讯公益</span>”，进入<span>腾讯公益公众号</span>，选择左下角菜单“<span>爱心捐助</span>”->“<span>我的捐款</span>”进入个人信息页面，点击右上角“<span>公益名片</span>”。</div>',
    'exampleModal': {
      'modalTitle': '腾讯公益证书 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/202212141513242ecafd.png\')"></div><ul><li>1. 图片请包含<span>证书编号、姓名、捐赠金额</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C10': {
    'cardType': 'C10',
    'pictureDirection': 'row',
    'pageTitle': '上传支付宝公益证书',
    'pageSubTitle': '请上传支付宝公益证书',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/202212141513245e43f1.png',
    'guideText': '<div class="guide-text">获取证书路径：打开支付宝，在上方搜索栏搜索“<span>公益</span>”，进入支付宝公益品牌，选择右下角“<span>我的</span>”进入我的页面，点击右上角“<span>证书</span>”，并选择具体需要认证的证书。</div>',
    'exampleModal': {
      'modalTitle': '支付宝公益证书 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/20221214151324139ac4.png\')"></div><ul><li>1. 图片请包含<span>证书编号、姓名、捐赠金额</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C11': {
    'cardType': 'C11',
    'pictureDirection': 'row',
    'pageTitle': '上传蚂蚁森林证书',
    'pageSubTitle': '请上传蚂蚁森林证书',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/202212141513078b2ace.png',
    'guideText': '<div class="guide-text">获取证书路径：打开支付宝，在上方搜索栏搜索“<span>蚂蚁森林</span>”，进入<span>蚂蚁森林小程序</span>，选择<span>左上角证书</span>，并选择具体<span>需要认证的证书</span>。</div>',
    'exampleModal': {
      'modalTitle': '蚂蚁森林证书 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/20221214151307fd43d1.png\')"></div><ul><li>1. 图片请包含<span>植树编号、申请时间、种植地点</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C12': {
    'cardType': 'C12',
    'pictureDirection': 'row',
    'pageTitle': '上传电子献血证',
    'pageSubTitle': '请上传电子献血证',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/2022121415114714c17f.png',
    'guideText': '<div class="guide-text">获取证书路径：打开支付宝，在上方搜索栏搜索“<span>全国电子无偿献血证</span>”，进入对应小程序，授权登录后进行认证。</div>',
    'exampleModal': {
      'modalTitle': '电子献血证书 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/20221214151147313983.png\')"></div><ul><li>1. 图片请包含<span>用户名、无偿献血时长、献血总次数、献血总量</span>等信息</li><li>2. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C13': {
    'cardType': 'C13',
    'pictureDirection': 'row',
    'pageTitle': '上传慈善捐款证明',
    'pageSubTitle': '请上传慈善捐款证明',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/202212141511474b61bb.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121915463148ed97.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121915462214161d.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154622329c14.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '慈善捐款证明 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/202212141511479c1cf8.png\')"></div><ul><li>1. 图片请包含<span>姓名、捐赠金额、捐赠日期、盖章</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C14': {
    'cardType': 'C14',
    'pictureDirection': 'row',
    'pageTitle': '上传义工/志愿者证明',
    'pageSubTitle': '请上传义工/志愿者证明',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/202212141513079f09ae.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121915463148ed97.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154630926037.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154631b1fe3a.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '义工/志愿者证明 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/20221214151307589314.png\')"></div><ul><li>1. 图片请包含<span>姓名、颁发单位、盖章</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  'C15': {
    'cardType': 'C15',
    'pictureDirection': 'row',
    'pageTitle': '上传业委会/村委会在职证明',
    'pageSubTitle': '请上传业委会/村委会在职证明',
    'bgImg': 'https://file.mucfc.com/abf/1/0/202212/20221214151307634571.png',
    'errTips': [
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/202212191546227bd9af.png',
        'errDesc': '信息缺失'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/2022121915462236b18c.png',
        'errDesc': '图片模糊'
      },
      {
        'errBg': 'https://file.mucfc.com/abf/1/0/202212/20221219154622a87267.png',
        'errDesc': '文字方向未朝上'
      }
    ],
    'exampleModal': {
      'modalTitle': '业委会/村委会在职证明 图片样例',
      'exampleTips': '<div class="popcontent"><p class="desc">提交虚假证件可能影响信用</p><div class="poopImg row" style="background-image: url(\'https://file.mucfc.com/abf/1/0/202212/202212141513073c3521.png\')"></div><ul><li>1. 图片请包含<span>姓名、颁发单位、盖章</span>等信息</li><li>2. 拍摄保持<span>清晰完整</span></li><li>3. 上传图片时请保证<span>文字方向朝上</span></li></ul>'
    }
  },
  "C17": {
    "errTips": [
      [
        {
          "errDesc": "信息缺失",
          "errBg": "https://file.mucfc.com/abf/1/0/202309/202309111127413ae310.png"
        },
        {
          "errDesc": "图片模糊",
          "errBg": "https://file.mucfc.com/abf/1/0/202309/20230911112719ad3f20.png"
        },
        {
          "errDesc": "文字方向未朝上",
          "errBg": "https://file.mucfc.com/abf/1/0/202309/202309111127415653b5.png"
        }
      ],
      [
        {
          "errDesc": "信息缺失",
          "errBg": "https://file.mucfc.com/abf/1/0/202309/202309211535073d9021.png"
        },
        {
          "errDesc": "图片模糊",
          "errBg": "https://file.mucfc.com/abf/1/0/202309/20230921153507e8bfd2.png"
        },
        {
          "errDesc": "文字方向未朝上",
          "errBg": "https://file.mucfc.com/abf/1/0/202309/202309211535074a5344.png"
        }
      ]
    ],
    "exampleModal": {
      "exampleTips": [
        "<div class='popcontent'><div class='poopImg row' style='background-image: url(https://file.mucfc.com/abf/1/0/202309/202309111127410751e2.png)'></div><ul class='ul'><li class='li'>1. 证件图片请包含<span class='span'>持证人姓名、夫妻双方信息</span></li><li class='li'>2. 拍摄保持<span class='span'>清晰完整</span></li><li class='li'>3. 上传图片时请保证<span class='span'>文字方向朝上</span></li></ul>",
        "<div class='popcontent'><div class='poopImg row' style='background-image: url(https://file.mucfc.com/abf/1/0/202309/2023092115350726afcb.png)'></div><ul class='ul'><li class='li'>1. 证件图片请包含<span class='span'>持证人姓名、夫妻双方信息</span></li><li class='li'>2. 拍摄保持<span class='span'>清晰完整</span></li><li class='li'>3. 上传图片时请保证<span class='span'>文字方向朝上</span></li></ul>"
      ],
      "modalTitle": "结婚证 图片样例"
    },
    "pageSubTitle": "请上传本人结婚证",
    "pictureDirection": "row",
    "pageTitle": "上传结婚证",
    "photoTips": [
      "上传结婚证",
      "上传电子结婚证"
    ],
    "cardType": "C17",
    "showPhotoTips": "true",
    "needWaterMark": "true",
    "operationGuideContent": {
      "needOperationGuide": "true",
      "operationGuideTitle": "如何获取电子结婚证：",
      "operationGuideButttonText": "立即前往>>"
    },
    "bgImg": [
      "https://file.mucfc.com/abf/1/0/202309/20230911112741534216.png",
      "https://file.mucfc.com/abf/1/0/202309/2023092115350726afcb.png"
    ],
    "introList": {
      "swiperImgList": [
        {
          "className": "swiper-image",
          "url": "https://file.mucfc.com/abf/1/0/202309/202309111127199891d4.png"
        },
        {
          "className": "swiper-image",
          "url": "https://file.mucfc.com/abf/1/0/202309/20230921154255d9e23e.png"
        },
        {
          "className": "swiper-image",
          "url": "https://file.mucfc.com/abf/1/0/202309/202309211535071fb3b5.png"
        }
      ],
      "authType": "default"
    }
  }
};

export default uploadImgMap;
