/**
 * 含有lui的业务组件测试页面
 * */
import Madp, { Component } from '@mu/madp';
import { getEnv } from '@mu/madp-utils';
import { disableTrackAlert } from '@mu/madp-track';
import {
  MUView,
  MUImage,
  MUButton,
  MUModal,
  MURichText
} from '@mu/zui';
import {PhotoUpload} from '../code';
import uploadImgMap from './upload-picture';
import './index.scss';

// 手动埋点的beaconid
export const BEACON_IDS = {
  showTips: 'ShowTips',
  exampleModal: 'ExampleModal',
  submitBtn: 'SubmitBtn',
};
// 埋点的pageid
export const BEACON_PAGE_ID = 'adjustUploadPicturePage';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  constructor() {
    super();
    console.log('uploadImgMap', uploadImgMap);
    this.state = {
      isWEB: getEnv() === Madp.ENV_TYPE.WEB,
      uploadImgInfo: uploadImgMap.C15,
      imgSrc: uploadImgMap.C15.bgImg
    };
  }

  onButtonClick = () => {
    const {
      isWEB,
    } = this.state;
    if (isWEB) {
      alert('您点击了按钮！');
    } else {
      Madp.showModal({ content: '您点击了按钮！', showCancel: false });
    }
  };

  chooseImage = (src) => {
    console.log('src', src);
    console.log('上传图片成功');
    this.setState({
      imgSrc: src.src,
      progress: 100,
    });
  }

  submit = () => {
    alert('提交成功');
  }

  closeExampleModal = () => {
    this.setState({
      exampleModalIsOpen: false
    });
  }

  onTipsClick = () => {
    this.setState({
      exampleModalIsOpen: true
    });
  }

  render() {
    const {
      uploadImgInfo,
      imgSrc,
      exampleModalIsOpen,
      progress,
      disableButton
    } = this.state;
    if (!uploadImgInfo) {
      return;
    }
    const {
      exampleModal
    } = uploadImgInfo;
    const exampleModalProps = {
      type: 'text',
      className: 'upload-picture-modal',
      title: exampleModal.modalTitle,
      onConfirm: this.closeExampleModal,
      onClose: this.closeExampleModal,
      content: exampleModal.exampleTips,
      beaconId: BEACON_IDS.exampleModal,
      confirmText: '我知道了',
      isOpened: exampleModalIsOpen,
    };
    if (process.env.TARO_ENV !== 'h5') {
      exampleModalProps.content = exampleModalProps.content
        // eslint-disable-next-line no-useless-escape
        .replace(/\<span\>/g, '<span class="span">')
        // eslint-disable-next-line no-useless-escape
        .replace(/\<ul\>/g, '<div class="ul">')
        // eslint-disable-next-line no-useless-escape
        .replace(/\<li\>/g, '<div class="li">')
        // eslint-disable-next-line no-useless-escape
        .replace(/(\<\/ul\>)|(\<\/li\>)/g, '</div>');
    }
    return (
      <MUView className="upload-picture">
        <MUView className="upload-picture-title">
          {uploadImgInfo && uploadImgInfo.pageSubTitle}
        </MUView>
        <MUView className="upload-picture-desc">
          请上传真实信息，否则可能会影响你的信用，
          <MUView
            className="upload-picture-desc-tips"
            beaconId={BEACON_IDS.showTips}
            onClick={this.onTipsClick}
          >
            查看示例
          </MUView>
        </MUView>
        <MUView
          className="upload-picture-photo"
        >
          <PhotoUpload
            initPhotoTips={uploadImgInfo.pageTitle}
            className={`upload-picture-${uploadImgInfo.pictureDirection}`}
            src={imgSrc}
            progress={progress}
            chooseImage={this.chooseImage.bind(this)}
            showPhotoTips={false}
            needWaterMark={false}
          />
        </MUView>
        {uploadImgInfo && uploadImgInfo.guideText && (
        <MUView
          className="upload-picture-guide"
        >
          <MURichText nodes={uploadImgInfo.guideText} />
        </MUView>
        )}
        {uploadImgInfo && uploadImgInfo.errTips && (
        <MUView
          className="upload-picture-err"
        >
          {
            uploadImgInfo && uploadImgInfo.errTips && uploadImgInfo.errTips.map((item) => (
              <MUView className="upload-picture-err-box">
                <MUImage
                  src={item.errBg}
                  className="upload-picture-err-img"
                />
                <MUView className="upload-picture-err-desc">
                  {item.errDesc}
                </MUView>
              </MUView>
            ))
          }
        </MUView>
        )}
        <MUView className="button-block">
          <MUButton
            type="primary"
            beaconId={BEACON_IDS.submitBtn}
            onClick={this.submit.bind(this)}
            disabled={disableButton}
          >
            提交照片
          </MUButton>
        </MUView>
        <MUView
          className="upload-picture-foot-desc"
        >
          信息安全保护中，未经你授权不对外提供
        </MUView>
        {
          exampleModalIsOpen && (
            <MUModal
              {...exampleModalProps}
            />
          )
        }
      </MUView>
    );
  }
}
