/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import {
  MUView,
} from '@mu/zui';
import {
  PhotoUploadPageComp
} from '../code';
import './index.scss';

// 手动埋点的beaconid
export const BEACON_IDS = {
  showTips: 'ShowTips',
  exampleModal: 'ExampleModal',
  submitBtn: 'SubmitBtn',
};
// 埋点的pageid
export const BEACON_PAGE_ID = 'adjustUploadPicturePage';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  initComplete = (res) => {
    console.log('initComplete res', res);
  }

  submit =(res) => {
    console.log('submit res', res);
  }

  render() {
    return (
      <MUView className="upload-picture">
        <PhotoUploadPageComp
          cardType="C03"
          sourceType={['album']}
          initComplete={this.initComplete}
          submit={this.submit}
        />
      </MUView>
    );
  }
}
