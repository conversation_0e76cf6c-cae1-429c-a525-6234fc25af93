# 点击选择

@mu/click-selector



点击选择某项

## 预览图

![screenshot.png](http://unpkg.mucfc.com/@mu/click-selector/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/click-selector`

### 样式引入

`@import "~@mu/click-selector/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/click-selector',
            '@mu\\click-selector'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\click-selector',
            '@mu/click-selector',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/click-selector':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/click-selector')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ----  |
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | '' | 埋点 |
| trackedBeaconId  | string | 否  | '' | 上级埋点 |
| ifShowStar  | boolean | 否  | false | 是否展示星号 |
| ifShowIcon  | boolean | 否  | false | 是否显示 icon |
| title  | string | 否  | ‘在读学历’ | 输入框标题 |
| placeholder  | string | 否  | '请选择你的在读学历' | 默认提示语 |
| range  | [{key:'', value: ''}] | 否  | 0 | 选项范围 |
| value  | string | 否  | '' | 已选选项 |
| count  | number | 否  | 5 | 每行几个选项 |
| onClickItem  | Function | 否  | () => {} | 点击选项回调 |
| iconClick  | Function | 否  | () => {} | icon 点击选项回调 |
| infoIconIsOpen  | boolean | 否  | false | icon 弹框是否打开 |
| iconAlertConfirm  | Function | 否  | () => {} | icon 弹框确认按钮 |
| iconAlertClose  | Function | 否  | () => {} | icon 弹框关闭按钮 |
| iconAlertTitle  | string | 否  | '' | icon 弹框标题 |
| iconAlertConent  | string | 否  | '' | icon 弹框内容 |