import { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import {
  MUView, MUText, MUImage, MUModal
} from '@mu/zui';
import { themeColor } from './utils';
import { MUClickSelectorProps, MUClickSelectorState } from './types/component';
if(!['tt','swan','kwai'].includes(process.env.TARO_ENV||'')) {
  require('../style/index.scss');
}
const introImg = 'https://file.mucfc.com/abf/1/38/202301/20230117102428ebfd89.png';

const activeStyle = {
  backgroundColor: themeColor.brand,
  color: '#fff',
  borderColor: themeColor.brand,
};

@track((props) => ({
  beaconId: props.beaconId||'ClickSelector', // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'ClickSelector', // 就是当前组件类名
}))
export default class ClickSelector extends Component<MUClickSelectorProps, MUClickSelectorState> {
  public static options = {
    addGlobalClass: true
  }
  config:any = {
    styleIsolation:'shared'
  }
  public static defaultProps: MUClickSelectorProps;

  public static propTypes: InferProps<MUClickSelectorProps>;

  public render(): JSX.Element {
    const {
      className = '',
      customStyle = '',
      title = '',
      beaconId = '',
      count = 5,
      trackedBeaconId = '',
      onClickItem = () => {},
      value = '',
      range = [],
      ifShowStar = false,
      ifShowIcon = false,
      infoIconIsOpen = false,
      iconClick = () => {},
      iconAlertConfirm = () => {},
      iconAlertClose = () => {},
      iconAlertTitle = '',
      iconAlertConent = '',
      type,
      needFixWidth = true
    } = this.props;
    const optionWidth = (100 - (count - 1) * 2) / count;
    // eslint-disable-next-line max-len
    const optionMarginRight = process.env.TARO_ENV === 'h5' ? `calc(1.6vw - ${Taro.pxTransform((60 * 1) / 100)})` : `calc(1vw - ${Taro.pxTransform((60 * 1) / 100)})`;
      const style = {};
  if (needFixWidth) {
    style.width = `calc(${optionWidth}vw - ${Taro.pxTransform(60 * optionWidth / 100)})`;
  }
    return (
      <MUView
        className={`click-selector ${className}`}
        style={{ customStyle }}
      >
        {title && (
        <MUView className="click-selector__title">
          {title}
          {ifShowStar && <MUView className="click-selector__star">*</MUView>}
          {ifShowIcon && (
            <MUView
              onClick={() => { iconClick() }}
              className="click-selector__intro"
              beaconId={`${type}InfoIntroClick`}
            >
              <MUImage
                src={introImg}
                className="click-selector__intro-img"
              />
            </MUView>
          )}
        </MUView>
        )}
        <MUView className="click-selector__options">
          {(range || []).map((option, index) => (
            <MUView
              className="option"
              parentId={trackedBeaconId}
              beaconId={`${type}ClickSelector`}
              style={{
                ...style,
                'margin-right': (index + 1) % count === 0 ? '0' : optionMarginRight,
                ...(option.key === value ? activeStyle : {})
              }}
              onClick={() => { onClickItem(option.key); }}
            >
              <MUText>{option.value}</MUText> 
            </MUView>
          ))}
        </MUView>
        {
        ifShowIcon && (
          <MUModal
            type="text"
            isOpened={infoIconIsOpen}
            closeOnClickOverlay={false}
            title={iconAlertTitle}
            content={iconAlertConent}
            onConfirm={() => { iconAlertConfirm() }}
            confirmText="我知道了"
            onClose={() => { iconAlertClose() }}
          />
        )
      }
      </MUView>
    );
  }
}
