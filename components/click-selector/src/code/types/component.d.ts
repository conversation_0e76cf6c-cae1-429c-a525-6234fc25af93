import { MouseEvent, ComponentClass } from 'react';
import { CommonEventFunction } from '@tarojs/components/types/common';

import MUComponent from './base';

interface RangeItem {
  key?: string;
  value?: string;
}

export interface MUClickSelectorProps extends MUComponent {
  // 标题
  title?: string,
  // 选项列表
  range?: Array<RangeItem>,
  // 已选选项
  value?: string,
  // 点击选项回调
  onClickItem?: Function,
  // 上级埋点
  trackedBeaconId?: string,
  // 每行几个选项
  count?: number,
  // 是否展示星号
  ifShowStar?: boolean,
  // 是否展示 icon
  ifShowIcon?: boolean,
  // icon 弹框是否打开
  infoIconIsOpen?: boolean,
  // icon 点击函数
  iconClick?: Function,
  type?:string,
  // icon 弹框确认按钮
  iconAlertConfirm?: Function,
  // icon 弹框取消按钮
  iconAlertClose?: Function,
  // icon 弹框标题
  iconAlertTitle?: string,
  // icon 弹框内容
  iconAlertConent?: string,
  needFixWidth?: boolean,
}

export interface MUClickSelectorState {}

declare const MUClickSelectorComponent: ComponentClass<MUClickSelectorProps>;

export default MUClickSelectorComponent;
