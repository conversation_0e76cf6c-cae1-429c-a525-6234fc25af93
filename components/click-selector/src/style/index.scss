@import '~@mu/zui/dist/style/variables/default.scss';
@import '~@mu/zui/dist/style/mixins/index.scss';

@include weappBottomLine('click-selector', '.click-selector', 30px);

.click-selector {
  padding: 25px 30px 0;
  background: #fff;
  color: #333;
  line-height: 1;

  &__title {
    display: flex;
    font-size: $font-size-lg;
    margin: 0 0 $spacing-v-xl 0;
  }

  &__options {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    font-size: $font-size-base;

    mu-view {
      flex: 0 1 auto;
    }
  }

  &__star {
    margin: $spacing-h-xxs 0 0 $spacing-h-xxs;
    color: #FE5A5F;
  }

  &__intro {
    display: inline-block;
    width: 50px;
    height: 100%;
    vertical-align: -10%;
    text-align: center;

    &-img {
      width: 32px;
      height: 32px;
    }
  }

  .option {
    white-space: nowrap;
    text-align: center;
    margin-bottom: 25px;
    padding: 14px 0;
    border: 1PX solid $at-input-placeholder-color;
    box-sizing: border-box;
    border-radius: 40px;
    font-size: $font-size-base;
    color: $mu-default-page-desc-color;

    &:last-child {
      margin-right: 0;
    }
      .mu-text__default {
            vertical-align: baseline;
          }
  }

  &:not(:last-child) {
    @include hairline-bottom-relative($left: 30px);
  }
}
