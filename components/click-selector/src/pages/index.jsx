/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView } from '@mu/zui';
import BusinessComponent from '../code';
import './index.scss';

const range = [
  {
    'key': 'A',
    'value': '3000及以下'
  },
  {
    'key': 'B',
    'value': '3001-5000'
  },
  {
    'key': 'C',
    'value': '5001-8000'
  },
  {
    'key': 'D',
    'value': '8001-10000'
  },
  {
    'key': 'E',
    'value': '10001-20000'
  },
  {
    'key': 'F',
    'value': '20000以上'
  }
]
// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  constructor() {
    super();
    this.state = {
      value: ''
    };
  }

  itemClick = (value) => {
    this.setState({
      value
    })
  };

  onTipIconClick = () => {
    console.log('onTipIconClick')
  }

  render() {
    const { value } = this.state;
    return (
      <MUView
        className="component_demo_page"
      >
        <BusinessComponent
          ifShowStar
          title='月收入'
          count={3}
          range={range}
          value={value}
          ifShowIcon
          iconClick={this.onTipIconClick}
          onClickItem={this.itemClick}
        />
      </MUView>
    );
  }
}
