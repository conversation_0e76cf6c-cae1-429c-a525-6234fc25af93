import { MouseEvent, ComponentClass } from 'react';
import { CommonEventFunction } from '@tarojs/components/types/common';

import MUComponent from './base';

interface HospitalName {
  /** 科室名称 */
  hospitalName: string,
}

interface HospitalDepartment {
  /** 科室名称 */
  hospitalDepartment: string,
}

/**
 * 地区代码
 */
interface AreaCodeObj {
  /** 省代码 */
  provinceCode: string,
  /** 城市代码 */
  cityCode: string,
}

export interface MUHospitalGroupProps extends MUComponent {
  /**
   * 地区代码
   */
   areaValue?: AreaCodeObj;
  /**
   * 医院名
  */
   hospitalName?: string,

   /**
    * 科室名
   */
   departName?: string,

  /**
   * 学校值变动回调
   */
  handleHospitalNameChange?: Function;

  /**
   * 学校值变动回调
   */
  handleDepartNameChange?: Function;

  /**
   * 地区值变动回调
   */
  handleChooseArea?: Function;

  /**
   * 无地区、学校时，点击学校输入框提示语
   */
  emptyHospitalAreaText?: string,
}

export interface MUHospitalGroupState {
  /**
   * 地区值
  */
  areaValue?: AreaCodeObj;

  /**
   * 医院名
  */
  hospitalName?: string,

  /**
   * 科室名
  */
  departName?: string,

  /**
   * 学校名代码和名称
  */
  hospitalNameList?: Array<string>

  /**
   * 学校名列表
  */
  departmentNameList?: Array<string>

  hospitalNameAndCodeList?: Array<HospitalName>
  departNameAndCodeList?: Array<HospitalDepartment>
}

declare const MUHospitalGroupComponent: ComponentClass<MUHospitalGroupProps>;

export default MUHospitalGroupComponent;
