import Madp, { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUAddressPicker } from '@mu/zui';
import VagueSearch from '@mu/vague-search';
import { fetch, apiHost } from '@mu/business-basic';
import { findObjFromArr } from './utils';
import { MUHospitalGroupProps, MUHospitalGroupState } from './types/component';

const operationId = {
  /** 查询医院 */
  queryHospitalList: 'mucfc.apply.assist.queryHospitalList',
  /** 查询科室 */
  queryHospitalDepartmentList: 'mucfc.apply.assist.queryHospitalDepartmentList',
};

const API_URL = {
  QUERY_HOSPITAL_LIST: `${apiHost.mgp}/?operationId=${operationId.queryHospitalList}`,
  QUERY_HOSPITAL_DEPARTMENTLIST: `${apiHost.mgp}/?operationId=${operationId.queryHospitalDepartmentList}`,
};

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'HospitalGroup', // 就是当前组件类名
}))
export default class HospitalGroup extends Component<MUHospitalGroupProps, MUHospitalGroupState> {
  public static defaultProps: MUHospitalGroupProps;

  public static propTypes: InferProps<MUHospitalGroupProps>;

  timer:any = null;

  public constructor(props: MUHospitalGroupProps) {
    super(props);
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
      hospitalName = '',
      departName = ''
    } = this.props;
    this.state = {
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      areaValue,
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      hospitalName,
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      departName,
      hospitalNameAndCodeList: [],
      departNameAndCodeList: [],
      hospitalNameList: [],
      departmentNameList: [],
    };
  }

  public async componentDidMount() {
    const {
      areaValue
    } = this.props;
    let hospitalNameAndCodeList = [];
    let departNameAndCodeList = [];
    this.timer = setTimeout(async () => {
      if (areaValue && areaValue.provinceCode && areaValue.cityCode) {
        const { hospitalInfoList = [] } = await this.queryHospital({
          provinceCode: areaValue && areaValue.provinceCode,
          cityCode: areaValue.cityCode,
        }) || [];
        hospitalNameAndCodeList = hospitalInfoList.map((item) => ({
          hospitalName: item.hospitalName
        }));
      }
      // 获取当前科室列表
      const { hospitalDepartmentInfoList = [] } = await this.queryHospitalDepart() || [];
      if (hospitalDepartmentInfoList) {
        departNameAndCodeList = hospitalDepartmentInfoList.map((item) => ({
          hospitalDepartment: item.hospitalDepartment
        }));
      }
      this.setState({
        hospitalNameAndCodeList,
        departNameAndCodeList
      })
    }, 60);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }

  /**
   * 调用医院接口
   * @param {obj} data 上传的参数
   */
  private queryHospital = async (data) => {
    const params = {
      provinceCode: data.provinceCode,
      cityCode: data.cityCode,
      hospitalName: data.hospitalName
    };
    const result = await fetch(API_URL.QUERY_HOSPITAL_LIST, {
      data: {
        data: {
          ...params,
        }
      },
      autoLoading: false,
    });
    return result;
  };

  /**
   * 调用科室接口
   * @param {obj} data 上传的参数
   */
  private queryHospitalDepart = async () => {
    const result = await fetch(API_URL.QUERY_HOSPITAL_DEPARTMENTLIST, {
      data: {},
      autoLoading: false,
    });
    return result;
  };

  private onChooseArea = async (val) => {
    const {
      handleHospitalNameChange,
      handleChooseArea
    } = this.props;
    const keyArray = [['provinceCode', 'provinceName'], ['cityCode', 'cityName']];
    const area = {
      provinceCode: '',
      cityCode: ''
    };
    val.forEach((addressValue, addressValueIndex) => {
      Object.keys(addressValue).forEach((key, index) => {
        if (keyArray[addressValueIndex]) {
          area[(keyArray[addressValueIndex])[index]] = addressValue[key];
        }
      });
    });
    this.setState({
      areaValue: area,
    });
    // 获取当前地区医院列表
    const { hospitalInfoList = [] } = await this.queryHospital({
      hospitalName: '',
      provinceCode: area.provinceCode,
      cityCode: area.cityCode,
    }) || [];
    const hospitalNameAndCodeList = hospitalInfoList.map((item) => ({
      hospitalName: item.hospitalName
    }));
    const hospitalNameList = hospitalInfoList.map((item) => (item.hospitalName));
    // 更新组件内医院地区和医院名称。改变医院地区后医院名称自动清空
    this.setState({
      hospitalName: '',
      hospitalNameAndCodeList,
      hospitalNameList
    });
    // 将最新的医院地区和医院名称传给父组件
    handleChooseArea && handleChooseArea(area);
    handleHospitalNameChange && handleHospitalNameChange({ hospitalName: '', hospitalCode: '' });
  }

  private onHospitalNameChange = (val) => {
    const {
      hospitalNameAndCodeList
    } = this.state;
    const {
      handleHospitalNameChange
    } = this.props;
    // 更新组件内医院地区
    this.setState({
      hospitalName: val
    });
    const { target } = findObjFromArr(hospitalNameAndCodeList, { hospitalName: val });
    if (Object.keys(target).length === 0) {
      handleHospitalNameChange && handleHospitalNameChange({
        hospitalName: val,
      });
      return;
    }
    // 将最新的医院名称传给父组件
    handleHospitalNameChange && handleHospitalNameChange({
      hospitalName: target.hospitalName,
    });
    this.updateHospitalNameCodeList(val)
  };

  /**
   * 更新医院名称列表
   * @param {string} val 用户输入的医院名称
  */
  private updateHospitalNameCodeList = (val) => {
    const {
      hospitalNameAndCodeList = []
    } = this.state;
    const hospitalNameList = hospitalNameAndCodeList.filter(
      (item) => item.hospitalName.toLowerCase().indexOf(val || '') !== -1
    ).map((hospital) => hospital.hospitalName);
    this.setState({
      hospitalNameList
    })
  }

  /**
   * 处理科室名称更新
   * @param {string} val 科室名称
   */
  private onDepartNameChange = (val) => {
    const {
      departNameAndCodeList
    } = this.state;
    const {
      handleDepartNameChange
    } = this.props;
    this.setState({
      departName: val
    });
    const { target } = findObjFromArr(departNameAndCodeList, { hospitalDepartment: val });
    if (Object.keys(target).length === 0) {
      handleDepartNameChange && handleDepartNameChange({
        hospitalDepartment: val,
      });
      return;
    }
    // 将最新的科室名称传给父组件
    handleDepartNameChange && handleDepartNameChange({
      hospitalDepartment: target.hospitalDepartment,
    });
    this.updateDepartNameList(val);
  };

  /**
   * 更新科室列表
   * @param {string} val 用户输入的科室
  */
  private updateDepartNameList = (val) => {
    const {
      departNameAndCodeList = []
    } = this.state;
    if (val && val.length >= 3) {
      const originData = departNameAndCodeList.filter(
        (item) => item.hospitalDepartment.toLowerCase().indexOf(val || '') !== -1
      );
      const newDate = originData.map((hospital) => hospital = hospital.hospitalDepartment)
      newDate.push('其他科室');
      this.setState({
        departmentNameList: newDate
      })
      return newDate
    } else {
      this.setState({
        departmentNameList: []
      })
    }
  }

  /**
   * 处理医院名称不可点击时
   */
  private whenHospitalUneditable = () => {
    const {
      emptyHospitalAreaText = '请先选择你执业机构的所在地区'
    } = this.props;
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
    } = this.props;
    const { provinceCode, cityCode } = areaValue;
    if (!provinceCode || !cityCode) {
      Madp.showToast({
        title: emptyHospitalAreaText,
        icon: 'none',
        duration: 2000
      });
    }
  };

  public render(): JSX.Element {
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
      hospitalName,
      departName,
      hospitalNameList,
      departmentNameList
    } = this.state;
    const {
      beaconId
    } = this.props;
    const isHospitalEditable = !!(areaValue.cityCode && areaValue.provinceCode);
    return (
      <MUView className="hospital-group">
        <MUAddressPicker
          title="工作地区"
          placeholder="请选择工作所在地区"
          value={areaValue}
          maxDeep="2"
          beaconId={`${beaconId}HosptialArea`}
          onClickItem={(val) => { this.onChooseArea(val); }}
        />
        {/* 医院名称自动补全 */}
        <VagueSearch
          title="执业机构"
          placeholder="输入你的第一执业机构关键词"
          beaconId={`${beaconId}HospitalName`}
          value={hospitalName}
          editable={isHospitalEditable}
          handleClick={this.whenHospitalUneditable}
          handleChange={this.onHospitalNameChange}
          optionList={hospitalNameList}
        />
        {/* 科室 */}
        <VagueSearch
          title="科室"
          placeholder="请输入科室"
          beaconId={`${beaconId}Department`}
          value={departName}
          handleChange={this.onDepartNameChange}
          optionList={departmentNameList}
        />
      </MUView>
    );
  }
}
