/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView, MUButton } from '@mu/zui';
import BusinessComponent from '../code';
import './index.scss';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  constructor() {
    super();
    this.state = {
      hospitalName: '招联金融',
      departmentName: ''
    };
  }

  handleChooseArea = () => {
    // this.setState({
    //   area: value
    // });
  };

  updateHospitalNameCode = (value) => {
    console.log('value', value);
    this.setState({
      hospitalName: value.hospitalName
    });
  }

  updateHospitalDepartNameCode = (value) => {
    this.setState({
      departmentName: value.hospitalDepartment
    });
  }

  render() {
    const { hospitalName, departmentName } = this.state;
    console.log('hospitalName', hospitalName);

    return (
      <MUView
        className="component_demo_page"
      >
        <MUButton
          type="primary"
          onClick={this.onButtonClick}
        >
          测试页面点击
        </MUButton>
        <BusinessComponent
          hospitalName={hospitalName}
          departName={departmentName}
          handleChooseArea={this.handleChooseArea}
          handleHospitalNameChange={this.updateHospitalNameCode}
          handleDepartNameChange={this.updateHospitalDepartNameCode}
        />
      </MUView>
    );
  }
}
