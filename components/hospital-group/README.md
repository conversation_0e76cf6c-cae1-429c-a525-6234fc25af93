# 医院选择组件

@mu/hospital-group



根据地区选择医院

## 预览图

![screenshot.png](http://unpkg.mucfc.com/@mu/hospital-group/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/hospital-group`

### 样式引入

`@import "~@mu/hospital-group/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/hospital-group',
            '@mu\\hospital-group'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\hospital-group',
            '@mu/hospital-group',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/hospital-group':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/hospital-group')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ----  |
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | '' | 埋点 |
| areaValue  | object | 否  | {provinceCode: '',cityCode: ''} | 地区编码值 |
| hospitalName  | string | 否  | '' | 医院名 |
| departName  | string | 否  | ‘’ | 科室名 |
| handleChooseArea  | Function | 否  | () => {} | 选择地区回调 |
| handleHospitalNameChange  | Function | 否  | () => {} | 选择医院回调 |
| handleDepartNameChange  | Function | 否  | () => {} | 选择科室回调 |
| emptyHospitalAreaText  | string | 否  | '请先选择你执业机构的所在地区' | 无地区、学校时，点击学校输入框提示语 |