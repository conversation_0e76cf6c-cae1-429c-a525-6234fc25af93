{
  "compilerOptions": {
    "target": "es2017",
    "module": "commonjs",
    "removeComments": false,
    "preserveConstEnums": true,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "jsxFactory": "Taro.createElement",
    "noImplicitAny": false,
    "allowSyntheticDefaultImports": true,
    "outDir": "lib",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "strictNullChecks": true,
    "sourceMap": true,
    "baseUrl": ".",
    "rootDir": ".",
    "jsx": "preserve",
    "typeRoots": [
      "node_modules/@types",
      "global.d.ts",
      "src/code/types"
    ],
    "resolveJsonModule": true,
  },
  "compileOnSave": false
}
