@import "./font.scss";
/* write style here */
.cmb-unite-apply {
    font-family: PingFang SC;
    .at-drawer__content{
        overflow: inherit;
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;
    }
    &__bg {
        background: url('https://file.mucfc.com/abf/1/25/202406/20240618103808817bc4.png') no-repeat left top / contain;
        width: 750px;
        height: 1200px;
        padding-top: 310px;

        &__halfScreen {
            background: url('https://file.mucfc.com/abf/1/25/202406/20240618103808817bc4.png') no-repeat left top / cover;
            height: 940px;
        }
    }

    &__apply-card {
        background: url('https://file.mucfc.com/abf/1/25/202406/20240621115409194638.png') no-repeat left top / cover;
        border-radius: 16px;
        margin: 0 30px 0 30px;
        padding: 160px 0 50px 0;
    }

    &__content {
        height: 100%;
        overflow: auto;
    }

    &__item {
        display: flex;
        font-size: 24px;
        color: #000000;
        margin-left: 105px;

        &__text {
            font-size: 24px;
            display: flex;
            height: 34px;
        }

        &__special-text {
            color: #FF8844;
        }

        &__credit {
            height: 96px;
            line-height: 96px;
            font-size: 64px;
        }

        &__lpr {
            margin-left: 137px;
        }

        &__lpr-number {
            height: 96px;
            line-height: 96px;
            font-size: 64px;
            margin-left: 159px;
        }

        &__symbol {
            font-weight: 700;
            font-size: 48px;
            height: 96px;
            line-height: 96px;
        }
    }

    &__number {
        font-family: DIN Alternate;
        font-size: 64px;
        font-weight: bold;
    }

    &__contract {
        height: 35px;
        font-size: 22px;
        margin: 20px 0 30px 60px;
        color: #808080;

        &__text {
            display: inline-block;
        }

        &__list {
            display: inline-block;
            color: #000000;
        }
    }

    &__tips {
        height: 24px;
        font-size: 22px;
        font-weight: normal;
        margin: 40px 0 0 191px;
        color: #A6A6A6;
    }

    &__btn {
        height: 100px;
        line-height: 100px;
        border-radius: 50px;
        background: #E50012;
        font-family: 苹方-简;
        font-size: 36px;
        color: #FFFFFF;
        font-weight: 600;
        margin: 0 40px;
        text-align: center;
    }

    &__title {
        width: 100%;
        height: 100px;
        background: white;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 30px;

        &__text {
            font-size: 35px;
            font-weight: 600;
        }

        &__close {
            width: 24px;
            height: 24px;
            position: absolute;
            top: 30px;
            right: 30px;
        }
    }

    &__assure {
        display: flex;
        margin-top: 56px;
        align-items: center;

        &__icon {
            width: 28px;
            height: 32px;
            margin-right: 12px;
            margin: 0 14px 0 42px;
        }

        &__text {
            height: 32px;
            font-size: 28px;
            line-height: 32px;
            font-weight: 600;
            color: #000000;
            margin-top: 5px;
        }
    }

    &__notify {
        width: 572px;
        height: 32px;
        line-height: 32px;
        font-size: 22px;
        color: #808080;
        margin: 16px 0 0 40px;
    }

    &__bottom-icon {
        width: 363px;
        height: 98px;
        position: absolute;
        bottom: 48px;
        left: 195px;

        &__fullScreen {
            position: static;
            margin: 70px 0 0 180px;
        }
    }

    .at-button__text {
        color: white !important;
    }

    &__loan {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        &-content {
            font-size: 48px;
            font-weight: 600;
            line-height: 64px;
            margin: 40px 0 60px 0;
        }

        &-logo {
            width: 670px;
            height: 194px;
            border-radius: 16px;
            background: url('https://file.mucfc.com/abf/1/38/202501/202501161121150a2b1f.png') no-repeat left top / cover;
            display: flex;
            justify-content: center;
            align-items: center;

            &-content {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                width: 100%;

                &-img {
                    width: 48px;
                    height: 48px;
                }

                &-text {
                    line-height: 34px;
                    font-size: 24px;
                    font-weight: 400;
                    margin-top: 25px;
                }
            }
        }

        &-contract {
            line-height: 24px;
            font-size: 22px;
            font-weight: 400;
            display: flex;
            margin: 60px 0 50px 0;


            &-text {
                color: #808080;
            }

            &-list {
                color: black;
            }
        }

        &-btn {
            width: 670px;
            height: 100px;
            border-radius: 50px;
        }
    }
}
