import { fetch, apiHost } from '@mu/business-basic';

export const getUserInfo = async data => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.infoMaintain.getCustDataStatus`, {
      data: {
        data
      },
      autoLoading: false
    });
    return res || {};
  } catch (e) {
    return {};
  }
};

/**
 * 获取商户信息
 */
export const getMerchantInfo = async () => {
  const merchantInfo = await fetch(`${apiHost.mgp}?operationId=mucfc.loan.query.queryMerchantInfo`, {
    method: 'POST',
    autoLoading: false
  });
  return merchantInfo;
};

/**
 * 查询授信产品系统合同协议 介绍页使用
 */
export const queryContractInfoList = async () => {
  const res = await fetch(`${apiHost.mgp}?operationId=mucfc.apply.assist.queryContractInfoList`, {
    method: 'POST',
    autoLoading: false
  });
  return res;
};

/**
 * 查询授信产品系统合同协议 介绍页使用
 */
export const queryContractTemplate = async data => {
  const { contractList } = await fetch(`${apiHost.mgp}?operationId=mucfc.user.contract.getContractInfo`, {
    method: 'POST',
    autoLoading: false,
    data: {
      data
    },
  });
  const contractHtml = contractList && contractList[0] && contractList[0].htmlFile;
  return contractHtml;
};

/**
 * 查询案件信息
 */
export const getApplyInfo = async data => {
  const res = await fetch(`${apiHost.mgp}?operationId=mucfc.apply.apply.applyInfo`, {
    method: 'POST',
    autoLoading: false,
    data: {
      data
    },
  });
  return res;
};