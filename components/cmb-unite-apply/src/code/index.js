import Madp, { Component } from '@mu/madp';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { MUView, MUImage, MUModal, MUDrawer, MUButton } from '@mu/zui';
import dayjs from 'dayjs';
import { observer } from '@tarojs/mobx';
import { AgreementDrawer } from '@mu/agreement';
import { cmbUniteApplyImg } from "./utils/constants";
import { urlDomain } from "@mu/business-basic";
import { getUserInfo, getMerchantInfo, queryContractTemplate, queryContractInfoList, getApplyInfo } from "./api/index";
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}

const mapCode = 'ef645b1336cbb05f';

@track({ event: EventTypes.PO }, {
  pageId: 'CmbUniteApplyDrawer',
  dispatchOnMount: true,
})
@observer
export default class CmbUniteApply extends Component {

  constructor(props) {
    super(props);
    this.state = {
      contracts: [], // 合同配置接口获取的合同
      contractText: '', // 协议拼接文案
      countingNum: 5, // 倒计时时间和强制阅读时间保持一致，
      newContractList: '',
      hasForceReadContract: false, // 是否有强读协议
      showDetainDialog: false, // 是否弹出挽留弹窗
      readDuration: '', // 协议强读时间
      showContractsModal: false, //控制展示协议半屏弹窗
      queryStatusInfo: {}, // 用户补全资料信息
      isModalOpen: false, // 控制跳转借款弹窗打开
      contractTextClick: false, // 是否为通过点击协议拉起协议阅读弹窗
      showContractBtn: true, // 展示阅读并同意协议按钮
      modalTextContent: {
        modalTitle: '',
        modalContent: '',
        modalConfirmText: '',
        type: '',
        isJumpLoan: false
      }, // 弹窗提示内容
      isCmbDialogOpen: true,
      userInfoStatus: {} // 客户资料状态
    };
    this.forceReadContractParams = []; // 强制阅读协议配置
    this.startReadTime = ''; // 协议开始阅读时间
    this.tickTime = ''; // 协议点击提交时间
    this.endReadTime = ''; // 协议结束阅读时间   
  }

  async componentDidMount() {
    Madp.setStorageSync('mapCode', 'ef645b1336cbb05f', 'SESSION');
    await this.getContractParams(); // 获取合同相关
    dispatchTrackEvent({
      target: this,
      event: EventTypes.PO,
    });
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation: 'shared'
  }

  /*
   * 获取合同模板参数
   */
  getContractParams = async () => {
    const promises = [];
    // 获取商户信息
    promises.push(getMerchantInfo());
    // 用户信息
    promises.push(getUserInfo({
      scene: 'SCENE_CMB_UNION_APPLY'
    }));
    // 获取授信产品合同配置
    promises.push(queryContractInfoList());
    // 查询案件信息
    promises.push(getApplyInfo({
      queryScene: '0'
    }));
    const [merchantInfo, userInfoStatus, contractInfoRes, applyInfo] = await Promise.all(promises);
    const { contracts, userInfo } = contractInfoRes;
    const { queryStatusInfo } = userInfo || {};
    const { custservicePhoneNo, merchantId, merchantName, orgName, partnerType, partnerId, partner } = merchantInfo;
    const { applyStep } = applyInfo || {};
    let BaseContractInfo = {
      name: userInfo && userInfo.realName || '',
      certId: userInfo && userInfo.idNo || '',
      dateNow: `${dayjs().format('YYYYMMDD')}`,
      mobile: userInfo && userInfo.mobileNo || '',
      signDate: `${dayjs().format('YYYYMMDD')}`,
      partnerInfoList: [{
        partnerId: partnerId || merchantId, // 机构编号
        partner: partner || orgName || merchantName, // 机构名称
        partnerType: partnerType || '01', // 机构类型 01-流量、02-资方、03-保险
        partnerContact: custservicePhoneNo // 联系方式
      }]
    };
    let forceReadContract = false;
    let readDuration = 5;
    const promiseList = [];
    const contractTextList = [];
    contracts && contracts.length > 0 && contracts.forEach(async item => {
      let params = {
        contractPreviewData: { baseContractInfo: { ...BaseContractInfo } },
        contractVersion: item.contractVersion,
        // contractEdition: 'COMMON',
        contractCode: item.contractCode, // 合同编码
        contractCategory: item.contractType, // 合同类型
        interfaceVersion: '3.0', // 合同改造接口版本，新版合同改造版本传入3.0
        // bringParam: '1', // 走预览合同接口
        scene: 'PREVIEW'
      };
      promiseList.push(this.getContractTemplate(params, item.text));
      contractTextList.push(item.text);
      if (item.forceReadFlag === 'Y') {
        // 是否有强读协议,获取强读时间
        forceReadContract = true;
        readDuration = item.readDuration || 5;
      }
    });

    const newContractList = await Promise.all(promiseList); // 解决并发请求协议模板数据返回顺序乱序问题
    // 更新预览页倒计时状态和埋点状态
    // if (forceReadContractParams.length === 0) {
    //   updateCountDownFlag(true);
    // }
    const contractText = contractTextList.length > 0 && contractTextList.join('、');
    this.setState({
      newContractList,
      forceReadContract,
      countingNum: readDuration,
      readDuration,
      contracts,
      queryStatusInfo,
      contractText,
      applyStep,
      userInfoStatus
    });
  };

  getContractTemplate = async (params, title) => {
    const contractTemplate = await queryContractTemplate(params);
    return { title, htmlFile: contractTemplate };
  };

  // 协议点击
  contractTextClick = () => {
    const { forceReadContract, newContractList } = this.state;
    if (newContractList && newContractList.length > 0) {
      if (forceReadContract) {
        this.setState({ showContractBtn: true }, () => {
          this.updateTime('startReadTime');
        });
      } else {
        this.setState({ showContractBtn: false });
      }
      // 协议弹窗打开要防止外面的招招贷运营组件滚动
      this.handleTouchScroll('hidden');
      this.setState({ showContractsModal: true, contractTextClick: true })
      return;
    }
  };

  /**
   * 点击提交
   */
  async onSubmit() {
    dispatchTrackEvent({
      target: this,
      beaconId: 'GoToApplyButton',
      event: EventTypes.EV,
    });
    // 四个标签判断：cmbLoanWhiteFlag - 招招联合贷白名单：Y - 在白名单内，N - 不在白名单内
    // cmbLoanCustStatus - 招招联合贷客户状态：1 - 申请通过未放款，2 - 申请通过已放款，3 - 申请失败
    // operateEntityTag - 运营主体标签值：LHD - 联合贷
    // funderTag - 资金方标签值：81000067 - 招行
    const { cmbLoanWhiteFlag, cmbLoanCustStatus, operateEntityTag, funderTag, needNewWebview, heightStyle, sceneCode = '' } = this.props;
    if (cmbLoanCustStatus === '1' || cmbLoanCustStatus === '2') {
      this.setState({
        isModalOpen: true,
        modalTextContent: {
          modalTitle: '您已升级成功',
          modalContent: '由招商银行&招联为您提供借款服务，快去借一笔吧',
          modalConfirmText: '去借钱',
          type: 'success',
          isJumpLoan: true
        }
      });
      dispatchTrackEvent({
        target: this,
        beaconId: 'UnableToReapplyModal',
        event: EventTypes.SO,
      });
      return;
    } else if (cmbLoanCustStatus === '3') {
      this.setState({
        isModalOpen: true,
        modalTextContent: {
          modalTitle: '抱歉，无法再次申请',
          modalContent: '您已经申请过，请不要重复申请',
          modalConfirmText: '知道了',
          type: 'tip',
          isJumpLoan: false
        }
      });
      dispatchTrackEvent({
        target: this,
        beaconId: 'NoApplicationModal',
        event: EventTypes.SO,
      });
      return;
    } else if (cmbLoanWhiteFlag !== 'Y') {
      Madp.showToast({
        title: '抱歉，您暂时不符合申请要求',
        icon: 'none',
        duration: 2000
      });
      dispatchTrackEvent({
        target: this,
        beaconId: 'NoApplicationModal',
        event: EventTypes.SO,
      });
      return;
    } else if (operateEntityTag === 'LHD' && funderTag === '81000067') {
      this.setState({
        isModalOpen: true,
        modalTextContent: {
          modalTitle: '您已升级成功',
          modalContent: '由招商银行&招联为您提供借款服务，快去借一笔吧',
          modalConfirmText: '去借钱',
          type: 'success',
          isJumpLoan: true
        }
      });
      dispatchTrackEvent({
        target: this,
        beaconId: 'UpgradedSuccessModal',
        event: EventTypes.SO,
      });
      return;
    }
    // 案件状态判断
    const { applyStep } = this.state;
    // 审批中,跳转招招贷款结果尾页
    if (applyStep === '3') {
      Madp.navigateTo({
        url: `${urlDomain}/${Madp.getChannel()}/apply/#/pages/result/cmb-unite-result/index?needComposeCase=false&mapCode=${mapCode}&sceneCode=${sceneCode}`,
        useAppRouter: needNewWebview
      });
      if (heightStyle) {
        this.setState({
          isCmbDialogOpen: false
        });
      }
      return;
    } else if (applyStep === '1') {
      // 没申请过，进行协议强读
      // 合同强读
      const { newContractList, forceReadContract } = this.state;
      if (newContractList && newContractList.length > 0) {
        if (forceReadContract) {
          // 有强读协议倒计时
          // 协议弹窗打开要防止外面的招招贷运营组件滚动
          this.handleTouchScroll('hidden');
          this.setState({ showContractBtn: true, showContractsModal: true });
          this.updateTime('startReadTime');
          return;
        }
      }
      this.setState({ contractTextClick: false }, () => this.submitContract());
    } else {
      // 兜底提示 审批成功或者审批被拒了
      this.setState({
        isModalOpen: true,
        modalTextContent: {
          modalTitle: '抱歉，无法再次申请',
          modalContent: '您已经申请过，请不要重复申请',
          modalConfirmText: '知道了',
          type: 'tip',
          isJumpLpan: false
        }
      });
    }
  }

  // 协议半屏弹窗点击关闭
  closeDialog() {
    // 协议弹窗关闭要让外面的招招贷运营组件滚动
    this.handleTouchScroll('scroll');
    this.setState({ showContractsModal: false });
  }

  // 记录协议点击时间，上送埋点
  updateTime(key) {
    // 开始阅读协议时间取最早时间
    if (key === 'startReadTime' && this.startReadTime !== '') {
      return;
    }
    this[key] = Date.now();
    dispatchTrackEvent({
      target: this,
      beaconId: key,
      event: EventTypes.EV,
      beaconContent: {
        cus: { [key]: this[key] }
      }
    });
  }

  submitContract() {
    dispatchTrackEvent({
      target: this,
      beaconId: 'AgreeContracts',
      event: EventTypes.SO,
    });
    const { contracts, userInfoStatus, contractTextClick } = this.state;
    // 如果是通过点击协议提交，应该关闭协议
    if (contractTextClick) {
      // 协议弹窗关闭要让外面的招招贷运营组件滚动
      this.handleTouchScroll('scroll');
      this.setState({ showContractsModal: false, forceReadContract: false, readDuration: 0 });
      return;
    }
    const applyContractList = [];
    // 处理合同信息
    contracts && contracts.length > 0 && contracts.forEach(item => {
      applyContractList.push(item.contractType);
      this.updateTime('tickTime');
      this.updateTime('endReadTime');
    });

    // 判断是否需要补充身份信息
    const { needIdCard, needProfession } = userInfoStatus || {};
    const { needNewWebview, heightStyle, sceneCode = '' } = this.props;
    const channel = Madp.getChannel();
    // 需要补全资料就跳转到用户前台补充资料
    if (needIdCard === '2' || needIdCard === '3') {
      const redirectUrl = encodeURIComponent(`${urlDomain}/${channel}/apply/#/pages/result/cmb-unite-result/index?mapCode=${mapCode}&contracts=${applyContractList.join('|')}&startReadTime=${this.startReadTime}&tickTime=${this.tickTime}&endReadTime=${this.endReadTime}&sceneCode=${sceneCode}`);
      const isNeedPProfession = (needProfession === '2' || needProfession === '3') ? '1' : '2'
      Madp.navigateTo({
        url: `${urlDomain}/${channel}/usercenter/#/bio-addIdCard?isNeedProfession=${isNeedPProfession}&isNeedAddress=1&isNeedIdCard=1&scene=SCENE_CMB_UNION_APPLY&redirectUrl=${redirectUrl}`,
        useAppRouter: true
      });
    } else {
      // 直接跳转结果尾页建案
      Madp.navigateTo({
        url: `${urlDomain}/${channel}/apply/#/pages/result/cmb-unite-result/index?mapCode=${mapCode}&contracts=${applyContractList.join('|')}&startReadTime=${Date.now()}&tickTime=${Date.now()}&endReadTime=${Date.now()}&sceneCode=${sceneCode}`,
        useAppRouter: needNewWebview
      });
    }
    if (heightStyle) {
      // 关闭弹窗
      this.setState({
        isCmbDialogOpen: false
      });
    }
  }

  onConfirmModal = () => {
    const { modalTextContent } = this.state;
    const { needNewWebview, heightStyle } = this.props;
    // 如果命中资格CIF标，跳转借款
    if (modalTextContent && modalTextContent.isJumpLoan) {
      Madp.navigateTo({
        url: `${urlDomain}/${Madp.getChannel()}/loan/#/pages/index/index?cashLoanMode=1`,
        useAppRouter: needNewWebview
      });
      if (heightStyle) {
        this.setState({
          isCmbDialogOpen: false
        });
      }
    }
    this.setState({
      isModalOpen: false,
    });
    return;
  };

  closeCmbDialog = () => {
    this.setState({
      isCmbDialogOpen: false
    });
    // 关闭弹窗后调取回调函数
    const { closeCmbDialogCallBack } = this.props;
    closeCmbDialogCallBack && closeCmbDialogCallBack();
  };

  preventMove(e) {
    e.stopPropagation();
  }

  handleTouchScroll = (type) => {
    try {
      const content = document.getElementsByClassName('cmb-unite-apply__content')[0];
      content.style.overflow = type;
    } catch (e) { }
  }

  // 借款场景招招联合贷样式
  getLoanView() {
    const { newContractList } = this.state;
    return (
      <MUView>
        <MUView className="cmb-unite-apply__loan">
          <MUView className="cmb-unite-apply__loan-content">招商银行联合放款</MUView>
          <MUView className="cmb-unite-apply__loan-logo">
            <MUView className="cmb-unite-apply__loan-logo-content">
              <MUImage className="cmb-unite-apply__loan-logo-content-img" src={cmbUniteApplyImg.loanSureIcon} />
              <MUView className="cmb-unite-apply__loan-logo-content-text">品牌保证</MUView>
            </MUView>
            <MUView className="cmb-unite-apply__loan-logo-content">
              <MUImage className="cmb-unite-apply__loan-logo-content-img" src={cmbUniteApplyImg.loanSafeIcon} />
              <MUView className="cmb-unite-apply__loan-logo-content-text">安全合规</MUView>
            </MUView>
          </MUView>
          {newContractList && newContractList.length > 0 && <MUView className="cmb-unite-apply__loan-contract" onClick={this.contractTextClick.bind(this)}>
            <MUView className="cmb-unite-apply__loan-contract-text">阅读并同意</MUView>
            <MUView className="cmb-unite-apply__loan-contract-list">
              招商银行个人资信信息授权书、业务合作授权书
            </MUView>
          </MUView>}
          <MUButton
            className="cmb-unite-apply__loan-btn"
            type='primary'
            onClick={this.onSubmit.bind(this)}
          >下一步</MUButton>
        </MUView>
      </MUView>
    )
  }

  // 申请、首页场景下的招招联合贷样式
  getNormalView() {
    const { heightStyle } = this.props;
    const { newContractList } = this.state;
    return (
      <MUView className={`cmb-unite-apply__bg cmb-unite-apply__bg__${heightStyle}`}>
        <MUView className="cmb-unite-apply__apply-card">
          <MUView className="cmb-unite-apply__item">
            <MUView className="cmb-unite-apply__item__text">额度
              <MUView className="cmb-unite-apply__item__special-text">最高可提至(元)</MUView>
            </MUView>
            <MUView className="cmb-unite-apply__item__text cmb-unite-apply__item__lpr">利率
              <MUView className="cmb-unite-apply__item__special-text">最低可降至</MUView>
            </MUView>
          </MUView>
          <MUView className="cmb-unite-apply__item cmb-unite-apply__number">
            <MUView className="cmb-unite-apply__item__credit">300,000</MUView>
            <MUView className="cmb-unite-apply__item__lpr-number">4.57</MUView>
            <MUView className="cmb-unite-apply__item__symbol">%</MUView>
          </MUView>
          {newContractList && newContractList.length > 0 && <MUView className="cmb-unite-apply__contract" onClick={this.contractTextClick.bind(this)}>
            <MUView className="cmb-unite-apply__contract__text">阅读并同意</MUView>
            <MUView className="cmb-unite-apply__contract__list">
              招商银行个人资信信息授权书、业务合作授权书
            </MUView>
          </MUView>}
          <MUView className="cmb-unite-apply__btn" onClick={this.onSubmit.bind(this)}>一键升级</MUView>
          <MUView className="cmb-unite-apply__tips">仅需核实身份，结果以审批为准</MUView>
        </MUView>
        {heightStyle !== 'halfScreen' && <MUView>
          <MUView className="cmb-unite-apply__assure">
            <MUImage className="cmb-unite-apply__assure__icon" src={cmbUniteApplyImg.assure} />
            <MUView className="cmb-unite-apply__assure__text">100%额度保障</MUView>
          </MUView>
          <MUView className="cmb-unite-apply__notify">即使升级不成功，您的额度不受影响，可继续使用</MUView>
          <MUImage className={`cmb-unite-apply__bottom-icon cmb-unite-apply__bottom-icon__${heightStyle}`} src={cmbUniteApplyImg.bottomIcon} />
        </MUView>}
      </MUView>
    )
  }

  render() {
    const { heightStyle, sceneCode } = this.props;
    const { newContractList, isCmbDialogOpen, showContractsModal, readDuration, modalTextContent, isModalOpen, showContractBtn } = this.state;
    const height = heightStyle === 'halfScreen' ? '60%' : heightStyle === 'fullScreen' ? '90%' : '100%';
    return <MUView className="cmb-unite-apply" onTouchMove={this.preventMove.bind(this)}>
      <MUDrawer hasTransition={false} height={height} show={isCmbDialogOpen} maskClickable={false} placement='bottom' className="cmb-unite-apply__drawer-content" beaconId={"CmbUniteApplyDrawer"}>
        {heightStyle && <MUView className="cmb-unite-apply__title">
          <MUView className="cmb-unite-apply__title__text">借款服务升级</MUView>
          <MUImage className="cmb-unite-apply__title__close" src={cmbUniteApplyImg.closeIcon} onClick={this.closeCmbDialog.bind(this)} />
        </MUView>}
        <MUView className="cmb-unite-apply__content">
          {sceneCode === 'withdraw' ? this.getLoanView() : this.getNormalView()}
          <AgreementDrawer agreementViewProps={{
            list: newContractList,
            current: 0
          }} show={showContractsModal} submit={this.submitContract.bind(this)} close={this.closeDialog.bind(this)} totalCount={readDuration || 0} showBtn={showContractBtn} beaconId="CmbUniteAgreementDrawer" />
          <MUModal type={modalTextContent.type} isOpened={isModalOpen} title={modalTextContent.modalTitle} content={modalTextContent.modalContent} confirmText={modalTextContent.modalConfirmText} onConfirm={this.onConfirmModal.bind(this)} />
        </MUView>
      </MUDrawer>
    </MUView>;
  }
};