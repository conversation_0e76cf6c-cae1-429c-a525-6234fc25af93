/**
 * 业务组件需要用到的公共方法封装
 * */
import {
    getEnv,
    getCurrentPageUrl
  } from '@mu/madp-utils';
  import Madp from '@mu/madp';
  import {
    dispatchTrackEvent
  } from '@mu/madp-track';
  
  // 当前环境
  const env = process.env.BUILD_ENV || getEnv();
  
  /**
   * 前端域名
   */
  let domain = 'https://m-zl.mucfc.com';
  if (env !== 'prod') {
    domain = `https://m-zl-${env}.cfcmu.cn`;
  }
  
  export const urlDomain = domain;