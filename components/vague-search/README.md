# 模糊搜索

@mu/vague-search



模糊搜索

## 预览图

![screenshot.png](http://unpkg.mucfc.com/@mu/vague-search/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/vague-search`

### 样式引入

`@import "~@mu/vague-search/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/vague-search',
            '@mu\\vague-search'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\vague-search',
            '@mu/vague-search',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/vague-search':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/vague-search')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ---------  |
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | 'schoolSelector' | 埋点 |
| optionList  | Array<string> | 否  | [] | 下拉列表 |
| needShowList  | boolean | 否  | true | 是否展示下拉列表 |
| value  | string | 否  | '' | 输入框默认值 |
| title  | string | 否  | '' | 输入框标题 |
| placeholder  | string | 否  | '' | 输入框默认提示 |
| editable  | boolean | 否  | true | 输入框是否可编辑 |
| selectedOnly  | boolean | 否  | false | 是否只能从下拉框中选择 |
| handleChange  | Function | 否  | () => {} | 值改变回调 |
| handleFocus  | Function | 否  | () => {} | focus 回调 |
| handleClick  | Function | 否  | () => {} | 输入框点击回调 |
| handleBlur  | Function | 否  | () => {} | blur 回调 |
| handleItemClick  | Function | 否  | () => {} | 点击下拉框回调 |