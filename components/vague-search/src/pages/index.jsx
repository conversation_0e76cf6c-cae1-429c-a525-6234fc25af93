/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView, MUForm } from '@mu/zui';
import VagueSearch from '../code';
import './index.scss';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

// const operationId = {
//   /** 查询预审结果 */
//   sendVerification: 'mucfc.apply.assist.sendVerification',
//   /** 申请准入检查 */
//   doAdmission: 'mucfc.apply.admission.doAdmission'
// };
// console.log('apiHost', apiHost);
// const API_URL = {
//   SEARCH_COMPANY: `https://mgp-st1.api.cfcmu.cn/?operationId=${operationId.doAdmission}`,
//   SEND_EMAIL_CODE: `https://mgp-st1.api.cfcmu.cn/?operationId=${operationId.sendVerification}`,
// };

export default class DemoPage extends Component {
  constructor() {
    super();
    this.state = {
      companyEmail: ''
    };
  }

  updateCompanyList = async (companyName) => {
    console.log('updateCompanyList', companyName);
    if (companyName) {
      const detailInfoList = await new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            {
              companyFullName: '招联金融',
              companyEmail: 'mucfc1.com'
            },
            {
              companyFullName: '招商银行',
              companyEmail: 'mucfc2.com'
            },
            {
              companyFullName: '建设银行',
              companyEmail: 'mucfc3.com'
            },
            {
              companyFullName: '工商银行',
              companyEmail: 'mucfc4.com'
            }
          ])
        }, 500)
      });
      this.setState({
        optionList1: detailInfoList.map((item) => item.companyFullName),
        optionList2: detailInfoList.map((item) => item.companyEmail),
      })
    } else {
      this.setState({
        optionList1: [],
        optionList2: [],
      })
    }
  }

  updateCompanyEmailList = () => {
    const {
      companyEmail
    } = this.state;
    const reg = /([^@]*)@?/;
    return this.emailSuffix.map((item) => `${companyEmail.match(reg)[1]}${item}`);
  }

  handleItemClick = () => {
    // this.setState({
    //   companyFullName: val
    // })
  }

  handleEmailItemClick = (val) => {
    this.setState({
      companyEmail: val
    })
  }

  render() {
    const {
      companyName,
      companyEmail,
      optionList1,
      optionList2
    } = this.state;
    return (
      <MUView
        className="component_demo_page"
      >
        <MUForm
          className="email-comp_form"
          titleType="list"
        >
          {/* 单位名称 */}
          <VagueSearch
            title="单位名称"
            placeholder="请输入单位名称"
            name="companyVagueSearch"
            notFetchWhenEmpty
            beaconId="companyName"
            value={companyName}
            optionList={optionList1}
            handleChange={(val) => { this.updateCompanyList(val) }}
            handleItemClick={this.handleItemClick}
          />
          {/* 公司邮箱 */}
          <VagueSearch
            title="工作邮箱"
            placeholder="请输入个人使用的工作邮箱"
            name="emailVagueSearch"
            listItemName="companyEmailSuffix"
            beaconId="companyEmail"
            value={companyEmail}
            optionList={optionList2}
            handleChange={(val) => { this.updateCompanyList(val) }}
            handleItemClick={this.handleEmailItemClick}
          />
        </MUForm>
      </MUView>
    );
  }
}
