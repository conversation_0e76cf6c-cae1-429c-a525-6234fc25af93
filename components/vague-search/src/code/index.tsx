import  { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUInput, MUScrollView } from '@mu/zui';
import { MUVagueSearchProps, MUVagueSearchState } from './types/component';
if(!['tt','swan','kwai'].includes(process.env.TARO_ENV||'')) {
  require('../style/index.scss');
}
@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'VagueSearch', // 就是当前组件类名
}))
export default class VagueSearch extends Component<MUVagueSearchProps, MUVagueSearchState> {

  public static options = {
    addGlobalClass: true
  }
  config:any = {
    styleIsolation:'shared'
  }
  
  public static defaultProps: MUVagueSearchProps;

  public static propTypes: InferProps<MUVagueSearchProps>;

  isBlurToChange = false;

  public constructor(props: MUVagueSearchProps) {
    super(props);
    const {
      value
    } = this.props;
    this.state = {
      showList: false, // 是否显示下拉框
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      value, // 下拉框的值
    };
  }

  // eslint-disable-next-line react/no-deprecated
  public componentWillReceiveProps(nextProps) {
    const {
      value: newValue
    } = nextProps;
    const {
      value
    } = this.props;
    if (value !== newValue) {
      this.setState({
        value: newValue
      });
    }
  }

  /**
   * 输入框值变动
   * @param val string
   */
  private onChange = (val): void => {
    // onblur 会触发一次 onchange，需排除
    if (!this.isBlurToChange) {
      const {
        handleChange,
        needShowList = true,
      } = this.props;
      let {
        showList
      } = this.state;
      if (needShowList && val) {
        showList = true
      }
      // 值若为空，不展示列表
      if (!val) {
        showList = false
      }
      this.setState({
        value: val,
        showList
      }, () => {
        handleChange && handleChange(val);
      })
    } else {
      this.isBlurToChange = false
    }
  };

  /**
   * 输入框值聚焦
   */
  private onFocus = () => {
    const {
      handleFocus,
      needShowList = true,
      optionList
    } = this.props;
    if (handleFocus && typeof handleFocus === 'function') {
      handleFocus();
    }
    if (needShowList && optionList && optionList.length) {
      this.setState({
        showList: true
      })
    }
  };

  /**
   * 失焦函数
   */
  private onBlur = () => {
    const {
      handleBlur,
      optionList = [],
      handleChange,
      selectedOnly = false
    } = this.props;
    const {
      value
    } = this.state;
    this.isBlurToChange = true;
    handleBlur && handleBlur();
    if (selectedOnly) {
      // 有下拉框，但是未主动选择时，默认选第一个
      if (value && (!optionList || !optionList.length)) {
        this.setState({
          value: ''
        }, () => {
          handleChange && handleChange('');
        });
      } else if (value) {
        this.setState({
          value: optionList[0]
        }, () => {
          handleChange && handleChange(optionList[0]);
        });
      }
      this.setState({
        showList: false
      });
    }
  };

  /**
   * 点击下拉框中某一选项
   * @param {string} val 选中的选项
  */
  private itemClick = async (val) => {
    const {
      handleChange,
      handleItemClick
    } = this.props;
    handleItemClick && handleItemClick(val);
    handleChange && handleChange(val);
    this.setState({
      showList: false,
      value: val
    })
  };

  /**
   * 点击输入框
  */
  private onClick = () => {
    const {
      handleClick
    } = this.props;
    handleClick && handleClick();
  };

  private onClickOverLay = () => {
    this.setState({
      showList: false
    })
  };

  public render(): JSX.Element {
    const {
      className,
      customStyle,
      beaconId,
      title,
      placeholder,
      optionList = [],
      editable = true,
      needShowList = true
    } = this.props;
    const {
      value,
      showList
    } = this.state;
    return (
      <MUView
        className={`vague-search ${className || ''}`}
        style={customStyle}
      >
        <MUInput
          className={process.env.TARO_ENV !== 'h5' ? 'vague-search__input' : ''}
          type="text"
          name="vagueSearch"
          title={title}
          placeholder={placeholder}
          beaconId={beaconId}
          clear
          maxLength={50}
          value={value}
          onChange={this.onChange}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
          editable={editable}
          onClick={this.onClick}
        />
        {/*
        * 支付宝小程序key值需要移除，主要是key导致搜索不到匹配的项
      */}
        {needShowList && showList && !!optionList.length && (
        <MUScrollView
          className="vague-search__scroll-view"
          scrollY
        >
          <MUView className="vague-search__scroll-view-main">
            {process.env.TARO_ENV === 'alipay' && optionList.map((item) => (
              <MUView
                className="item"
                beaconId={`${beaconId}Option`}
                onClick={() => { this.itemClick(item); }}
              >
                {item}
              </MUView>
            ))}

            {(process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'h5') && optionList.map((item) => (
              <MUView
                className="item"
                key={item.key}
                beaconId={`${beaconId}Option`}
                onClick={() => { this.itemClick(item); }}
              >
                {item}
              </MUView>
            ))}
          </MUView>
        </MUScrollView>
        )}
        <MUView
          className={`${needShowList && showList ? 'vague-search__overlay' : 'hide__overlay'}`}
          beaconId="VagueSearchOverLay"
          onClick={this.onClickOverLay}
        />
      </MUView>
    );
  }
}
