import { ComponentClass } from 'react';

import MUComponent from './base';

// interface optionItem {
//   /** 从相册选图 */
//   key: string;
//   /** 使用相机 */
//   value: string;
// }

export interface MUVagueSearchProps extends MUComponent {
  handleChange?: Function;
  handleFocus?: Function;
  handleClick?: Function;
  handleBlur?: Function;
  handleItemClick?: Function;
  needShowList?: boolean;
  optionList?: Array<string>;
  value: string;
  title?: string;
  placeholder?: string;
  editable?: boolean;
  selectedOnly?: boolean;
}

export interface MUVagueSearchState {
  showList?: boolean;
  value: string;
}

declare const MUVagueSearchComponent: ComponentClass<MUVagueSearchProps>;

export default MUVagueSearchComponent;
