@import "~@mu/zui/dist/style/mixins/index.scss";
@import "~@mu/zui/dist/style/variables/default.scss";

.order-group {
  font-family: PingFangSC-Regular;
  background-color: #fff;

  .order-block {
    .order-title {
      font-size: 32px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
    }

    .order-tip {
      margin-bottom: 48px;
      color: #a6a6a6;
      text-align: center;
      font-size: 24px;
    }
  }

  .day-block {
    display: flex;
    flex-flow: wrap;
    justify-content: space-around;

    &-view {
      display: flex;
      flex-direction: column;
      position: relative;
      justify-content: center;
      align-items: center;
      width: 130px;
      height: 110px;
      background-color: #F3F3F3;
      border-radius: 8px;
      font-size: 24px;
      color: #808080;

      .day-block-date {
        text-align: center;
        font-size: 26px;
        font-weight: bold;
        color: #333333;
      }
    }

    &-disabled {
      background-color: #F3F3F3;

      .day-block-date {
        color: #808080;
        font-weight: normal;
      }
    }

    &-active {
      background-color: #3477FF;
      color: #ffffff;

      .day-block-date {
        color: #ffffff;
      }
    }

    &-icon {
      opacity: 1;
      position: absolute;
      width: 44px;
      height: 24px;
      right: 0;
      top: 0;
    }
  }

  .time-block {
    box-sizing: border-box;
    margin-bottom: 26px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    &-view {
      margin-left: 15px;
      margin-top: 20px;
      display: flex;
      flex-direction: column;
      position: relative;
      justify-content: center;
      align-items: center;
      background-color: #F3F3F3;
      border-radius: 8px;
      width: 168px;
      height: 80px;
      font-size: 18px;
      color: #808080;

      &-icon {
        position: absolute;
        width: 56px;
        height: 25px;
        right: 0%;
        top: 0;
      }

      &-time {
        color: #333333;
        font-weight: bold;
        font-size: 24px;
      }
    }

    &-disabled {
      opacity: 0.7;
    }

    &-active {
      background-color: #3477FF;
      color: #ffffff;

      .time-block-view-time {
        color: #ffffff;
      }
    }
  }

  .order-button {
    margin: 0 20px 24px;
    border-radius: 50px;
  }

  .order-button-full {
    margin: 0 30px 24px;
    border-radius: 50px;
    opacity: 0.7;
  }

  .empty_block {
    margin: 10px;
    height: 198px;
    border-radius: 8px;
    border: 2px solid #e5e5e5;
    color: #a6a6a6;
    text-align: center;
    font-size: 26px;
    font-weight: 400;
    font-family: "PingFang SC";
    line-height: 198px;
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .no-scheduled-time,
  .has-scheduled-time {
    .mu-badge__num {
      right: 0px;
      box-shadow: none;
    }
  }
}