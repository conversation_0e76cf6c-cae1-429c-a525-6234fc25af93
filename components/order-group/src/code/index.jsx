import dayjs from 'dayjs';
import Madp, { Component } from '@mu/madp';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import { MUView, MUButton, MUTabs, MUTabsPane, MUImage, MUModal } from '@mu/zui';
import { observer } from '@tarojs/mobx';
import { queryAppointStatus, submitAppointInfo } from './api';
import { getweek, sendTrackBeacon, convertTimeToString, convertStringToTimestamp } from './utils'
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}
const fullIcon = 'https://file.mucfc.com/abf/1/0/202304/20230406101122908655.png';


const beaconIds = {
  cancelBtn: 'CancelBtn',
  clickDayBlock: 'ClickDayBlock',
  changeTab: "ChangeTab",
  clickTimeBlock: 'ClickTimeBlock',
  orderBtn: "OrderBtn",
  changeOrderBtn: "ChangeOrderBtn"
}

@track((props) => ({
  beaconId: props.beaconId,
  beaconContent: props.beaconContent,
  uiType: 'OrderGroup',
}))
@observer
export default class OrderGroup extends Component {
  constructor(props) {
    super(props);
    this.state = {
      current: 0,
      chooseDay: 0,
      chooseTime: '',
      realChooseDay: '', // 请求预约时的选定日期
      realChooseTime: '', // 请求预约时的选定时间段
      WholeDayList: [
        { id: 0, status: false },
        { id: 1, status: false },
        { id: 2, status: false },
        { id: 3, status: false },
        { id: 4, status: false },
      ],
      needCloseUserPage: true,
      openCloseModal: false,
      showSuccessModal: false,
      closeWebViewcontent: '',
      closeWebViewTitle: '',
      AMTimeList: [],
      PMTimeList: [],
      hasScheduledTime: false,// 是否有可预约的时间段 -没有可预约时间段时，点击选项toast提示
      hasAMScheduledTime: false,// 上午是否存在可预约时间
      hasPMScheduledTime: false,// 下午是否存在可预约时间
    };
  }

  async componentDidMount() {
    await this.initComp();
    dispatchTrackEvent({
      event: EventTypes.SO,
      target: this,
      beaconId: 'orderGroupShow',
    });
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation: 'shared'
  }

  async initComp() {
    const { orderCaseType } = this.props;
    this.setState({
      WholeDayList: [
        { id: 0, status: false },
        { id: 1, status: false },
        { id: 2, status: false },
        { id: 3, status: false },
        { id: 4, status: false },
      ],
    });
    try {
      // 查询可预约时间段，预约状态和预约时间
      const res = await queryAppointStatus({ needAppointList: true, caseType: orderCaseType });
      const orderTimeList = res.appointmentInfoList; // 预约时间列表
      const orderState = res.applyState === '9' ? 'appointment' : 'no_appointment'; // 预约状态
      const appointmentStartTime = res.appointmentStartTime; // 预约开始时间
      const appointmentEndTime = res.appointmentEndTime; // 预约结束时间
      this.caseNo = res.caseNo
      // 将预约开始时间戳和结束时间戳转化为字符串
      const appointTime = convertTimeToString([appointmentStartTime, appointmentEndTime]);
      console.log(123123, appointTime)
      // 通知调用方预约状态
      this.notifyAppointmentStatus(orderState, appointTime);

      // 处理日期和时间段状态
      this.processTimeSlots(orderTimeList, orderState);
      return { orderState }
    } catch (error) {
      Madp.showToast({
        title: '系统繁忙，请稍后重试',
        icon: 'none',
        duration: 2000,
      });
    }
  }

  /**
   * 通知调用方预约状态
   * @param {string} orderState - 预约状态
   * @param {string} appointTime - 预约时间
   */
  notifyAppointmentStatus(orderState, appointTime,) {
    // 预约时间 -年月日格式
    const orderTime = `${appointTime.slice(0, 4)}年${appointTime.slice(5, 7)}月${appointTime.slice(8, 10)}日 ${appointTime.slice(11)}`;
    // 预约时间 -月日格式
    const orderMonthDayTime = `${appointTime.slice(5, 7)}月${appointTime.slice(8, 10)}日 ${appointTime.slice(11)}`;
    const { callbackStatus, changeOrderTime } = this.props;

    this.setState({
      orderState,
      orderTime,
      changeOrderTime: changeOrderTime && orderState === 'appointment'
    });

    // 执行回调，将查到的预约状态通知调用方
    if (callbackStatus && typeof callbackStatus === 'function') {
      callbackStatus({
        orderState,
        orderTime,
        orderMonthDayTime
      });
    }
  }

  /**
   * 处理时间段状态
   * @param {Array} orderTimeList - 预约时间列表
   * @param {string} orderState - 预约状态
   */
  processTimeSlots(orderTimeList, orderState) {
    if (!orderTimeList || !(orderState === 'no_appointment' || orderState === 'appointment')) {
      return;
    }

    const { WholeDayList } = this.state;
    let hasScheduledTime = false;
    let dataList = [...WholeDayList];

    // 处理每天的可预约状态 appointmentTimeSlotList是每天的可预约时间段 有Y表示当天可预约
    orderTimeList.forEach((item, index) => {
      if (item.appointmentTimeSlotList && item.appointmentTimeSlotList.length > 0) {
        const hasAvailableSlot = item.appointmentTimeSlotList.some(slot => slot.available);
        if (hasAvailableSlot) {
          // 标记存在可预约时间
          hasScheduledTime = true;
          // 标记当天可预约
          dataList[index].status = true;
        }
      }
    });

    // 找出第一个可预约的日期作为默认选中日期
    let choosedate = dataList.findIndex(item => item.status);
    choosedate = choosedate > -1 ? choosedate : 0;

    this.setState({
      chooseDay: choosedate,
      realChooseDay: dayjs(orderTimeList[choosedate].appointmentDate).format('YYYY-MM-DD'),
      hasScheduledTime,
      orderTimeList,
      WholeDayList: dataList
    });

    // 获取选中日期的时间段列表
    this.getDayTimeList({
      orderTimeList,
      chooseDay: choosedate
    });
  }

  /**
   * 获取指定日期的时间段列表
   * @param {Object} data - 包含时间数据的对象
   */
  getDayTimeList = (data) => {
    const orderTimeList = data && data.orderTimeList;
    const chooseDay = data && data.chooseDay;
    let AMTimeList = [];
    let PMTimeList = [];
    // 没有时间段数据时直接返回
    if (!orderTimeList || !orderTimeList[chooseDay] ||
      !orderTimeList[chooseDay].appointmentTimeSlotList ||
      orderTimeList[chooseDay].appointmentTimeSlotList.length === 0) {
      this.setState({ AMTimeList: [], PMTimeList: [] });
      return;
    }

    // 处理时间段数据，区分上午和下午
    orderTimeList[chooseDay].appointmentTimeSlotList.forEach(slot => {
      const availableTimeSlot = slot.availableTimeSlot;
      const startTime = availableTimeSlot.split('-')[0];
      // 根据时间段区分上午和下午
      if (startTime.slice(0, 2) >= 12 && startTime.slice(0, 5) !== '12:00') {
        // 下午时间段
        PMTimeList.push(slot);
      } else {
        // 上午时间段
        AMTimeList.push(slot);
      }
    });

    // 判断上午和下午是否存在可预约时间
    let hasAMScheduledTime = false;
    let hasPMScheduledTime = false;
    hasAMScheduledTime = AMTimeList.find(slot => slot.available);
    hasPMScheduledTime = PMTimeList.find(slot => slot.available);

    this.setState({ AMTimeList, PMTimeList, hasAMScheduledTime, hasPMScheduledTime, current: hasAMScheduledTime ? 0 : 1 });
  }

  /**
   * Tab切换回调
   * @param {number} value - 选中的tab索引
   */
  handleClick = (value) => {
    this.setState({
      current: value,
      realChooseTime: '',
      chooseTime: ''
    });

    // 埋点
    const { beaconPageId } = this.props;
    sendTrackBeacon(beaconPageId, {
      event: EventTypes.EV,
      beaconId: beaconIds.changeTab
    });
  }

  // 去借款
  onGoBorrowMoney = () => {
    const {
      beaconPageId,
    } = this.props;
    sendTrackBeacon(beaconPageId, {
      event: EventTypes.EV,
      beaconId: beaconIds.goloanBtn
    });
    const loanUrl = getLoanUrl();
    Madp.navigateTo({
      url: loanUrl
    });
  }

  /**
   * 选择日期
   * @param {number} index - 日期索引
   * @param {Object} item - 日期数据
   */
  checkDay = (index, item) => {
    const { WholeDayList, orderTimeList, hasScheduledTime, changeOrderTime } = this.state;
    const { beaconPageId } = this.props;

    // 发送埋点
    sendTrackBeacon(beaconPageId, {
      event: EventTypes.EV,
      beaconId: beaconIds.clickDayBlock
    });

    // 没有可预约时间时显示提示
    if (!hasScheduledTime) {
      const title = changeOrderTime ? '改约失败' : '预约失败';
      const content = changeOrderTime
        ? '当前无可改约时间，请下次再试'
        : '当前预约时间段已满，请等待专家联系';

      this.showModal({ title, content });
      return;
    }

    // 选择可预约的日期
    if (WholeDayList[index].status) {
      this.setState({
        chooseDay: index,
        realChooseDay: dayjs(item.appointmentDate).format('YYYY-MM-DD'),
        chooseTimeList: orderTimeList[index],
        realChooseTime: '',
        chooseTime: ''
      });

      // 获取选中日期的时间段
      this.getDayTimeList({
        orderTimeList,
        chooseDay: index
      });
    }
  }

  /**
   * 选择时间段
   * @param {Object} item - 时间段数据
   * @param {number} index - 时间段索引
   */
  checkTime = (item, index) => {
    const { beaconPageId } = this.props;
    const { hasScheduledTime, changeOrderTime } = this.state;

    // 发送埋点
    sendTrackBeacon(beaconPageId, {
      event: EventTypes.EV,
      beaconId: beaconIds.clickTimeBlock
    });

    // 没有可预约时间时显示提示
    if (!hasScheduledTime) {
      const title = changeOrderTime ? '改约失败' : '预约失败';
      const content = changeOrderTime
        ? '当前无可改约时间，请下次再试'
        : '当前预约时间段已满，请等待专家联系';

      this.showModal({ title, content });
      return;
    }

    // 选择可预约的时间段
    if (item.available) {
      this.setState({
        chooseTime: index,
        realChooseTime: item.availableTimeSlot
      });
    }
  }

  /**
   * 预约专家
   * @param {Object} data - 预约信息
   */
  submitAppoint = async (data) => {
    const { needSuccessModal, orderCaseType } = this.props;
    const { changeOrderTime } = this.state;

    const res = convertStringToTimestamp(`${data.realChooseDay} ${data.realChooseTime}`);

    // 构建请求参数
    const params = data.realChooseDay ? {
      appointmentStartTime: res.startTimestamp,
      appointmentEndTime: res.endTimestamp,
      appointType: data.orderStyle,
      caseType: orderCaseType,
      caseNo: this.caseNo
    } : {
      appointType: data.orderStyle,
      caseType: orderCaseType
    };

    try {
      // 调用预约接口
      await submitAppointInfo(params, false);
      const res = await this.initComp();
      const { onSubmitOrder } = this.props
      if (typeof onSubmitOrder === 'function') {
        onSubmitOrder({ orderState: res.orderState, })
      }
      // 处理成功情况
      this.handleAppointmentSuccess(data, changeOrderTime, needSuccessModal);
    } catch (e) {
      // 统一处理错误
      this.handleAppointmentError(e, changeOrderTime);
    }
  }

  /**
   * 处理预约成功
   * @param {Object} data - 预约信息
   * @param {boolean} changeOrderTime - 是否为修改预约
   * @param {boolean} needSuccessModal - 是否需要显示成功弹窗
   */
  handleAppointmentSuccess(data, changeOrderTime, needSuccessModal) {
    if (changeOrderTime) {
      // 修改预约成功
      const changeTime = (data.realChooseDay && data.realChooseTime) ?
        `${data.realChooseDay.slice(5, 7)}月${data.realChooseDay.slice(8, 10)}日 ${data.realChooseTime}` : '';

      this.showModal({
        title: '修改预约成功',
        content: `你已完成预约时间修改，新的预约时间为${changeTime}`,
        showSuccess: true
      });
    } else if (needSuccessModal) {
      // 新预约成功
      this.setState({ isSuccessOpened: true });
    }
  }

  /**
   * 处理预约错误
   * @param {Error} error - 错误对象
   * @param {boolean} changeOrderTime - 是否为修改预约
   */
  handleAppointmentError(error, changeOrderTime) {
    const errCode = error.errCode;
    // 处理不同错误码
    switch (errCode) {
      case 'UMDP02431':
      case 'UMDP02665':
      case 'UMDP02441':
      case 'UMDP03687':
        // 预约时间被占用，重新加载
        this.handleTimeSlotOccupiedError(changeOrderTime);
        break;
      default:
        // 其他错误
        Madp.showToast({
          title: '抱歉，当前系统繁忙，请稍后重试，如有疑问请联系在线客服',
          icon: 'none',
          duration: 2000,
        });
    }
  }

  /**
   * 处理时间段被占用错误
   * @param {boolean} changeOrderTime - 是否为修改预约
   */
  async handleTimeSlotOccupiedError(changeOrderTime) {
    // 重新请求最新可预约时间
    await this.initComp();
    const { hasScheduledTime } = this.state;

    if (changeOrderTime) {
      // 改约失败
      if (!hasScheduledTime) {
        this.showModal({
          title: '改约失败',
          content: '抱歉，当前暂无可预约时段，请等待专家联系'
        });
      } else {
        this.showModal({
          title: '改约失败',
          content: '抱歉，当前预约时段已满，请预约其他时段',
          needClose: false
        });
      }
    } else if (!hasScheduledTime) {
      // 无可预约时间
      this.showModal({
        title: '预约失败',
        content: '抱歉，当前暂无可预约时段，请等待专家联系'
      });
    } else {
      // 有其他可选时间
      this.showModal({
        title: '预约失败',
        content: '抱歉，当前预约时段已满，请预约其他时段',
        needClose: false
      });
    }
  }

  /**
   * 提交预约
   */
  orderSubmit = async () => {
    const { realChooseDay, realChooseTime, changeOrderTime, hasScheduledTime } = this.state;
    // 检查是否有可预约时间
    if (!hasScheduledTime) {
      const title = changeOrderTime ? '改约失败' : '预约失败';
      const content = changeOrderTime
        ? '当前无可改约时间，请下次再试'
        : '当前预约时间段已满，请等待专家联系';

      this.showModal({ title, content });
      return;
    }

    // 检查是否选择了时间
    if (!realChooseDay || !realChooseTime) {
      Madp.showToast({
        title: '请先选择您希望预约的时间',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 提交预约
    const orderStyle = changeOrderTime ? 'revise' : 'appoint';
    this.submitAppoint({
      orderStyle,
      realChooseDay,
      realChooseTime
    });
  }

  /**
   * 失败弹窗确认按钮回调
   */
  modalConfirm = () => {
    const { modalConfirmCallback } = this.props;
    const { needCloseUserPage } = this.state;

    this.setState({
      openCloseModal: false,
      needCloseUserPage: true,
    }, () => {
      if (modalConfirmCallback && typeof modalConfirmCallback === 'function') {
        modalConfirmCallback({ needCloseUserPage });
      }
    });
  }

  /**
   * 显示模态框
   * @param {Object} options - 模态框配置
   * @param {string} options.title - 标题
   * @param {string} options.content - 内容
   * @param {boolean} options.showSuccess - 是否为成功模态框
   * @param {boolean} options.needClose - 是否需要关闭页面
   */
  showModal = (options) => {
    const { title, content, showSuccess = false, needClose = true } = options;
    this.setState({
      openCloseModal: true,
      showSuccessModal: showSuccess,
      closeWebViewTitle: title,
      closeWebViewcontent: content,
      needCloseUserPage: needClose,
    });
  }

  /**
   * 跳转到借款页面
   */
  successConfirm = () => {
    const { beaconPageId, successCancelCallback } = this.props;
    // 埋点
    sendTrackBeacon(beaconPageId, {
      event: EventTypes.EV,
      beaconId: beaconIds.cancelBtn
    });
    if (successCancelCallback && typeof successCancelCallback === 'function') {
      successCancelCallback();
    }
  }

  /**
   * 预约成功弹窗取消回调
   */
  successCancel = () => {
    // 返回首页处理
    this.setState({
      isSuccessOpened: false
    }, () => {
      // 这里可以添加返回首页的逻辑
    });
  }

  // 渲染日期选择项
  getDayBlockView() {
    const { orderTimeList, chooseDay, WholeDayList, hasScheduledTime } = this.state
    return (
      <MUView className="day-block">
        {
          orderTimeList && orderTimeList.length > 0 &&
          orderTimeList.map((item, index) => {
            const disabled = !WholeDayList[index].status;
            const active = chooseDay === index && hasScheduledTime;
            return (
              <MUView className={`day-block-view  ${disabled ? 'day-block-disabled' : ''} ${active ? 'day-block-active' : ''}`}
                key={index} onClick={() => this.checkDay(index, item)}>
                {!WholeDayList[index].status && <MUImage src={fullIcon} alt="" className="day-block-icon" />}
                <MUView className='day-block-date'>
                  {getweek(dayjs(item.appointmentDate).format('YYYY-MM-DD'))}
                </MUView>
                <MUView>{(dayjs(item.appointmentDate).format('MM-DD'))}</MUView>
              </MUView>
            )
          })
        }
      </MUView>

    );
  };


  // 渲染时间选择项
  getTimeBlockView(timeList) {
    if (!timeList || timeList.length === 0) {
      return <MUView className="empty_block">当前还未释放预约号，请稍后再试</MUView>;
    }
    const { chooseTime } = this.state
    return (
      <MUView className="time-block">
        {timeList.map((item, index) => {
          const active = chooseTime === index;
          return (
            <MUView className={`time-block-view ${!item.available ? 'time-block-disabled' : ''} ${active ? 'time-block-active' : ''}`} key={index} onClick={() => this.checkTime(item, index)}>
              <MUView className="time-block-view-time">{item.availableTimeSlot} </MUView>
              {item.available && item.availableSlotsCount &&
                <MUView className={`${item.available ? '' : 'time-block-view-no-count'}`}>
                  可约{item.availableSlotsCount}
                </MUView>
              }
              {!item.available && <MUImage src={fullIcon} alt="" className="time-block-view-icon" />}
            </MUView>
          );
        })
        }
      </MUView>
    );
  };

  render() {
    const {
      current,
      orderState,
      PMTimeList,
      AMTimeList,
      changeOrderTime,
      hasScheduledTime,
      isSuccessOpened,
      orderTime,
      openCloseModal,
      showSuccessModal,
      closeWebViewTitle,
      closeWebViewcontent,
      hasAMScheduledTime,
      hasPMScheduledTime
    } = this.state;

    const {
      beaconPageId
    } = this.props;



    // 判断是否显示预约区域
    const shouldShowOrderBlock = orderState === 'no_appointment' || changeOrderTime;
    return (
      <MUView>
        <MUView className="order-group">
          {shouldShowOrderBlock && (
            <MUView>
              <MUView className="order-block">
                <MUView className="order-title">预约时间</MUView>
                {this.getDayBlockView()}

                <MUTabs
                  className={hasAMScheduledTime ? 'has-scheduled-time' : 'no-scheduled-time'}
                  current={current}
                  tabList={[
                    {
                      title: '上午时段',
                      badge: {
                        type: 'slogan',
                        value: current === 0 ? '' : (hasAMScheduledTime ? '可约' : '约满'), // current-0表示上午时间，选中上午不展示角标
                        shape: 'oval',
                        className: hasAMScheduledTime ? 'has-scheduled-time' : 'no-scheduled-time',
                        color: hasAMScheduledTime ? '#ff8844' : '#a6a6a6'
                      }
                    },
                    {
                      title: '下午时段',
                      badge: {
                        type: 'slogan',
                        value: current === 1 ? '' : (hasPMScheduledTime ? '可约' : '约满'),// current-1表示下午时间，选中下午不展示角标
                        shape: 'oval',
                        className: hasPMScheduledTime ? 'has-scheduled-time' : 'no-scheduled-time',
                        color: hasPMScheduledTime ? '#ff8844' : '#a6a6a6'
                      }
                    }
                  ]}
                  onClick={this.handleClick.bind(this)}
                >
                  <MUTabsPane current={current} index={0}>
                    <MUView>{this.getTimeBlockView(AMTimeList, current)}</MUView>
                  </MUTabsPane>
                  <MUTabsPane current={current} index={1}>
                    <MUView>{this.getTimeBlockView(PMTimeList, current)}</MUView>
                  </MUTabsPane>
                </MUTabs>

                <MUView className="order-tip">
                  请保持预约期间通讯正常，以便审批专家顺利联系您
                </MUView>
              </MUView>

              <MUView className={`${hasScheduledTime ? 'order-button' : 'order-button-full'} `}>
                <MUButton
                  type="primary"
                  className="order-button"
                  onClick={this.orderSubmit}
                  beaconId={changeOrderTime ? beaconIds.changeOrderBtn : beaconIds.orderBtn}
                  parentId={beaconPageId}
                >
                  {changeOrderTime ? '确定修改' : '确定预约'}
                </MUButton>
              </MUView>
            </MUView>
          )}

          {/* 预约成功弹窗 */}
          {isSuccessOpened && (
            <MUView>
              <MUModal
                type="success"
                isOpened={isSuccessOpened}
                beaconId="OpenSuccessModal"
                closeOnClickOverlay={false}
                title="预约成功"
                content={`你已预约${orderTime} 的审批专家服务，可在我的-"我的预约"中查看`}
                confirmText="我知道了"
                className="success_modal"
                onConfirm={() => this.successConfirm()}
              />
            </MUView>
          )
          }

          {/* 操作结果提示弹窗 */}
          {
            openCloseModal && (
              <MUModal
                type={showSuccessModal ? 'success' : 'tip'}
                closeOnClickOverlay={false}
                beaconId="tipOrderDialog"
                beaconContent={{ cus: { text: closeWebViewcontent } }}
                isOpened={openCloseModal}
                title={closeWebViewTitle}
                content={closeWebViewcontent}
                confirmText="我知道了"
                onConfirm={() => this.modalConfirm()}
              />
            )
          }
        </MUView>
      </MUView>
    );
  }
}
