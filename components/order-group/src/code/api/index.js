import { fetch, apiHost } from '@mu/business-basic';

export const getUserInfo = async () => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.userInformation.getUserInfo`);
    return res || {};
  } catch (e) {
    return {};
  }
};

export const unBind = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.login.logout`, {
      data
    });
    return res;
  } catch (e) {
    return null;
  }
};

export const queryAppointStatus = async (params) => {
  const res = await fetch(`${apiHost.mgp}?operationId=mucfc.apply.acpt.queryCreditStatus`, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    }
  });
  return res;
};


/**
 * 专家预约，改约，取消预约接口
 */
export const submitAppointInfo = async (params, autoToast = true) => {
  const res = await fetch(`${apiHost.mgp}?operationId=mucfc.apply.operate.submitAppointInfo`, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoToast
  });
  return res;
};
