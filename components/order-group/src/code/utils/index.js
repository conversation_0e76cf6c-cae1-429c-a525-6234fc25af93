import {
  getCurrentPageUrl
} from '@mu/madp-utils';
import { track, dispatchTrackEvent, EventTypes } from '@mu/madp-track';
import dayjs from 'dayjs';
/**
 * 业务组件需要用到的公共方法封装
 * */

const addZero = (num) => {
  return (`0${num}`).slice(-2);
}

/**
 * 手动埋点方法，在基础方法上拓展，封装添加模块Id、PageId
 * @param {string} pageId 页面Id
 * @param {object} beaconObj 埋点数据对象，跟dispatchTrackEvent定义的所需参数一样
 */
export const sendTrackBeacon = (pageId, beaconObj) => {
  let moduleId = process.env.TRACK_MODULE_ID; // 默认工程moduleId
  // 兼容小程序moduleId获取（路由上带）
  const currentRoute = getCurrentPageUrl();
  if (
    (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'alipay') && typeof currentRoute === 'string'
  ) {
    const matchRes = currentRoute.match(/^\/?(.+)\/pages\/.+$/);
    if (matchRes) {
      // eslint-disable-next-line prefer-destructuring
      moduleId = matchRes[1];
    }
  }
  let beaconId = moduleId;
  // 传参只有pageId
  if (pageId && !beaconObj.beaconId) {
    beaconId = `${beaconId}.${pageId}`;
  }
  // 传参已拼pageId
  if (!pageId && beaconObj.beaconId) {
    beaconId = `${beaconId}.${beaconObj.beaconId}`;
  }
  // 传参分开pageId和beaconId
  if (pageId && beaconObj.beaconId) {
    beaconId = `${beaconId}.${pageId}.${beaconObj.beaconId}`;
  }

  dispatchTrackEvent({
    ...beaconObj,
    beaconId
  });
};

export const getweek = (date) => {
  let weekDay = {
    0: '周日',
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
  };
  const nowdate = new Date();
  const year = nowdate.getFullYear();
  const month = addZero(nowdate.getMonth() + 1);
  const day = addZero(nowdate.getDate());
  const today = `${year}-${month}-${day}`;
  if (today === date) {
    return '今天';
  }
  return weekDay[(new Date(date)).getDay()];
}
/**
* 将时间范围字符串转换为时间戳
* @param {string} input - 时间范围字符串 "YYYY-MM-DD HH:mm-HH:mm" 
* @returns {Array} - 返回 [开始时间戳, 结束时间戳]；
*/
export const convertStringToTimestamp = (input) => {
  const result = {
    startTimestamp: '',
    endTimestamp: '',
  }
  // 如果输入是字符串，将其转换为时间戳
  if (typeof input === 'string') {
    // 分割日期和时间范围
    const [datePart, timeRangePart] = input.split(' ');

    // 分割开始和结束时间
    const [startTime, endTime] = timeRangePart.split('-');

    // 构建完整的日期时间字符串
    const startDateTime = `${datePart} ${startTime}`;
    const endDateTime = `${datePart} ${endTime}`;

    // 转换为时间戳（毫秒）
    const startTimestamp = dayjs(startDateTime).valueOf();
    const endTimestamp = dayjs(endDateTime).valueOf();
    result.startTimestamp = startTimestamp;
    result.endTimestamp = endTimestamp;
  }
  return result
}
/**
* 将时间戳转换为时间范围字符串
* @param {Array} input - 时间范围是[开始时间戳, 结束时间戳]
* @returns {string} -返回格式化的时间范围字符串时 "YYYY-MM-DD HH:mm-HH:mm" 
*/
export const convertTimeToString = (input) => {
  let formatTime = ''
  if (Array.isArray(input) && input.length === 2) {
    const [startTimestamp, endTimestamp] = input;
    if (!startTimestamp || !endTimestamp) {
      return formatTime;
    }
    // 转换为 dayjs 对象
    const startDate = dayjs(startTimestamp);
    const endDate = dayjs(endTimestamp);

    // 提取日期部分（两个时间戳应该是同一天）
    const datePart = startDate.format('YYYY-MM-DD');

    // 提取时间部分
    const startTimePart = startDate.format('HH:mm');
    const endTimePart = endDate.format('HH:mm');
    formatTime = `${datePart} ${startTimePart}-${endTimePart}`;
  }
  return formatTime
}
