/**
 * 含有lui的业务组件测试页面
 * */
import Madp, { Component } from '@mu/madp';
import { getEnv } from '@mu/madp-utils';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView, MUButton } from '@mu/zui';
import OrderGroup from '../code';
import BusinessComponent from '../code';
import './index.scss';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  constructor() {
    super();
    this.state = {
      isWEAPP: getEnv() === Madp.ENV_TYPE.WEAPP,
      isALIPAY: getEnv() === Madp.ENV_TYPE.ALIPAY,
      isWEB: getEnv() === Madp.ENV_TYPE.WEB,
    };
  }

  onButtonClick = (event) => {
    const {
      isWEB,
    }
      = this.state;
    if (isWEB) {
      alert('您点击了按钮！');
    } else {
      Madp.showModal({ content: '您点击了按钮！', showCancel: false });
    }
  };

  render() {
    const { bannerWithoutBorder } = this.state;
    return (
      <MUView
        className="component_demo_page"
      >
        <OrderGroup orderCaseType='LIMIT_EXPERT' changeOrderTime />
      </MUView>
    );
  }
}
