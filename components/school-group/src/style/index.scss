@import '~@mu/zui/dist/style/mixins/index.scss';
@import '~@mu/zui/dist/style/variables/default.scss';

.school-group {
  position: static;

  &:not(:last-child) {
    @include hairline-bottom-relative($left: 30px);
  }

  .vague-search { // 因为最后一个，不会触发默认的not:last-child样式，需要补
    @include hairline-bottom-relative($left: 30px);
  }

  .vague-search__scroll-view{
    width: 75%
  }
}

// 兼容小程序，参考zui直接用了标签名，但开发工具会有warning
@include weappBottomLine("vague-search", ".at-input", 30px);

.vague-search {
  position: relative;

  &:not(:last-child) {
    @include hairline-bottom-relative($left: 30px);
  }
  // 小程序额外多一层标签
  &__scroll-view {
    position: absolute;
    max-height: 352px;
    width: calc(100% - 204px);
    left: 174px;
    right: 30px;
    z-index: 20;
    background: #fff;
    box-shadow: 0 8px 8px 0 rgba(0, 0, 0, 0.08);
    border-radius: 0 0 $border-radius-md $border-radius-md;

    &-main {
      .item {
        font-size: $font-size-lg;
        color: #333;
        padding: 0 0 0 32px;
        height: 87px;
        line-height: 87px;

        &:not(:last-child) {
          @include hairline-bottom-relative($left: 30px);

          &::after {
            right: 30px;
          }
        }

        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      // 小程序额外多一层标签
      mu-view:not(:last-child) view {
        @include hairline-bottom-relative($left: 30px);

        &::after {
          right: 30px;
        }
      }
    }
  }

  .at-input {
    margin-bottom: 0;
  }

  .taro-scroll{
    &::-webkit-scrollbar { // 滚动条主体
      display: inherit;
      width: 8px;
    }
    // &::-webkit-scrollbar-track { } // 滚动轨迹，默认空白
    &::-webkit-scrollbar-thumb // 滚动条块
    {
      border-radius: 4px;
      background-color: #E5E5E5;
    }
  }

  &__overlay {
    position: fixed;
    z-index: 10;
    background: transparent;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
  }

  .hide__overlay {
    display: none;
  }
}
