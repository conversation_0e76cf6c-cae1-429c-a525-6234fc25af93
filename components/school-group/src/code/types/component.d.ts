import { MouseEvent, ComponentClass } from 'react';
import { CommonEventFunction } from '@tarojs/components/types/common';

import MUComponent from './base';

interface SchoolNameCode {
  /** 学校代码 */
  schoolCode: string,
  /** 学校名称 */
  schoolName: string,
}

/**
 * 地区代码
 */
interface AreaCodeObj {
  /** 省代码 */
  provinceCode: string,
  /** 城市代码 */
  cityCode: string,
}

export interface MUSchoolGroupProps extends MUComponent {
  /**
   * 地区代码
   */
   areaValue?: AreaCodeObj;
  /**
   * 学校名
   */
  schoolNameValue?: string,
  /**
   * 学校名代码和名称
   */
  schoolNameCodeList?: Array<SchoolNameCode>
  /**
   * 学校名列表
   */
  schoolNameList?: Array<string>
  /**
   * 学校值变动回调
   */
  handleSchoolNameChange?: Function;
  /**
   * 地区值变动回调
   */
  handleChooseArea?: Function;

  /**
   * 无地区、学校时，点击学校输入框提示语
   */
  emptySchoolAreaText?: string,

  /**
   * 无学校时，点击学校输入框提示语
   */
  noSchoolText?: string,
}

export interface MUSchoolGroupState {
  /**
  * 学校名是否可编辑
  */
  isSchoolEditable?: boolean;
  /**
  * 地区值
  */
  areaValue?: AreaCodeObj;
  /**
  * 学校名
  */
  schoolNameValue?: string,
  /**
  * 学校名代码和名称
  */
  schoolNameCodeList?: Array<SchoolNameCode>
  /**
  * 学校名列表
  */
  schoolNameList?: Array<string>
}

declare const MUSchoolGroupComponent: ComponentClass<MUSchoolGroupProps>;

export default MUSchoolGroupComponent;
