import Madp, { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUAddressPicker } from '@mu/zui';
import VagueSearch from '@mu/vague-search';
import { findObjFromArr } from './utils';
import { MUSchoolGroupProps, MUSchoolGroupState } from './types/component';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}
@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'SchoolGroup', // 就是当前组件类名
}))
export default class SchoolGroup extends Component<MUSchoolGroupProps, MUSchoolGroupState> {
  public static options = {
    addGlobalClass: true
  }
  config: any = {
    styleIsolation: 'shared'
  }
  public static defaultProps: MUSchoolGroupProps;

  public static propTypes: InferProps<MUSchoolGroupProps>;

  public constructor(props: MUSchoolGroupProps) {
    super(props);
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
      schoolNameValue = ''
    } = this.props;
    this.state = {
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      areaValue,
      // eslint-disable-next-line taro/duplicate-name-of-state-and-props
      schoolNameValue,
      schoolNameCodeList: [],
      schoolNameList: []
    };
  }

  public async componentDidMount() {
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
    } = this.props;
    if (!areaValue.provinceCode && !areaValue.cityCode) {
      return;
    }
  }


  private onChooseArea = async (val) => {
    const {
      handleSchoolNameChange,
      handleChooseArea
    } = this.props;
    const keyArray = [['provinceCode', 'provinceName'], ['cityCode', 'cityName']];
    const area = {
      provinceCode: '',
      cityCode: ''
    };
    val.forEach((addressValue, addressValueIndex) => {
      Object.keys(addressValue).forEach((key, index) => {
        if (keyArray[addressValueIndex]) {
          area[(keyArray[addressValueIndex])[index]] = addressValue[key];
        }
      });
    });
    // 更新组件内学校地区和学校名称。改变学校地区后学校名称自动清空
    this.setState({
      areaValue: area,
      schoolNameValue: ''
    });
    // 将最新的学校地区和学校名称传给父组件
    handleChooseArea && handleChooseArea(area);
    handleSchoolNameChange && handleSchoolNameChange({ schoolName: '', schoolCode: '' });
  }

  /**
   * 处理学校名称不可点击时
  */
  private whenSchoolUneditable = () => {
    console.log('whenSchoolUneditable');
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
    } = this.state;
    const {
      emptySchoolAreaText = '请先选择你学校的所在地区',
      noSchoolText = '该地区无可选择的零零花支持院校'
    } = this.props;
    const { provinceCode, cityCode } = areaValue;
    if (!provinceCode || !cityCode) {
      Madp.showToast({
        title: emptySchoolAreaText,
        icon: 'none',
        duration: 2000
      });
    } else {
      Madp.showToast({
        title: noSchoolText,
        icon: 'none',
        duration: 2000
      });
    }
  };

  /**
   * 处理学校名称更新
   * @param {string} val 学校名称
   */
  private onSchoolNameChange = (val) => {
    const {
      schoolNameCodeList
    } = this.state;
    const {
      handleSchoolNameChange
    } = this.props;
    // 更新组件内学校地区
    const { target } = findObjFromArr(schoolNameCodeList, { schoolName: val });
    this.setState({
      schoolNameValue: val,
    })
    // 将最新的学校名称传给父组件
    handleSchoolNameChange && handleSchoolNameChange({
      schoolName: val,
      schoolCode: target.schoolCode || '00000' // 取不到则为其他学校，兜底学校编码
    })
  };

  public render(): JSX.Element {
    const {
      areaValue = {
        provinceCode: '',
        cityCode: ''
      },
      schoolNameValue,
    } = this.state;
    const {
      beaconId = 'schoolSelector'
    } = this.props;
    const isSchoolEditable = !!(areaValue.cityCode && areaValue.provinceCode);
    return (
      <MUView className="school-group">
        <MUAddressPicker
          title="学校地区"
          placeholder="请选择学校所在地区"
          maxDeep="2"
          value={areaValue}
          beaconId={`${beaconId}SchoolSchoolAddressArea`}
          onClickItem={(val) => { this.onChooseArea(val); }}
        />
        {/* 学校名称自动补全 */}
        <VagueSearch
          title="学校名称"
          placeholder="请填写所在学校"
          beaconId={`${beaconId}SchoolName`}
          value={schoolNameValue}
          editable={isSchoolEditable}
          handleClick={this.whenSchoolUneditable}
          handleChange={this.onSchoolNameChange}
          optionList={[]}
        />
      </MUView>
    );
  }
}
