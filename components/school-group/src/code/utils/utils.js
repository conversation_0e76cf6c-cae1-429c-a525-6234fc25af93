/**
 * 查找对象数组中，含有目标对象的项
 * @param {Array} rangeArray 待查对象数组
 * @param {object} targetObj 目标对象
 */
const findObjFromArr = (rangeArray, targetObj) => {
  let index = 0;
  let target = {};
  if (rangeArray && rangeArray.length) {
    target = rangeArray.filter((item, i) => {
      const keys = Object.keys(targetObj);
      if (keys.every((k) => String(item[k]).includes(String(targetObj[k])))) {
        index = i;
        return true;
      }
      return false;
    })[0] || {};
  }
  return {
    target,
    index
  };
};

export {
  findObjFromArr
}
