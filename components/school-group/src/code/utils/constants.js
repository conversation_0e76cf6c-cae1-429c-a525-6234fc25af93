import {
  getEnv,
  isAlipay,
} from '@mu/madp-utils';
import appInfos from './app-config-infos';

// 当前环境
const env = process.env.BUILD_ENV || getEnv();

/**
 * 前端域名
 */
let domain = 'https://m-zl.mucfc.com';
if (env !== 'prod') {
  domain = `https://m-zl-${env}.cfcmu.cn`;
}

const urlDomain = domain;

/**
 * 后端域名
 */
let apiHostList = {};

// 判断是mucfc还是sbd
// sbd
if (process.env.APP === 'sbd') {
  apiHostList = appInfos.apiHost;
}
// mucfc
/**
 * 获取后端生产域名
 * @param module api模块
 */
function getBackendDomainProd(module) {
  // 对支付宝环境的处理
  const pre = (isAlipay() || process.env.TARO_ENV === 'alipay') && (module !== 'pay' && module !== 'mgp' && module !== 'forum')
    ? `${module}-ali`
    : module;
  return `https://${pre}.api.mucfc.com`;
}

/**
 * 获取测试的后端域名
 * @param module api模块
 * @param env 环境
 * @returns
 */
function getBackendDomainDev(module) {
  // 对支付宝环境的处理
  let pre = (isAlipay() || process.env.TARO_ENV === 'alipay') && (module !== 'pay' && module !== 'mgp' && module !== 'forum')
    ? `${module}-ali-${env}`
    : `${module}-${env}`;
  // 对pos模块的特殊处理，维持现有逻辑
  pre = module === 'pay' && /uat|st2/i.test(env) ? `${env}-${module}` : pre;
  pre = module === 'pay' && /se\d?/i.test(env) ? 'buy' : pre;

  if (process.env.TARO_ENV === 'h5') {
    let { protocol } = window.location;
    protocol = protocol === 'offline:' ? 'https:' : protocol;
    return `${protocol}//${pre}.api.cfcmu.cn`;
  }
  // 小程序使用https
  return `https://${pre}.api.cfcmu.cn`;
}

if (process.env.APP === 'mucfc') {
  const apiModules = ['mgp', 'auth', 'forum', 'mgr', 'apply', 'user'];
  apiHostList[env] = apiHostList[env] || {};
  apiModules.forEach((m) => {
    if (env === 'prod') {
      // 生产
      apiHostList[env][m] = getBackendDomainProd(m);
    } else {
      // 测试
      apiHostList[env][m] = getBackendDomainDev(m);
    }
  });
}

/**
  * api hosts
  * eg. https://mgp.api.mucfc.com'
  */
const apiHost = apiHostList[env];

export {
  urlDomain,
  apiHost,
};
