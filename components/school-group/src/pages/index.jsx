/**
 * 含有lui的业务组件测试页面
 * */
import { Component } from '@mu/madp';
import { disableTrackAlert } from '@mu/madp-track';
import { MUView } from '@mu/zui';
import SchoolGroup from '../code';
import './index.scss';

// 测试页面禁用埋点警告弹窗
disableTrackAlert();

export default class DemoPage extends Component {
  onChooseSchoolArea = (val) => {
    console.log('onChooseSchoolArea val', val)
  };

  handleSchoolNameChange = (val) => {
    console.log('handleSchoolNameChange val', val)
  }

  render() {
    return (
      <MUView
        className="component_demo_page"
      >
        <SchoolGroup
          areaValue={{}}
          schoolNameValue=""
          // 给非受控组件设置key：后台返回初始值后，子组件重新构建。（使子组件state的初始值可以获取最新props）
          key
          handleChooseArea={(val) => { this.onChooseSchoolArea(val) }}
          handleSchoolNameChange={this.handleSchoolNameChange}
          beaconId='school'
          emptySchoolAreaText='先选地区'
          noSchoolText='没有学校'
        />
      </MUView>
    );
  }
}
