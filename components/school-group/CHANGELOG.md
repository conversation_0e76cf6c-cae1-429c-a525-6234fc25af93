# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="1.0.1-beta.5"></a>
## 1.0.1-beta.5 (2023-06-01)


### Bug Fixes

* 删除多余渠道默认主题色 ([20cc9e0](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/20cc9e0))
* 删除多余主题颜色 ([3c686db](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3c686db))
* 删除生活号渠道默认主题 ([d8a6037](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d8a6037))
* 新增上传图片样例 ([ef72536](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ef72536))
* 新增图片列表 ([22b7614](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/22b7614))
* 新增样式 ([95d84d7](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/95d84d7))
* 修复不自动倒计时 bug ([995260f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/995260f))
* 修复步骤条样式 ([640cd3f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/640cd3f))
* 修复轮播组件样式被覆盖 bug ([9a60fef](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/9a60fef))
* 修改步骤条颜色 ([f4bcc6a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f4bcc6a))
* 修改步骤条样式 ([3a7496c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3a7496c))
* 修改步骤条主题色 ([37b4b47](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/37b4b47))
* 修改步骤条icon大小 ([e2311af](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e2311af))
* 学校增加其他选项 ([661b13d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/661b13d))
* 主题色适配 ([5cd7816](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5cd7816))


### Features

* 调试波形图 ([f44d4bd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f44d4bd))
* 调整 logout 函数 ([f91102e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f91102e))
* 调整部分逻辑 ([7afabcf](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7afabcf))
* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd))
* 调整网络监听函数 ([8c97264](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8c97264))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb))
* 函数抽取 ([3fdf675](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3fdf675))
* 进度条样式修改 ([0567a90](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0567a90))
* 删除一些 console ([fb5f35e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fb5f35e))
* 删除click-selector多余主题色 ([11807be](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/11807be))
* 生成物料信息文件 ([a01a54b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/a01a54b))
* 添加 im 消息返回 4 的类型 ([be47312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/be47312))
* 添加埋点信息 ([4e87be3](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/4e87be3))
* 添加埋点信息 ([7715908](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7715908))
* 添加申请表单提交页面模板物料 ([e1f20b1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e1f20b1))
* 添加申请业务基础页面模板物料 ([8b1431f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b1431f))
* 添加申请业务通用结果页面模板物料 ([df434f8](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/df434f8))
* 添加视频组件 ([b541022](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b541022))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c1))
* 添加网络监听事件 ([db5c5bb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/db5c5bb))
* 添加销毁实例函数 ([b206232](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b206232))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d6))
* 添加重试功能 ([881bb52](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/881bb52))
* 完善 actionSheetPicker ([c725488](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c725488))
* 完善 click-selector ([5a74d35](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5a74d35))
* 完善 hospital-selector ([059b99e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/059b99e))
* 完善 readme ([e8da312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e8da312))
* 完善 result-loading ([645cc56](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/645cc56))
* 完善 school-selector ([50bc92c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/50bc92c))
* 完善 text-progress-bar ([cc2d42f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/cc2d42f))
* 完善 vague-search ([f48eb4d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f48eb4d))
* 完善 vague-search ([2af22e4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/2af22e4))
* 完善区块物料 ([bc7deee](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bc7deee))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2f))
* 修改 zego 推流 url ([0c40a62](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0c40a62))
* 修改 zegoUrl ([7466c33](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7466c33))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e6210))
* 学校组件新增其他学校编码 ([c48338b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c48338b))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a))
* 增加按钮 ([f05a31b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f05a31b))
* 增加编译 copy ([136108e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/136108e))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d4))
* 增加超时返回和稍等 loading ([834e110](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/834e110))
* 增加倒计时组件 ([3709e31](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3709e31))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2))
* 增加网络切换错误码 ([5468819](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5468819))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e924))
* click-selector 和 result-loading 图片替换为线上 ([6b7839c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/6b7839c))



<a name="1.0.1-beta.4"></a>
## 1.0.1-beta.4 (2023-05-25)


### Bug Fixes

* 删除多余渠道默认主题色 ([20cc9e0](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/20cc9e0))
* 删除多余主题颜色 ([3c686db](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3c686db))
* 删除生活号渠道默认主题 ([d8a6037](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d8a6037))
* 新增上传图片样例 ([ef72536](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ef72536))
* 新增图片列表 ([22b7614](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/22b7614))
* 新增样式 ([95d84d7](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/95d84d7))
* 修复不自动倒计时 bug ([995260f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/995260f))
* 修复步骤条样式 ([640cd3f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/640cd3f))
* 修复轮播组件样式被覆盖 bug ([9a60fef](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/9a60fef))
* 修改步骤条颜色 ([f4bcc6a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f4bcc6a))
* 修改步骤条样式 ([3a7496c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3a7496c))
* 修改步骤条主题色 ([37b4b47](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/37b4b47))
* 学校增加其他选项 ([661b13d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/661b13d))
* 主题色适配 ([5cd7816](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5cd7816))


### Features

* 调试波形图 ([f44d4bd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f44d4bd))
* 调整 logout 函数 ([f91102e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f91102e))
* 调整部分逻辑 ([7afabcf](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7afabcf))
* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd))
* 调整网络监听函数 ([8c97264](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8c97264))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb))
* 函数抽取 ([3fdf675](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3fdf675))
* 进度条样式修改 ([0567a90](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0567a90))
* 删除一些 console ([fb5f35e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fb5f35e))
* 删除click-selector多余主题色 ([11807be](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/11807be))
* 生成物料信息文件 ([a01a54b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/a01a54b))
* 添加 im 消息返回 4 的类型 ([be47312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/be47312))
* 添加埋点信息 ([4e87be3](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/4e87be3))
* 添加埋点信息 ([7715908](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7715908))
* 添加申请表单提交页面模板物料 ([e1f20b1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e1f20b1))
* 添加申请业务基础页面模板物料 ([8b1431f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b1431f))
* 添加申请业务通用结果页面模板物料 ([df434f8](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/df434f8))
* 添加视频组件 ([b541022](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b541022))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c1))
* 添加网络监听事件 ([db5c5bb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/db5c5bb))
* 添加销毁实例函数 ([b206232](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b206232))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d6))
* 添加重试功能 ([881bb52](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/881bb52))
* 完善 actionSheetPicker ([c725488](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c725488))
* 完善 click-selector ([5a74d35](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5a74d35))
* 完善 hospital-selector ([059b99e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/059b99e))
* 完善 readme ([e8da312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e8da312))
* 完善 result-loading ([645cc56](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/645cc56))
* 完善 school-selector ([50bc92c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/50bc92c))
* 完善 text-progress-bar ([cc2d42f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/cc2d42f))
* 完善 vague-search ([f48eb4d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f48eb4d))
* 完善 vague-search ([2af22e4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/2af22e4))
* 完善区块物料 ([bc7deee](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bc7deee))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2f))
* 修改 zego 推流 url ([0c40a62](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0c40a62))
* 修改 zegoUrl ([7466c33](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7466c33))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e6210))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a))
* 增加按钮 ([f05a31b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f05a31b))
* 增加编译 copy ([136108e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/136108e))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d4))
* 增加超时返回和稍等 loading ([834e110](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/834e110))
* 增加倒计时组件 ([3709e31](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3709e31))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2))
* 增加网络切换错误码 ([5468819](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5468819))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e924))
* click-selector 和 result-loading 图片替换为线上 ([6b7839c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/6b7839c))



<a name="1.0.1-beta.3"></a>
## 1.0.1-beta.3 (2023-05-25)


### Bug Fixes

* 删除多余渠道默认主题色 ([20cc9e0](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/20cc9e0))
* 删除多余主题颜色 ([3c686db](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3c686db))
* 删除生活号渠道默认主题 ([d8a6037](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d8a6037))
* 新增上传图片样例 ([ef72536](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ef72536))
* 新增图片列表 ([22b7614](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/22b7614))
* 新增样式 ([95d84d7](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/95d84d7))
* 修复不自动倒计时 bug ([995260f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/995260f))
* 修复步骤条样式 ([640cd3f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/640cd3f))
* 修复轮播组件样式被覆盖 bug ([9a60fef](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/9a60fef))
* 修改步骤条颜色 ([f4bcc6a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f4bcc6a))
* 修改步骤条样式 ([3a7496c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3a7496c))
* 修改步骤条主题色 ([37b4b47](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/37b4b47))
* 学校增加其他选项 ([661b13d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/661b13d))
* 主题色适配 ([5cd7816](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5cd7816))


### Features

* 调试波形图 ([f44d4bd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f44d4bd))
* 调整 logout 函数 ([f91102e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f91102e))
* 调整部分逻辑 ([7afabcf](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7afabcf))
* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd))
* 调整网络监听函数 ([8c97264](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8c97264))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb))
* 函数抽取 ([3fdf675](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3fdf675))
* 进度条样式修改 ([0567a90](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0567a90))
* 删除一些 console ([fb5f35e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fb5f35e))
* 删除click-selector多余主题色 ([11807be](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/11807be))
* 生成物料信息文件 ([a01a54b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/a01a54b))
* 添加 im 消息返回 4 的类型 ([be47312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/be47312))
* 添加埋点信息 ([4e87be3](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/4e87be3))
* 添加埋点信息 ([7715908](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7715908))
* 添加申请表单提交页面模板物料 ([e1f20b1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e1f20b1))
* 添加申请业务基础页面模板物料 ([8b1431f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b1431f))
* 添加申请业务通用结果页面模板物料 ([df434f8](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/df434f8))
* 添加视频组件 ([b541022](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b541022))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c1))
* 添加网络监听事件 ([db5c5bb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/db5c5bb))
* 添加销毁实例函数 ([b206232](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b206232))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d6))
* 添加重试功能 ([881bb52](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/881bb52))
* 完善 actionSheetPicker ([c725488](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c725488))
* 完善 click-selector ([5a74d35](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5a74d35))
* 完善 hospital-selector ([059b99e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/059b99e))
* 完善 readme ([e8da312](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e8da312))
* 完善 result-loading ([645cc56](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/645cc56))
* 完善 school-selector ([50bc92c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/50bc92c))
* 完善 text-progress-bar ([cc2d42f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/cc2d42f))
* 完善 vague-search ([f48eb4d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f48eb4d))
* 完善 vague-search ([2af22e4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/2af22e4))
* 完善区块物料 ([bc7deee](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bc7deee))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2f))
* 修改 zego 推流 url ([0c40a62](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/0c40a62))
* 修改 zegoUrl ([7466c33](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7466c33))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e6210))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a))
* 增加按钮 ([f05a31b](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f05a31b))
* 增加编译 copy ([136108e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/136108e))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d4))
* 增加超时返回和稍等 loading ([834e110](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/834e110))
* 增加倒计时组件 ([3709e31](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3709e31))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2))
* 增加网络切换错误码 ([5468819](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5468819))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e924))
* click-selector 和 result-loading 图片替换为线上 ([6b7839c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/6b7839c))



<a name="1.0.1-beta.2"></a>
## 1.0.1-beta.2 (2022-08-25)


### Bug Fixes

* 修复不自动倒计时 bug ([995260f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/995260f))
* 修复轮播组件样式被覆盖 bug ([9a60fef](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/9a60fef))


### Features

* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb))
* 添加申请表单提交页面模板物料 ([e1f20b1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/e1f20b1))
* 添加申请业务基础页面模板物料 ([8b1431f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8b1431f))
* 添加申请业务通用结果页面模板物料 ([df434f8](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/df434f8))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c1))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d6))
* 完善 actionSheetPicker ([c725488](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/c725488))
* 完善 click-selector ([5a74d35](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/5a74d35))
* 完善 hospital-selector ([059b99e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/059b99e))
* 完善 result-loading ([645cc56](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/645cc56))
* 完善 school-selector ([50bc92c](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/50bc92c))
* 完善 text-progress-bar ([cc2d42f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/cc2d42f))
* 完善 vague-search ([f48eb4d](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f48eb4d))
* 完善 vague-search ([2af22e4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/2af22e4))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2f))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e6210))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d4))
* 增加倒计时组件 ([3709e31](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/3709e31))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e924))



<a name="1.0.1-beta.1"></a>
## 1.0.1-beta.1 (2022-08-02)


### Features

* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c1))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d6))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09))
* 修改 vague-search ([f904f2f](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/f904f2f))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e6210))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d4))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e924))



<a name="1.0.1-beta.0"></a>
## 1.0.1-beta.0 (2022-07-27)


### Features

* 调整图片位置 ([b3638dd](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b3638dd))
* 调整文件位置 ([97e73eb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/97e73eb))
* 添加图片上传示例 ([b01125e](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/b01125e))
* 添加图片上传组件 ([53b58c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/53b58c1))
* 添加选择组件 ([fea68d6](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/fea68d6))
* 完善文档 ([00206cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/00206cb))
* 物料库初始化 ([ac04f09](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ac04f09))
* 修改样式 ([44e6210](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/44e6210))
* 增加 VagueSearch 组件 ([d7e2d3a](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d7e2d3a))
* 增加测一测区块 ([7ebc7d4](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/7ebc7d4))
* 增加地区选择学校组件 ([ae47e55](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/ae47e55))
* 增加地区选择医院及科室组件 ([bcc38c1](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/bcc38c1))
* 增加公共介绍区块 ([d68d1c2](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/d68d1c2))
* 增加文字步骤条组件 ([08556cb](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/08556cb))
* 增加学历选择面板选择组件 ([8a7e924](https://gitfe.mucfc.com:8082/abf/abf-apply-fe/abf-material-fe/commits/8a7e924))
