# 学校选择

@mu/school-group



根据地区选择学校

## 预览图

预览截图，需手动替换为实际的预览图，图片名不可重命名

![screenshot.png](http://unpkg.mucfc.com/@mu/school-group/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/school-group`

### 样式引入

`@import "~@mu/school-group/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/school-group',
            '@mu\\school-group'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\school-group',
            '@mu/school-group',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/school-group':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/school-group')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ----  |
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | 'schoolSelector' | 埋点 |
| areaValue  | object | 否  | {provinceCode: '',cityCode: ''} | 地区编码值 |
| schoolNameCodeList  | Array | 否  | [] | 学校名称编码列表 |
| schoolNameValue  | string | 否  | '' | 学校名 |
| schoolNameList  | string | 否  | [] | 学校名称列表 |
| handleChooseArea  | Function | 否  | () => {} | 选择地区回调 |
| handleSchoolNameChange  | Function | 否  | () => {} | 学校值变动回调 |
| emptySchoolAreaText  | string | 否  | '请先选择你学校的所在地区' | 无地区、学校时，点击学校输入框提示语 |
| noSchoolText  | string | 否  | '该地区无可选择的零零花支持院校' | 无学校时，点击学校输入框提示语 |

