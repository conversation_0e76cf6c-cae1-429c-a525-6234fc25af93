# result-loading

@mu/result-loading

intro component

## 预览图

预览截图，需手动替换为实际的预览图，图片名不可重命名

![screenshot.png](http://unpkg.mucfc.com/@mu/result-loading/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/result-loading`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
  // h5的业务模块
  h5: {
      esnextModules: [
        ...
        '@mu/result-loading',
        '@mu\\result-loading'
      ]
    },
  // 小程序壳工程
  weapp: {
    compile: {
      include: [
        ...
        '@mu\\result-loading',
        '@mu/result-loading',
      ]
    }
  },
  alias: {
    ...
    '@mu/[业务模块名]/result-loading':
    process.env.TARO_ENV === 'h5'
      ? path.resolve(__dirname, '..', 'node_modules', '@mu/result-loading')
      : path.resolve(__dirname, '..', 'src/[业务模块名]/components/result-loading')
  },
}
```

### 接入示例

```javascript
import ResultLoading from '@mu/result-loading'; // 使用appworks插件接入时会自动引入组件
```

需要在壳工程或者业务引入样式文件
```javascript
@import "~@mu/result-loading/dist/style/index.scss";
```

```javascript

class Demo extends Madp.Component {

  constructor(props) {
    super(props);
  }

  render() {
    return (
      <ResultLoading
        beaconId="ResultLoading" // 必填，组件埋点id
        mainTitle="额度计算中..."  // 选填 主标题
        subTitle="结果马上出来请勿离开" // 选填 副标题
        mainTitleColor="#FF8800"  // 选填 主标题颜色
        subTitleColor="#333" // 选填 副标题颜色
        totalCount={15} //选填 倒计时总数
        totalCountColor='#333'   // 倒计时数字颜色
        size={80}   // 选填 倒计时圈大小
        lineColor="#E5E5E5" // 选填 倒计时外圈默认颜色
        lineActive="#3477FF"  // 选填 倒计时激活部分颜色
        count={1}  // 选填 倒计时间隔数
        onfinish={() => {console.log('finish')}} // 选填 倒计时结束后的回调
      />
    )
  }

}
```
### 配置说明

| 属性/方法 | 类型 |  描述 |   是否必填  |  默认 |
| -------------- | --------| --------------------------| ----| ----|
| className  | string | 否  | '' | 类名 |
| customStyle  | string | 否  | '' | 样式 |
| beaconId  | string | 否  | 'resultLoading' | 埋点 |
| mainTitle | string | 主标题文案 | 否 | 额度计算中... |
| subTitle | string | 副标题文案 | 否 | 结果马上出来请勿离开|
| mainTitleColor | string | 主标题颜色 | 否 | #FF8800|
| subTitleColor | string | 副标题颜色 | 否 | #333|
| totalCountColor | string | 倒计时数字颜色 | 否 | #333|
| totalCountColor | number | 倒计时总数 | 否 | 15 |
| size | number | 倒计时圈大小 | 否 | 80 |
| lineColor | string | 倒计时外圈默认颜色 | 否 | #E5E5E5 |
| lineActive | string | 倒计时激活部分颜色 | 否 | #3477FF |
| count | number | 倒计时间隔数 | 否 | 1 |
| onfinish | function | 倒计时结束后的回调 | 否 | 无 |