import { ComponentClass } from 'react';
import MUComponent from './base';

export interface MULoadingResultProps extends MUComponent {
  initCountingNum?: number,
  interval?: number,
  queryResult?: Function,
  primaryTitle?: string,
  secondTitle?: string,
  thirdTitle?: string,
  countOverShowRefresh?: boolean,
  hasQueryResult?: boolean,
}

export interface MULoadingResultState {
  countingNum?: number;
  showRefresh?: boolean;
  interval?: number;
  showNum?: boolean;
  primaryTitle?: string,
  secondTitle?: string,
}

declare const MULoadingResultComponent: ComponentClass<MULoadingResultProps>;

export default MULoadingResultComponent;
