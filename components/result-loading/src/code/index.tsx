import { Component } from '@mu/madp';
import {
  MU<PERSON>iew,
  MUImage,
  MUButton,
  MUText,
} from '@mu/zui';
import {
  track, disableTrackAlert
} from '@mu/madp-track';
import { MULoadingResultProps, MULoadingResultState } from './types/component';
import { themeColor } from './utils';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}
const loading = 'https://file.mucfc.com/abf/1/38/202301/202301171024285803b1.png';
const cmbLogo = 'https://file.mucfc.com/abf/1/25/202406/2024061316201572a74d.png';

disableTrackAlert()

const animationParams = {
  viewbox: '40 40 90 90',
  cx: '45',
  cy: '45',
  r: '40',
  strokeWidth: '1',
  strokeScendWidth: '2',
  strokeDasharray: '15 2350',
  transform: 'matrix(0,-1,1,0,0,90)',
  strokeFirst: '#E5E5E5',
  strokeScend: themeColor.brand
};

const loadingImage = themeColor && themeColor.image;

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'ResultLoading', // 就是当前组件类名
}))
export default class ResultLoading extends Component<MULoadingResultProps, MULoadingResultState> {
  public static options = {
    addGlobalClass: true
  }
  config: any = {
    styleIsolation: 'shared'
  }
  private countingDownTimer: any = null;

  private queryTimer: any = null;

  private queryIndex = 0;

  constructor(props) {
    super(props);
    const { initCountingNum = 10, interval = 2, primaryTitle, secondTitle } = props;
    this.state = {
      countingNum: initCountingNum, // 倒计时时间
      showRefresh: false,
      interval: interval, // 请求结果间隔时间
      showNum: true, // 是否展示数字倒计时
      primaryTitle: primaryTitle || '额度计算中...',
      secondTitle: secondTitle || '结果马上出来，请勿离开',
    };
  }

  componentDidMount() {
    this.startCounting();
  }

  componentDidUpdate(prevProps) {
    // 如果修改组件的入参，则重置state，重新开启计时器
    if (this.props.initCountingNum !== prevProps.initCountingNum) {
      const { primaryTitle, secondTitle, initCountingNum, interval } = this.props;
      const newState = {};
      // 更新 primaryTitle 和 secondTitle
      newState.primaryTitle = primaryTitle || '信息安全保障中...';
      newState.secondTitle = secondTitle || '还剩3秒，请勿离开';
      newState.countingNum = initCountingNum;
      newState.interval = interval;
      newState.showNum = false;
      this.setState(newState);
      this.startCounting();
    }
  }

  startCounting = () => {
    const { initCountingNum = 10 } = this.props;
    if (this.countingDownTimer) {
      clearInterval(this.countingDownTimer);
    }
    if (this.queryTimer) {
      clearTimeout(this.queryTimer);
    }
    this.setState({
      countingNum: initCountingNum,
      showRefresh: false,
    }, () => {
      this.countingDown();
      this.queryIndex = 0;
      this.queryTimer = setTimeout(this.query, 0);
    });
  }

  countingDown = () => {
    this.countingDownTimer = setInterval(() => {
      const { countingNum } = this.state;
      const newCountingNum = countingNum - 1;
      if (newCountingNum <= 0) {
        clearInterval(this.countingDownTimer);
      }
      this.setState({
        countingNum: newCountingNum
      });
    }, 1000);
  }

  query = async () => {
    const {
      queryResult, interval = 2, countOverShowRefresh, hasQueryResult = false
    } = this.props;
    const startTime = new Date().getTime();
    const { countingNum } = this.state;
    // 判断是否为最后一次请求
    const isLastQuery = countingNum <= 0;
    this.queryIndex += 1;
    const hasResult = await queryResult(isLastQuery, this.queryIndex);
    if (hasResult || hasQueryResult) { // 已出结果
      clearInterval(this.countingDownTimer);
      clearTimeout(this.queryTimer);
      return;
    }
    if (isLastQuery) { // 最后一次请求
      this.setState({
        showRefresh: countOverShowRefresh
      });
      clearTimeout(this.queryTimer);
      return;
    }
    const endTime = new Date().getTime();
    const passTime = endTime - startTime;
    const delayTime = passTime > interval * 1000 ? 0 : interval * 1000 - passTime;
    this.queryTimer = setTimeout(() => {
      this.query();
    }, delayTime);
  }

  render() {
    const {
      showRefresh,
      showNum,
      primaryTitle,
      secondTitle,
      countingNum
    } = this.state;
    const {
      beaconId = 'resultLoading',
      className = '',
      customStyle = {},
      thirdTitle = '',
      showCmbLogo,
    } = this.props;

    return (
      <MUView
        className={`result-loading ${className}`}
        style={customStyle}
      >
        <MUView className="result-loading__inner">
          {process.env.TARO_ENV === 'h5' ? (
            <svg
              width="160"
              height="160"
              viewbox={animationParams.viewbox}
              className="result-loading__circular"
            >
              <circle
                cx={animationParams.cx}
                cy={animationParams.cy}
                r={animationParams.r}
                strokeWidth={animationParams.strokeWidth}
                stroke={animationParams.strokeFirst}
                fill="none"
              />
              <circle
                className="result-loading__path"
                cx={animationParams.cx}
                cy={animationParams.cy}
                r={animationParams.r}
                strokeWidth={animationParams.strokeScendWidth}
                stroke={animationParams.strokeScend}
                fill="none"
                strokeDasharray={animationParams.strokeDasharray}
                strokeDashoffset=""
                transform={animationParams.transform}
              />
            </svg>
          ) : (
            <MUImage
              className="result-loading__count"
              src={loading}
            />
          )}
          {showNum && <MUText className={process.env.TARO_ENV === 'h5' ? 'result-loading__num' : 'result-loading__num result-loading__weapnum'}>{countingNum > 0 ? `${countingNum}` : '1'}</MUText>}
          {!showNum && <MUImage className="result-loading__image" src={loadingImage}></MUImage>}
        </MUView>

        {showCmbLogo && <MUImage src={cmbLogo} className="result-loading__cmb-logo" />}

        <MUView className="result-loading__text">{primaryTitle}</MUView>
        {secondTitle ? <MUView className="result-loading__tips">{secondTitle}</MUView> : null}
        {thirdTitle ? (<MUView className="result-loading__desc">{thirdTitle}</MUView>) : null}
        {showRefresh && (
          <MUView className="button-block">
            <MUButton
              type="primary"
              beaconId={beaconId}
              onClick={this.startCounting}
            >
              刷新
            </MUButton>
          </MUView>
        )}
      </MUView>
    );
  }
}
