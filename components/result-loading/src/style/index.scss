@import '~@mu/zui/dist/style/variables/default.scss';

.result-loading {
  box-sizing: border-box;

  &__icon {
    width: 200px;
    height: 200px;
    margin: 339px auto 0;
  }

  text-align: center;

  &__text {
    font-weight: bold;
    color: $color-text-title;
    font-size: 40px;
    line-height: 40px;
  }

  &__count {
    width: 160px;
    height: 160px;
    margin: 60px auto;
    background: center center no-repeat;
    background-size: contain;
    animation: rotateNew 1s linear infinite;
  }

  &__tips {
    margin-top: $spacing-h-xl;
    font-size: $font-size-base;
    color: $color-orange;
  }

  &__desc {
    margin: $spacing-v-sm $spacing-h-xl;
    font-size: $font-size-base;
    line-height: 42px;
    color: $color-text-title-secondary;
  }

  &__inner {
    position: relative;
    margin-top: 80px;
  }

  &__cmb-logo {
    height: 80px;
    width: 355px;
    margin: -45px 0 26px 0;
  }

  &__num {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 70px;
    font-weight: 200;
  }

  &__weapnum {
    top: 47%;
  }

  &__image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 94px;
    height: 94px;
  }

  &__circular {
    display: block;
    animation: rotateloading 1s linear infinite;
    transform-origin: center center;
    width: 90PX;
    height: 90PX;
    margin: 0 auto;
    margin-bottom: 50px;
    transform: rotate(-0.05deg);
  }

  &__path {
    stroke-linecap: round;
  }
  @keyframes rotateloading {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes rotateNew {
    0%,
    100% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .button-block {
    padding: 30px 40px;
  }
}
