# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.


<a name="1.2.0"></a>
# 1.2.0 (2022-06-10)


### Bug Fixes

* 表单物料提交相关兼容 ([52fec57](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/52fec57))
* 更新下 ([504fd41](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/504fd41))
* 滚动穿透 ([322ca39](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/322ca39))
* 加入弹框消失 ([dc02a7e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/dc02a7e))
* 兼容微信小程序 ([e9cc323](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/e9cc323))
* 年从边界切回别的无法更新月列表问题 ([2aae512](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/2aae512))
* 区块滚动选择和天数的问题 ([75dd22e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/75dd22e))
* 使用项目模板创建的工程无法启动的bug ([dd6ce68](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/dd6ce68))
* 微信小程序样式问题 ([235346a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/235346a))
* 相关兼容问题 ([9f042e8](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9f042e8))
* 新增 ([ebcd99a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ebcd99a))
* 新增 ([6bd33f6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6bd33f6))
* 修复相等日期后选择的会被认为是较大日期无法确认的bug ([62bcad0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/62bcad0))
* 选择对齐 ([ddd41e7](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ddd41e7))
* 样式 ([a57cabb](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a57cabb))
* 样式加载 ([91c994f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/91c994f))
* 样式修改 ([48bc3ab](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/48bc3ab))
* 样式wenti ([32a7e18](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/32a7e18))
* build ([68e2f4b](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/68e2f4b))
* conflict ([d349117](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d349117))
* dayjs ([b343a94](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b343a94))
* debonce ([e24d3a6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/e24d3a6))
* form-input 完善 ([9fdcd95](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9fdcd95))
* result-loading样式问题 ([33c2e06](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/33c2e06))
* loadingresult埋点 ([19f2102](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/19f2102))
* range-pick-mini ([59dbf13](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/59dbf13))
* update ([b1325b0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b1325b0))
* year ([16463f1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/16463f1))


### Features

*  物料数据发布 ([338db79](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/338db79))
*  修改 npmignore 文件 ([76849ba](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/76849ba))
*  修改 TaroSDK配置 ([31a4450](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/31a4450))
* 测试区块组件代码提交 ([1c3e222](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1c3e222))
* 初始化物料项目 ([ac66f1e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ac66f1e))
* 多端兼容 ([0e17181](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/0e17181))
* 更新工程配置 ([f03de07](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f03de07))
* 更新开发文档 ([286ca90](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/286ca90))
* 更新物料数据 ([d91100e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d91100e))
* 更新物料数据 ([095cf1a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/095cf1a))
* 更新物料数据 ([2a669c9](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/2a669c9))
* 更新项目模板文档 ([5091dfe](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/5091dfe))
* 更新ledapage package.json ([f7dda30](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f7dda30))
* 更新page开发文档 ([6f16b57](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6f16b57))
* 固定依赖版本 ([93f7183](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/93f7183))
* 基础物料包项目完善 ([669d9c3](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/669d9c3))
* 基础组件lui/zui处理完成 ([ef08792](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ef08792))
* 兼容小程序样式 ([6f5fa2e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6f5fa2e))
* 兼容safari浏览器的 new Date 问题 ([d2cc149](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d2cc149))
* 简化组件逻辑 ([840c9a2](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/840c9a2))
* 解决小程序端滚动条问题 ([c6a3ac1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/c6a3ac1))
* 精度为month时日期格式化优化 ([f6fa13a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f6fa13a))
* 配置修改 ([f9d8acd](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f9d8acd))
* 切换类型是时间初始值选中问题 ([c4d5123](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/c4d5123))
* 区块文档完善 ([1c01e5f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1c01e5f))
* 删除不必要的依赖 ([4ee2b30](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4ee2b30))
* 删除无用文件 ([bf9f47c](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/bf9f47c))
* 删除状态管理 ([9953fcf](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9953fcf))
* 删除abc.json ([fe26572](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/fe26572))
* 删除build.json ([494e029](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/494e029))
* 生成物料 ([14da209](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/14da209))
* 生成物料数据 ([7b99d9e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/7b99d9e))
* 生成物料数据 ([5d4ab71](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/5d4ab71))
* 生成物料数据 ([4b655e6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4b655e6))
* 生成物料数据 ([e9d73b0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/e9d73b0))
* 生成物料数据 ([410e09b](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/410e09b))
* 生成物料数据 ([cc9f594](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/cc9f594))
* 生成物料数据 ([1a1b246](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1a1b246))
* 生成物料数据 ([f5577d1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f5577d1))
* 生成物料数据 ([919eec8](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/919eec8))
* 时间范围选择 first commit ([5c5f33e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/5c5f33e))
* 提交SDK模板文件 ([b41dd57](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b41dd57))
* 添加可选时间范围限制逻辑 ([6d415a3](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6d415a3))
* 添加埋点逻辑 ([028259f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/028259f))
* 添加注释 ([cc7b2ab](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/cc7b2ab))
* 添加注释 ([4ee7656](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4ee7656))
* 完善文档 ([2375ea1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/2375ea1))
* 文档更新 ([04c0498](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/04c0498))
* 物料发布 ([1b210cf](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1b210cf))
* 项目模板添加项目开发说明模板 ([abf2fcc](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/abf2fcc))
* 项目模板文件重命名，修改项目模板名称 ([fa3ffdc](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/fa3ffdc))
* 小程序初始化时间选择器跳转问题兼容 ([81e3045](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/81e3045))
* 小程序兼容问题 ([b34fd64](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b34fd64))
* 小程序壳工程模板 ([4d5ce11](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4d5ce11))
* 小程序无法正确跳转到指定位置的问题 ([4777ae1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4777ae1))
* 小程序依赖配置问题 ([68bde80](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/68bde80))
* 新增常用服务样例区块 ([52deb8c](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/52deb8c))
* 新增带状态管理和网络请求的组件demo ([c3a76a8](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/c3a76a8))
* 新增倒计时 ([68ef7d0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/68ef7d0))
* 修改发布命令 ([a062257](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a062257))
* 修改滚动样式及交互等 ([15f4b82](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/15f4b82))
* 修改物料 ([cd04377](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/cd04377))
* 修改物料 readme ([bfab10f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/bfab10f))
* 修改项目模板图标 ([1424714](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1424714))
* 修改小程序模板 ([9a0f9af](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9a0f9af))
* 修改小程序模板图标 ([fd7b47b](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/fd7b47b))
* 修改样式问题 ([d6d6b7d](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d6d6b7d))
* 修改业务组件打包配置 ([96846ad](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/96846ad))
* 修改demo截图 ([b17df05](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b17df05))
* 修改leda-page文件名 ([48dfdca](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/48dfdca))
* 修改ledapage物料 readme ([12b0038](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/12b0038))
* 修改materialConfig.template字段，删除分支 ([816dc95](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/816dc95))
* 业务组件使用新模板 ([59cc516](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/59cc516))
* 优化确认点击提示效果 ([a131734](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a131734))
* 预览图 ([f36cd46](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f36cd46))
* 增加小程序插件项目模板 ([19d1d1c](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/19d1d1c))
* 增加自定义时间显示格式化 ([3a58c30](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/3a58c30))
* 增加mini app 项目开发文档 ([36e8baa](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/36e8baa))
* 增加page类型物料（带leda的页面） ([0914c46](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/0914c46))
* 增加taro app 项目开发文档 ([7b58786](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/7b58786))
* 增加TaroSDK项目模板 ([11b3560](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/11b3560))
* component增加截图 ([45edece](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/45edece))
* component增加外部可调用的方法 ([3a3e3ee](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/3a3e3ee))
* form-argeement-submit ([8c4a283](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/8c4a283))
* gitignore ([8ba8555](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/8ba8555))
* ledapage 类名规范 ([02835d2](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/02835d2))
* ledapage模板依赖更新 ([1468afa](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1468afa))
* onConfirm 回调增加参数 ([da2b148](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/da2b148))
* page 物料 packagejson files增加 screenshot ([4adca7e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4adca7e))
* README文件完善 ([6fe1ad3](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6fe1ad3))
* readme组件名称修改 ([91e4945](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/91e4945))
* taro项目模板 ([57cc9e6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/57cc9e6))
* taro项目模板配置文件 ([390c102](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/390c102))
* taro项目模板添加ejs文件 ([8a36a6d](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/8a36a6d))
* taro项目模板增加预览图 ([3bf3ede](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/3bf3ede))
* unpkg链接替换成公司unpkg地址 ([a824459](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a824459))
* w物料数据 ([44e9a7d](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/44e9a7d))



<a name="1.1.0"></a>
# 1.1.0 (2022-06-10)


### Bug Fixes

* 表单物料提交相关兼容 ([52fec57](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/52fec57))
* 更新下 ([504fd41](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/504fd41))
* 滚动穿透 ([322ca39](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/322ca39))
* 加入弹框消失 ([dc02a7e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/dc02a7e))
* 兼容微信小程序 ([e9cc323](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/e9cc323))
* 年从边界切回别的无法更新月列表问题 ([2aae512](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/2aae512))
* 区块滚动选择和天数的问题 ([75dd22e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/75dd22e))
* 使用项目模板创建的工程无法启动的bug ([dd6ce68](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/dd6ce68))
* 微信小程序样式问题 ([235346a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/235346a))
* 相关兼容问题 ([9f042e8](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9f042e8))
* 新增 ([ebcd99a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ebcd99a))
* 新增 ([6bd33f6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6bd33f6))
* 修复相等日期后选择的会被认为是较大日期无法确认的bug ([62bcad0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/62bcad0))
* 选择对齐 ([ddd41e7](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ddd41e7))
* 样式 ([a57cabb](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a57cabb))
* 样式加载 ([91c994f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/91c994f))
* 样式修改 ([48bc3ab](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/48bc3ab))
* 样式wenti ([32a7e18](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/32a7e18))
* build ([68e2f4b](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/68e2f4b))
* conflict ([d349117](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d349117))
* dayjs ([b343a94](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b343a94))
* debonce ([e24d3a6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/e24d3a6))
* form-input 完善 ([9fdcd95](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9fdcd95))
* result-loading样式问题 ([33c2e06](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/33c2e06))
* loadingresult埋点 ([19f2102](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/19f2102))
* range-pick-mini ([59dbf13](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/59dbf13))
* update ([b1325b0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b1325b0))
* year ([16463f1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/16463f1))


### Features

*  物料数据发布 ([338db79](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/338db79))
*  修改 npmignore 文件 ([76849ba](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/76849ba))
*  修改 TaroSDK配置 ([31a4450](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/31a4450))
* 测试区块组件代码提交 ([1c3e222](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1c3e222))
* 初始化物料项目 ([ac66f1e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ac66f1e))
* 多端兼容 ([0e17181](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/0e17181))
* 更新工程配置 ([f03de07](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f03de07))
* 更新开发文档 ([286ca90](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/286ca90))
* 更新物料数据 ([d91100e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d91100e))
* 更新物料数据 ([095cf1a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/095cf1a))
* 更新物料数据 ([2a669c9](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/2a669c9))
* 更新项目模板文档 ([5091dfe](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/5091dfe))
* 更新ledapage package.json ([f7dda30](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f7dda30))
* 更新page开发文档 ([6f16b57](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6f16b57))
* 固定依赖版本 ([93f7183](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/93f7183))
* 基础物料包项目完善 ([669d9c3](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/669d9c3))
* 基础组件lui/zui处理完成 ([ef08792](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/ef08792))
* 兼容小程序样式 ([6f5fa2e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6f5fa2e))
* 兼容safari浏览器的 new Date 问题 ([d2cc149](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d2cc149))
* 简化组件逻辑 ([840c9a2](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/840c9a2))
* 解决小程序端滚动条问题 ([c6a3ac1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/c6a3ac1))
* 精度为month时日期格式化优化 ([f6fa13a](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f6fa13a))
* 配置修改 ([f9d8acd](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f9d8acd))
* 切换类型是时间初始值选中问题 ([c4d5123](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/c4d5123))
* 区块文档完善 ([1c01e5f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1c01e5f))
* 删除不必要的依赖 ([4ee2b30](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4ee2b30))
* 删除无用文件 ([bf9f47c](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/bf9f47c))
* 删除状态管理 ([9953fcf](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9953fcf))
* 删除abc.json ([fe26572](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/fe26572))
* 删除build.json ([494e029](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/494e029))
* 生成物料 ([14da209](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/14da209))
* 生成物料数据 ([7b99d9e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/7b99d9e))
* 生成物料数据 ([5d4ab71](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/5d4ab71))
* 生成物料数据 ([4b655e6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4b655e6))
* 生成物料数据 ([e9d73b0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/e9d73b0))
* 生成物料数据 ([410e09b](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/410e09b))
* 生成物料数据 ([cc9f594](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/cc9f594))
* 生成物料数据 ([1a1b246](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1a1b246))
* 生成物料数据 ([f5577d1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f5577d1))
* 生成物料数据 ([919eec8](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/919eec8))
* 时间范围选择 first commit ([5c5f33e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/5c5f33e))
* 提交SDK模板文件 ([b41dd57](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b41dd57))
* 添加可选时间范围限制逻辑 ([6d415a3](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6d415a3))
* 添加埋点逻辑 ([028259f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/028259f))
* 添加注释 ([cc7b2ab](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/cc7b2ab))
* 添加注释 ([4ee7656](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4ee7656))
* 完善文档 ([2375ea1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/2375ea1))
* 文档更新 ([04c0498](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/04c0498))
* 物料发布 ([1b210cf](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1b210cf))
* 项目模板添加项目开发说明模板 ([abf2fcc](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/abf2fcc))
* 项目模板文件重命名，修改项目模板名称 ([fa3ffdc](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/fa3ffdc))
* 小程序初始化时间选择器跳转问题兼容 ([81e3045](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/81e3045))
* 小程序兼容问题 ([b34fd64](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b34fd64))
* 小程序壳工程模板 ([4d5ce11](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4d5ce11))
* 小程序无法正确跳转到指定位置的问题 ([4777ae1](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4777ae1))
* 小程序依赖配置问题 ([68bde80](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/68bde80))
* 新增常用服务样例区块 ([52deb8c](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/52deb8c))
* 新增带状态管理和网络请求的组件demo ([c3a76a8](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/c3a76a8))
* 新增倒计时 ([68ef7d0](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/68ef7d0))
* 修改发布命令 ([a062257](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a062257))
* 修改滚动样式及交互等 ([15f4b82](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/15f4b82))
* 修改物料 ([cd04377](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/cd04377))
* 修改物料 readme ([bfab10f](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/bfab10f))
* 修改项目模板图标 ([1424714](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1424714))
* 修改小程序模板 ([9a0f9af](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/9a0f9af))
* 修改小程序模板图标 ([fd7b47b](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/fd7b47b))
* 修改样式问题 ([d6d6b7d](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/d6d6b7d))
* 修改业务组件打包配置 ([96846ad](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/96846ad))
* 修改demo截图 ([b17df05](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/b17df05))
* 修改leda-page文件名 ([48dfdca](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/48dfdca))
* 修改ledapage物料 readme ([12b0038](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/12b0038))
* 修改materialConfig.template字段，删除分支 ([816dc95](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/816dc95))
* 业务组件使用新模板 ([59cc516](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/59cc516))
* 优化确认点击提示效果 ([a131734](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a131734))
* 预览图 ([f36cd46](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/f36cd46))
* 增加小程序插件项目模板 ([19d1d1c](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/19d1d1c))
* 增加自定义时间显示格式化 ([3a58c30](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/3a58c30))
* 增加mini app 项目开发文档 ([36e8baa](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/36e8baa))
* 增加page类型物料（带leda的页面） ([0914c46](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/0914c46))
* 增加taro app 项目开发文档 ([7b58786](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/7b58786))
* 增加TaroSDK项目模板 ([11b3560](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/11b3560))
* component增加截图 ([45edece](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/45edece))
* component增加外部可调用的方法 ([3a3e3ee](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/3a3e3ee))
* form-argeement-submit ([8c4a283](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/8c4a283))
* gitignore ([8ba8555](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/8ba8555))
* ledapage 类名规范 ([02835d2](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/02835d2))
* ledapage模板依赖更新 ([1468afa](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/1468afa))
* onConfirm 回调增加参数 ([da2b148](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/da2b148))
* page 物料 packagejson files增加 screenshot ([4adca7e](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/4adca7e))
* README文件完善 ([6fe1ad3](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/6fe1ad3))
* readme组件名称修改 ([91e4945](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/91e4945))
* taro项目模板 ([57cc9e6](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/57cc9e6))
* taro项目模板配置文件 ([390c102](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/390c102))
* taro项目模板添加ejs文件 ([8a36a6d](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/8a36a6d))
* taro项目模板增加预览图 ([3bf3ede](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/3bf3ede))
* unpkg链接替换成公司unpkg地址 ([a824459](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/a824459))
* w物料数据 ([44e9a7d](https://gitfe.mucfc.com:8082/mdp/mdp-devtool/mdp-material-base-fe/commits/44e9a7d))
