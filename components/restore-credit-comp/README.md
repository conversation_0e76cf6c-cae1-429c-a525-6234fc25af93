# restore-credit-comp

@mu/restore-credit-comp



intro component

## 预览图

预览截图，需手动替换为实际的预览图，图片名不可重命名

![screenshot.png](http://unpkg.mucfc.com/@mu/restore-credit-comp/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/restore-credit-comp`

### 样式引入

`@import "~@mu/restore-credit-comp/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/restore-credit-comp',
            '@mu\\restore-credit-comp'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\restore-credit-comp',
            '@mu/restore-credit-comp',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/restore-credit-comp':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/restore-credit-comp')
    },
}
```

在这里写业务组件详细接入文档
