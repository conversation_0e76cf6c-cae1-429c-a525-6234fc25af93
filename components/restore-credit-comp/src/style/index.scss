/* write style here */
/* write style here */
@import './limit-modal.scss';

.restore-credit-comp {
    .fast-restore {
      width: 100%;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      // width: 690px;
  
      &-bg1 {
        width: 162px;
        height: 143px;
        margin-top: 20px;
        margin-bottom: 50px;
      }
  
      &-bg2 {
        width: 162px;
        height: 143px;
        margin-top: 20px;
        margin-bottom: 50px;
      }
  
      .icon {
        position: absolute;
        right: 0;
        top: 0;
      }
  
      &-time {
        text-align: center;
        margin: 50px 0 100px 0;
        display: flex;
  
        &-num {
          color: #3477ff;
          text-align: center;
          font-size: 28px;
          font-weight: 600;
          font-family: "PingFang SC";
          line-height: 32px;
        }
        &-text{
          color: #808080;
          text-align: center;
          font-size: 28px;
          font-weight: 400;
          font-family: "PingFang SC";
          line-height: 32px;
        }
      }
  
      &-tip-block{
        display: flex;
  
        .tip{
          width: 28px;
          height: 32px;
          margin: -5px 10px 0 0;
          display: flex;
        }
  
        .text{
          opacity: 1;
          color: #a6a6a6;
          text-align: left;
          font-size: 24px;
          font-weight: 400;
          font-family: "PingFang SC";
          line-height: 24px;
        }
  
      }
  
      &-text {
        opacity: 1;
        color: #333333;
        text-align: center;
        font-size: 28px;
        font-weight: 400;
        font-family: 'PingFang SC';
      }
    }
  
    .pt {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .pt-modal{
      width: 690px;
      margin-left: 20px;
    }
  
    .text-block {
      position: absolute;
      top: 20%;
      width: 610px;
      left: 50%;
      transform: translateX(-50%);
  
    }
  
    .mu-dialog__overlay{
      backdrop-filter:blur(15px);
    }
  
  }