.restore-increase-dialog {
    backdrop-filter:blur(15px);
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
  
    .dialog-container {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 750px;
      height: auto;
      background-color: #fff;
      border-radius: 30px 30px 0 0;
      padding-bottom: 100px;
      box-sizing: border-box;
  
      .dialog-close-btn {
        position: absolute;
        right: 30px;
        top: 30px;
        color: #b2b2b2;
      }
  
      .dialog-title {
        width: 100%;
        height: 60px;
        line-height: 60px;
        color: #000000;
        font-size: 40px;
        font-weight: 600;
        border-radius: 30px 30px 0 0;
        padding: 38px 0 40px 40px;
        box-sizing: border-box;
        background: linear-gradient(180deg, #E1EAFF 0%, #fff 100%);
        display: flex;
        text-align: left;
      }

      .dialog-subtitle {
        width: 100%;
        height: 38px;
        line-height: 38px;
        color: #808080;
        font-size: 26px;
        font-weight: 400;
        border-radius: 30px 30px 0 0;
        padding: 30px 0 50px 40px;
        box-sizing: border-box;
        text-align: left;
      }
  
      .dialog-content_wrapper {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
  
        // 兼容微信小程序：不可改为odd，因为只选择元素1与3
        > mu-view:nth-child(1) {
          flex: 1 !important;
        }
  
        > mu-view:nth-child(3) {
          flex: 1 !important;
        }
  
        //...end
  
        .increase-content {
          width: 710px;
          height: 324px;
          margin: 10px auto 30px;
          background-image: url('https://file.mucfc.com/cop/0/0/202402/20240202093621d897f4.png');
          background-size: 100% auto;
          background-repeat: no-repeat;
          display: flex;
          flex-direction: row;
  
          .content-before {
            width: 284px;
            height: 324px;
            position: relative;
  
            &_title {
              color: #e7aa87;
              font-size: 24px;
              font-weight: 600;
              position: absolute;
              left: 87px;
              top: 166px;
            }
  
            &_price {
              color: #e7aa87;
              font-size: 28px;
              font-weight: 600;
              position: absolute;
              left: 87px;
              top: 202px;
            }
          }
  
          .content-current {
            width: 426px;
            position: relative;
  
            &_expried {
              position: absolute;
              right: 40px;
              top: 24px;
              padding: 6px 20px;
              color: #fff;
              font-size: 20px;
              border-radius: 0 24px 0 20px;
              background-color: #FEAA73;
            }
  
            &_title {
              color: #ffffff;
              font-size: 28px;
              font-weight: 600;
              position: absolute;
              left: 72px;
              top: 92px;
            }
  
            &_price {
              color: #fff;
              font-size: 50px;
              font-weight: 600;
              position: absolute;
              left: 72px;
              top: 130px;
  
              &-fix {
                top: 135px;
              }
            }
  
            &_subtitle {
              color: #fff;
              font-size: 22px;
              position: absolute;
              left: 72px;
              top: 208px;
            }
          }
        }
      }
  
      .dialog-content_btn_wrapper {
        position: relative;
  
        .dialog-content_btn {
          width: 670px;
          height: 100px;
          border-radius: 100px;
          font-size: 36px;
          font-weight: 600;
          margin: 0 auto;
        }
  
        .dialog-content_save {
          position: absolute;
          right: 0;
          top: -60px;
          width: 240px;
          height: 60px;
          line-height: 60px;
          text-align: center;
          border-radius: 60px;
          font-size: 24px;
          background-color: orange;
        }
      }

      .dialog-content_btn_tip {
        position: absolute;
        background-color: #FF8844;
        height: 32px;
        opacity: 1;
        color: #ffffff;
        text-align: right;
        font-size: 20px;
        font-weight: 600;
        line-height: 32px;
        padding: 5px 10px;
        right: 30px;
        top: -20px;
        z-index: 10;
        border-radius: 15px 15px 15px 0;
      }
  
      .adjust-dialog__infomation {
        width: 100%;
        position: absolute;
        display: flex;
        align-items: center;
        bottom: 42px;
        left: 20px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 20px;
        color: #a6a6a6;
        text-align: center;
        line-height: 30px;
        z-index: 500;
  
        &--tag {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 68px;
          height: 32px;
          background: rgba(0, 0, 0, 0.30);
          border-radius: 4px;
          color: #fff;
          margin-left: 16px;
        }
  
        // --兼容微信小程序
        > mu-view:nth-child(2) {
          flex: 1;
        }
  
        &--text {
          flex: 1;
          text-align: left;
          margin-left: 12px;
  
          &.long-infomation {
            margin-right: 0;
          }
        }
      }
    }
  
    .info-dialog {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 750px;
      height: auto;
      background-color: #fff;
      border-radius: 30px;
  
      .close-btn {
        position: absolute;
        right: 28px;
        top: 28px;
        font-size: 12px;
        color: #b2b2b2;
      }
  
      .dialog-title {
        width: 100%;
        height: 120px;
        line-height: 120px;
        text-align: center;
        font-size: 36px;
        color: #333333;
        font-weight: 600;
        border-bottom: 1px solid #eee;
      }
  
      .dialog-content {
        padding: 30px 30px;
        box-sizing: border-box;
        font-size: 28px;
        color: #808080;
        text-align: left;
      }
  
      .mu-dialog__content {
        padding: 0;
      }
    }
  }
  
  .dialog-unshow {
    display: none;
  }
  