import { fetch, apiHost } from '@mu/business-basic';

const operationId = {
  /* 查询授信产品合同信息 */
  composeCase: 'mucfc.apply.apply.composeCase',
  applyInfo: 'mucfc.apply.apply.applyInfo',
  // 新账户信息查询
  queryAccount: 'mucfc.loan.account.queryAccount',
}
const API_URL = {
  COMPOSE_CASE: `${apiHost.mgp}/?operationId=${operationId.composeCase}`,
  GET_APPLY_INFO: `${apiHost.mgp}/?operationId=${operationId.applyInfo}`,
  QUERY_ACCOUNT: `${apiHost.mgp}/?operationId=${operationId.queryAccount}`,
}
export const getUserInfo = async () => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.userInformation.getUserInfo`);
    return res || {};
  } catch (e) {
    return {};
  }
};

/**
 * 获取用户借款券种类
 */
 export const getComposeCase = async (params, mapCode, autoLoading = false) => {
  const res = await fetch(API_URL.COMPOSE_CASE, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading,
    reqEnvParams: {
      mapCode
    },
  });
  return res;
};

export const unBind = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.login.logout`, {
      data
    });
    return res;
  } catch (e) {
    return null;
  }
};

/**
 * 查询applyinfo信息
 */
 export const checkApplyInfo = async (params, autoLoading = true, autoToast = true, mapCode) => {
  const applyInfo = await fetch(API_URL.GET_APPLY_INFO, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading,
    autoToast,
    reqEnvParams: {
      mapCode
    },
  });
  return applyInfo;
};

export const queryAccount = async (params) => {
  try {
    const result = await fetch(API_URL.QUERY_ACCOUNT, {
      method: 'POST',
      data: {
        data: {
          ...params
        }
      },
      autoLoading: false,
    });
    return result;
  } catch (error) {
    console.warn(error);
  }
};