import { MouseEvent, ComponentClass } from 'react';
import { CommonEventFunction } from '@tarojs/components/types/common';

import MUComponent from './base';

export interface SubComp1Props extends MUComponent {
  /**
   * 是否开启
   * @default false
   */
  count?: number;

  subComp1Store?: object;

  /**
   * 点击按钮触发事件
   */
  onClick?: CommonEventFunction;

}

export interface SubComp1State {
  isWEB?: boolean;
  isWEAPP?: boolean;
  isALIPAY?: boolean;
  isShow?: boolean;
}

declare const SubCompDemo: ComponentClass<SubComp1Props>;

export default SubCompDemo;
