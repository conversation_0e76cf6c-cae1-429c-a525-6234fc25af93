import Taro from "@tarojs/taro";
import Madp from '@mu/madp';
import PropTypes from 'prop-types';
import {
  urlDomain,
} from '../../utils';
import { isMuapp } from '@mu/madp-utils';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import {
    MUView, MUIcon, MUButton, MURichText
  } from '@mu/zui';

const propTypes = {
  data: PropTypes.object,
};

const defaultProps = {
  data: {},
};

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'RestoreCreditComp', // 就是当前组件类名
}))

class LimitPriceModal extends Taro.Component {
  constructor(props) {
    super(props);
    this.state = {
      isShow: true
    }
  }

  componentDidMount() {
    PropTypes.checkPropTypes(propTypes, this.props, 'prop', 'LimitPriceModal');
  }

  static options = {
    addGlobalClass: true
  };

  config = {
    styleIsolation: 'shared'

    /**
     * 点击合同checkbox
     */
  };

  onClose = () => {
    dispatchTrackEvent({
      target: this,
      beaconId: 'CloseLimitModal',
      event: EventTypes.EV,
    })
    const {
      limitModalCallback
    } = this.props;
    console.log('onClose', limitModalCallback);
    // 执行业务的关闭额度领取弹窗回调，当前用于提额降息中心重新弹出kyc
    limitModalCallback && limitModalCallback('close')
    this.setState({
      isShow: false
    })
  }

  onBtnClick = () => {
    dispatchTrackEvent({
      target: this,
      beaconId: 'ClickLimitModalBtn',
      event: EventTypes.EV,
      beaconContent: {
        cus: {
          text: '立即借款'
        }
      }
    });
    Madp.navigateTo({
      url: `${urlDomain}/${Madp.getChannel()}/loan/#/pages/index/index?cashLoanMode=1&mtago=14016.01.18`,
      useAppRouter: isMuapp()
    });
    this.setState({
      isShow: false
    })
  }

  render() {
    const {
      data,
      limitModalBtnType = 'loan'
    } = this.props;
    const {
        isShow
    } = this.state;
    const subtitle = `恭喜，获得<span style="color:#FF8844">${(Number(data && data.limitNum) - Number(data && data.beforeLimitNum)).toFixed(2)}元</span>提额！`

    return isShow ? (<MUView
    className="restore-increase-dialog"
    onTouchMove={(e) => { e.preventDefault(); e.stopPropagation(); }}
  >
    <MUView className="dialog-container">
      <MUIcon
        className="dialog-close-btn"
        value="close2"
        size={18}
        onClick={this.onClose}
      />
      <MURichText className="dialog-title" nodes={subtitle} />
      <MUView className="dialog-subtitle">今日专享福利已到账</MUView>
      <MUView className="dialog-content_wrapper">
        <MUView className="increase-content">
          <MUView className="content-before">
            <MUView className="content-before_title">原额度</MUView>
            <MUView className="content-before_price">
              ¥
              {data && data.beforeLimitNum}
            </MUView>
          </MUView>
          <MUView className="content-current">
            {data && data.tmpLimitExpireDate && <MUView className="content-current_expried">{data && data.tmpLimitExpireDate}</MUView>}
            <MUView className="content-current_title">现额度提升至</MUView>
            <MUView className={`content-current_price ${process.env.TARO_ENV === 'alipay' ? 'content-current_price-fix' : ''}`}>
              ¥
              {data && data.limitNum}
            </MUView>
            <MUView className="content-current_subtitle">
              可用额度为¥
              {data && data.availLimit}
            </MUView>
          </MUView>
        </MUView>
      </MUView>
      <MUView className="dialog-content_btn_wrapper">
      {data && data.bubbleText && <MUView className="dialog-content_btn_tip">{data && data.bubbleText}</MUView>}
          {limitModalBtnType === 'loan' && <MUButton onClick={this.onBtnClick} type="primary" beaconId="clickRestoreLimitModalBtn" className="dialog-content_btn" customStyle={{
            backgroundColor: data && data.themeColor, borderColor: data && data.themeColor
          }}>
            立即借款
          </MUButton>}
          {limitModalBtnType === 'close' && <MUButton onClick={this.onClose} type="primary" beaconId="clickRestoreLimitModalBtn" className="dialog-content_btn" customStyle={{
            backgroundColor: data && data.themeColor, borderColor: data && data.themeColor
          }}>
            我知道了
          </MUButton>}
      </MUView>
      <MUView className="adjust-dialog__infomation">
        <MUView className="adjust-dialog__infomation--tag">服务</MUView>
        <MUView className="adjust-dialog__infomation--text">本弹窗由招联推送</MUView>
      </MUView>
    </MUView>
  </MUView>) : null;
  }
}

LimitPriceModal.propTypes = propTypes;
LimitPriceModal.defaultProps = defaultProps;

export default LimitPriceModal;