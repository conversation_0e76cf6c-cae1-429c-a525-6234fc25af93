import { Component } from '@mu/madp';
import PropTypes from 'prop-types';
import {
  MU<PERSON><PERSON><PERSON>, MUDialog, MUImage, MUIcon
} from '@mu/zui';
import { observer, PropTypes as MobxPropTypes } from '@tarojs/mobx';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import {
  getComposeCase,
  checkApplyInfo,
  queryAccount
} from '../code/api/index';
import LimitModal from "./sub-comp/limit-modal/index";
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}

const defaultProps = {
  reStoreStyle: '',
  beaconIds: 'RestoreCreditComp',
  themeChannel: '',
  isShowReStoreModal: true,
  drawPackRequestList: [],
  irrAnnualRate: 0,
  bestCouponText: '',
  themeColor: ''
};

const safe = 'https://file.mucfc.com/abf/1/0/202403/20240304152137692f6e.png';
const tip = 'https://file.mucfc.com/abf/1/0/202403/2024030415213770932c.png';

const propTypes = {
  reStoreStyle: PropTypes.string,
  beaconIds: PropTypes.string,
  themeChannel: PropTypes.string,
  isShowReStoreModal: PropTypes.bool,
  drawPackRequestList: PropTypes.array,
  irrAnnualRate: PropTypes.number,
  bestCouponText: PropTypes.string,
  themeColor: PropTypes.string,
};

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'RestoreCreditComp', // 就是当前组件类名
}))

class RestoreCreditComp extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowFastReStoreModal: false, // 展示快速恢复弹窗
      isShowNeedReStoreLimitModal: false, // 展示需要额度重审的弹窗
      FastCount: 3,
      SafeCount: 3,
      isChangeFastModalText: false, // 直接展示快速恢复弹窗有对应的文案，展示3+3弹窗时，后面打开的快速恢复弹窗，文案需要更换
    };
  }

  async componentDidMount() {
    PropTypes.checkPropTypes(propTypes, this.props, 'props', 'RestoreCreditComp');
    const {
      themeChannel
    } = this.props;
    if (themeChannel) {
      switch (themeChannel) {
        case '3CUAPP':
          this.setState({
            channelColor: '3CUAPP'
          })
          break;
        case '0JD1ZFBMNP':
          this.setState({
            channelColor: '0JD1ZFBMNP'
          })
          break;
        default:
          this.setState({
            channelColor: 'default'
          })
          break;
      }
    }
    this.initData();
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation: 'shared'
  }

  async initData() {
    const {
      reStoreStyle,
      drawPackRequestList
    } = this.props;
    if (reStoreStyle === 'MA') {//极简恢复+额度领取
      try {
        this.setState({
          isShowFastReStoreModal: true
        })
        const caseParams = drawPackRequestList && drawPackRequestList.length > 0 ? {createCaseType: '1', drawPackRequestList: drawPackRequestList} : {createCaseType: '1'}
        const res = await getComposeCase({...caseParams});
        this.fastCountDown(false);
        // 触发回调事件
        const {
          resultCallback,
          showModalCallback,
          beforeTotalLimit
        } = this.props;

        if (res && res.lmtActivateResult) {//恢复成功
          let getLimitGift = false;
          let limitNum = '';
          let beforeLimitNum = '';
          let tmpLimitExpireDate = '';
          let isTmpGiftStatus = '';
          let availLimit = ''
          if (res && res.adjustInfos && res.adjustInfos.length > 0) {
            // 判断是否有礼包
            res.adjustInfos.forEach((item) => {
              if (item.decision === 'Y' && (item.targetTmpLimit || item.targetLimit)) {
                getLimitGift = true,
                limitNum = item.targetTmpLimit || item.targetLimit,
                beforeLimitNum = item.originTmpLimit || item.originLimit,
                tmpLimitExpireDate = item.targetTmpLimitExpireDate||'',
                isTmpGiftStatus = item.targetTmpLimit ? 'T' : 'F',
                availLimit = Math.floor(item.realLimitValue) 
                dispatchTrackEvent({
                  target: this,
                  beaconId: 'ReceivedLimitSuccess',
                  event: EventTypes.SO,
                  beaconContent: {
                    cus: {
                      cus: {
                        limitNum,
                        availLimit
                      }
                    }
                  }
                })
              }
            });
            // 礼包领取成功并且调后额度需大于调前总额度，则展示领取礼包（需求35224，固额提升后小于可用额度时，不展示额度生效弹窗）
            if (getLimitGift && (limitNum > beforeTotalLimit)) {
              // 重新请求getAccount接口。获取可用额度
              const bubbleText = this.transTip();
              // 存下可用额度（getAccount）
              // 存下调前额度（composecase）
              // 存下现额度（composecase，先取临额，没有取固额）
              // 存下额度失效时间 （用getTime方法转化字段）
              this.setState({
                isShowLimitModal: true,
                isShowFastReStoreModal: false,
                bubbleText: bubbleText,
                beforeLimitNum: beforeLimitNum && Number(beforeLimitNum).toFixed(2),
                limitNum: limitNum && Number(limitNum).toFixed(2),
                tmpLimitExpireDate: tmpLimitExpireDate && this.getTime(tmpLimitExpireDate),
                availLimit: availLimit && Number(availLimit).toFixed(2),
              })
              // 有额度包且领取成功。执行礼包成功结果回调
              if (resultCallback && typeof resultCallback === 'function') {
                resultCallback({ giftResult: 'success' });
              }
              dispatchTrackEvent({
                target: this,
                beaconId: 'LimitModal',
                event: EventTypes.SO,
                beaconContent: {
                  cus: {
                    bubbleText: bubbleText
                  }
                }
              })
              if (showModalCallback && typeof showModalCallback === 'function') {
                // limitData新增，传可用额度，调前调后额度、生效时间和失效时间、以及是永久还是临时
                // 更改modalType，告诉交互运营所需的组件会弹出的哪种弹窗或者哪两种弹窗类型--
                // modalType--01代表极简恢复，02代表重审恢复，03代表极简+礼包弹窗,后续有其他类型的话再加（类似于如果后续加额价弹窗等）
                showModalCallback({
                  modalType: '03',
                  limitData: {
                    availLimit: availLimit && Number(availLimit).toFixed(2),
                    limitBeforeAdjustValue: beforeLimitNum && Number(beforeLimitNum).toFixed(2),
                    limitAfterAdjustValue: limitNum && Number(limitNum).toFixed(2),
                    limitBenefitExpireDate: tmpLimitExpireDate.toString(),
                    limitBenefitEffectDate: new Date().getTime().toString(),
                    limitBenefitType: isTmpGiftStatus,
                  }
                });
              }
            } else {
              // 有额度包但领取不成功。执行礼包失败结果回调
              if (resultCallback && typeof resultCallback === 'function') {
                resultCallback({ giftResult: 'fail' });
              }
              if (showModalCallback && typeof showModalCallback === 'function') {
                showModalCallback({
                  modalType: '01'
                });
              }
            }
          } else {
            // 无礼包结果回调
            if (resultCallback && typeof resultCallback === 'function') {
              resultCallback({ giftResult: 'none' });
            }
            if (showModalCallback && typeof showModalCallback === 'function') {
              showModalCallback({
                modalType: '01'
              });
            }
          }
        }
      } catch (e) {
        this.setState({
          isShowFastReStoreModal: false,
          isShowLimitModal: false
        })
        const {
          showModalCallback,
          resultCallback
        } = this.props;
        // 异常情况，结果回调
        if (resultCallback && typeof resultCallback === 'function') {
          resultCallback();
        }
        // 异常情况，回调告诉用户不弹出弹窗
        // 异常情况，回调给用户是没有展示弹窗的情况
        if (showModalCallback && typeof showModalCallback === 'function') {
          showModalCallback({
            modalType: 'none'
          });
        }
        clearInterval(this.fastCountDownInterval);
      }

      // 打开3s弹窗，开始倒计时
      // 请求composeCase接口--mucfc.apply.apply.composeCase(实时出结果，出结果就关闭弹窗。结束倒计时，触发回调)
      // 倒计时期间1s查一次结果applyinfo结果
      // 倒计时结束或者用户手动关闭弹窗--触发回调事件告诉业务方
    } else if (reStoreStyle === 'RA') {//重审恢复
      try {
        this.setState({
          isShowNeedReStoreLimitModal: true,
          isChangeFastModalText: true,
        })
        await getComposeCase(
          {
            createCaseType: '',
            applyCardBaseInfo: {
              applyCaseLoanInfo: {
                renewAdjustLimitWillingFlag: 'N',
              }
            }
          },
          'd0fc5d54bc88fdc2');
        const {
          showModalCallback,
        } = this.props;
        if (showModalCallback && typeof showModalCallback === 'function') {
          showModalCallback({
            modalType: '02'
          });
        }
        // 3s倒计时完，没出结果，关闭当前弹窗，打开另外的3s弹窗
        this.countDown();
      } catch (e) {
        this.setState({
          isShowNeedReStoreLimitModal: false,
          isChangeFastModalText: false
        });
        const {
          showModalCallback,
          resultCallback
        } = this.props;
        // 异常情况，结果回调
        if (resultCallback && typeof resultCallback === 'function') {
          resultCallback();
        }
        // 异常情况，回调告诉用户不弹出弹窗
        // 异常情况，回调给用户是没有展示弹窗的情况
        if (showModalCallback && typeof showModalCallback === 'function') {
          showModalCallback({
            modalType: 'none'
          });
        }
      }
      // applyStep为3是审批中，5和7是出结果了的
      // const { applyStep } = await getApplyInfo({ queryScene: '0' });

      // 请求composeCase接口
      // 打开3s加载中弹窗
      // 开始倒计时，请求applyinfo事件（scene怎么确定），查看恢复结果
      // 3s倒计时结束后恢复结果还没出现的话，则打开3s弹窗；
      // 3s弹窗倒计时结束或者用户手动关闭弹窗，触发回调事件告诉业务方；
    } else {
      // 异常情况，回调告诉用户不弹出弹窗
      const {
        showModalCallback,
      } = this.props;
      // 异常情况，回调给用户是没有展示弹窗的情况
      if (showModalCallback && typeof showModalCallback === 'function') {
        showModalCallback({
          modalType: 'none'
        });
      }
    }
  }

  getTime = (expireDate) => {
    const time = Number(expireDate) - Date.now();
    const dayTime = Math.floor(time / 86400000);
    if (time <= 0 || dayTime > 60) return '';
    const hourTime = Math.floor(time % 86400000 / 3600000);
    const minuteTime = Math.floor(time % 86400000 % 3600000 / 60000);
    return dayTime > 0 ? `${dayTime}天后失效` : dayTime <= 0 && hourTime > 0 ? `${hourTime}小时后失效` : `${minuteTime}分钟后失效`;
  }

  transTip = () => {
    const {
      irrAnnualRate,
      bestCouponText
    } = this.props;
    if (irrAnnualRate && Number(irrAnnualRate) < 0.07) {
      // 存下气泡文案 年利率（单利）低至xx%
      const rate = this.mul(irrAnnualRate, 100, 4);
      return `年利率（单利）低至${rate}%`;
    } else if (bestCouponText) {
      return bestCouponText;
    } else {
      return '';
    }
  }

  mul = (arg1, arg2, d) => {
    const r1 = arg1.toString();
    const r2 = arg2.toString();
    const m = (r1.split('.')[1] ? r1.split('.')[1].length : 0) + (r2.split('.')[1] ? r2.split('.')[1].length : 0);
    const resultVal = Number(r1.replace('.', '')) * Number(r2.replace('.', '')) / (10 ** m);
    return typeof d !== 'number' ? Number(resultVal) : Number(resultVal.toFixed(parseInt(d, 10)));
  };

  fastCountDown(needCallback) {
    this.fastCountDownInterval = setInterval(() => {
      const { FastCount } = this.state; // 当前计数
      const newCountingNum = FastCount - 1; // 下次计数

      // 更新记数
      this.setState({
        FastCount: newCountingNum
      });

      // 倒计时结束展示刷新按钮，清除计时器
      if (newCountingNum === 0) {
        this.setState({
          isShowFastReStoreModal: false,
          isChangeFastModalText: false
        })
        if (needCallback) {
          const {
            resultCallback,
          } = this.props;
          if (resultCallback && typeof resultCallback === 'function') {
            resultCallback();
          }
        }
        const {
          closeModalCallback,
        } = this.props;
        if (closeModalCallback && typeof closeModalCallback === 'function') {
          closeModalCallback();
        }
        clearInterval(this.fastCountDownInterval);
      }
    }, 1000);
  }

  countDown() {
    this.countDownInterval = setInterval(async () => {
      const { SafeCount } = this.state; // 当前计数
      const newCountingNum = SafeCount - 1; // 下次计数

      // 更新记数
      this.setState({
        SafeCount: newCountingNum
      });

      // 倒计时结束展示刷新按钮，清除计时器
      if (newCountingNum === 0) {
        // let applyStep = '';
        try {
          const { applyStep } = await checkApplyInfo({ queryScene: '0' }, false, false, 'd0fc5d54bc88fdc2');
          if (applyStep === '5' || applyStep === '7') {
            const {
              resultCallback,
            } = this.props;
            if (resultCallback && typeof resultCallback === 'function') {
              resultCallback();
            }
          }
          // 打开3s倒计时弹窗
          this.setState({
            isShowFastReStoreModal: true,
          }, () => {
            setTimeout(() => {
              this.setState({
                isShowNeedReStoreLimitModal: false,
              })
            }, 200);
          })
          // applystep等于5或者等于7，则在上面就已经触发了回调事件，在后面的倒计时事件就不用在触发回调了
          this.fastCountDown(applyStep !== '5' && applyStep !== '7');

        } catch (e) {
          // 打开3s倒计时弹窗
          this.setState({
            isShowFastReStoreModal: true,
          }, () => {
            setTimeout(() => {
              this.setState({
                isShowNeedReStoreLimitModal: false,
              })
            }, 200);
          })
          this.fastCountDown(true);
        }
        clearInterval(this.countDownInterval);
      }
    }, 1000);
  }

  onIconClick = () => {
    const {
      closeModalCallback,
      resultCallback
    } = this.props;
    if (resultCallback && typeof resultCallback === 'function') {
      resultCallback();
    }
    if (closeModalCallback && typeof closeModalCallback === 'function') {
      closeModalCallback();
    }
    this.setState({
      isShowFastReStoreModal: false,
      isChangeFastModalText: false
    });
  }

  render() {
    const {
      isShowFastReStoreModal,
      isShowNeedReStoreLimitModal,
      FastCount,
      SafeCount,
      isChangeFastModalText,
      isShowLimitModal,
      bubbleText,
      beforeLimitNum,
      limitNum,
      tmpLimitExpireDate,
      availLimit
    } = this.state;
    const {
      isShowReStoreModal,
      themeColor,
      limitModalBtnType,
      limitModalCallback
    } = this.props;
    return (
      <MUView className="restore-credit-comp" onTouchMove={(e) => { e.preventDefault(); e.stopPropagation(); }}>
        {isShowReStoreModal && (<MUView>
          <MUDialog
            isOpened={isShowNeedReStoreLimitModal}
            beaconId="needReStoreLimitModal"
          >
            <MUView
              className="fast-restore"
            >
              <MUView className="fast-restore-bg1">
                <MUImage
                  className="pt"
                  src={safe}
                />
              </MUView>
              <MUView className="fast-restore-text">
                为保障您的资金安全，正在进行账户安全检测...
              </MUView>
              <MUView className="fast-restore-time">
                <MUView className="fast-restore-time-num">{SafeCount > 0 ? SafeCount : '0'}</MUView>
                <MUView className="fast-restore-time-text">秒</MUView>
              </MUView>
              <MUView className="fast-restore-tip-block">
                <MUImage
                  className="tip"
                  src={tip}
                />
                <MUView className="text">全程信息加密，保障您的账户安全</MUView>
              </MUView>
            </MUView>
          </MUDialog>
          <MUDialog
            isOpened={isShowFastReStoreModal}
            beaconId="fastReStoreModal"
          >
            <MUView
              className="fast-restore"
            >
              <MUIcon
                beaconId="closeIcon"
                onClick={this.onIconClick}
                value="close2"
                className="icon"
                color="#a6a6a6"
                size="18"
              />
              <MUView className="fast-restore-bg2">
                <MUImage
                  className="pt"
                  src={safe}
                />
              </MUView>
              <MUView className="fast-restore-text">
                {isChangeFastModalText ? '账户安全已检测完成，正在加载中，请稍后...' : '为保障您的资金安全，正在进行账户安全检测...'}
              </MUView>
              <MUView className="fast-restore-time">
                <MUView className="fast-restore-time-num">{FastCount > 0 ? FastCount : '0'}</MUView>
                <MUView className="fast-restore-time-text">秒</MUView>
              </MUView>
              <MUView className="fast-restore-tip-block">
                <MUImage
                  className="tip"
                  src={tip}
                />
                <MUView className="text">全程信息加密，保障您的账户安全</MUView>
              </MUView>
            </MUView>
          </MUDialog>
        </MUView>)}
        {isShowLimitModal && <LimitModal
          data={{
            bubbleText: bubbleText,
            beforeLimitNum: beforeLimitNum,
            limitNum: limitNum,
            tmpLimitExpireDate: tmpLimitExpireDate,
            availLimit: availLimit,
            themeColor: themeColor
          }}
          limitModalBtnType={limitModalBtnType}
          limitModalCallback={limitModalCallback}
        />}
      </MUView>
    );
  }
  // }
}

RestoreCreditComp.propTypes = propTypes;
RestoreCreditComp.defaultProps = defaultProps;
export default observer(RestoreCreditComp);
