# 圣约回家弹窗

@mu/sy-dialog



外部渠道点击认证项或领取项，不满足认证逻辑或者领取逻辑，弹出弹窗拦截

## 预览图

预览截图，需手动替换为实际的预览图，图片名不可重命名

![screenshot.png](http://unpkg.mucfc.com/@mu/sy-dialog/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/sy-dialog`

### 样式引入

`@import "~@mu/sy-dialog/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/sy-dialog',
            '@mu\\sy-dialog'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\sy-dialog',
            '@mu/sy-dialog',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/sy-dialog':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/sy-dialog')
    },
}
```

在这里写业务组件详细接入文档
