@import "./sub-comp-demo.scss";

.default-dialog {
    .mu-dialog__container {
        width: 560px;
        overflow: visible !important;
    }

    .mu-dialog__content {
        padding: 0;
    }

    .contain {
        &__img {
            padding: 12px 12px 0;
            width: 536px;
            height: 220px;
        }

        &__content {
            padding: 0 40px;

            &__title {
                margin: 40px 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 36px;
                color: #333;
                font-weight: 600;
            }

            &__title_level_three {
                margin: 40px 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 36px;
                color: #333;
                font-weight: 600;
            }

            .main_title {
                text-align: center;
                margin-bottom: 10px;
            }

            .sub_title {
                font-size: 28px;
                color: #808080;
                white-space: normal;
            }

            &__desc {
                max-height: 380px;
                overflow: hidden;
                font-size: 28px;
                line-height: 38px;
                color: #808080;
            }

            &__button {
                width: 480px;
                height: 88px;
                border-radius: 8px;
                margin: 40px 0;
                font-size: 36px;
                font-weight: 600;
                color: #FFFFFF;
                line-height: 88px;
                background-color: #3477FF;
            }

            &__button-redpink {
                width: 480px;
                height: 88px;
                border-radius: 8px;
                margin: 40px 0;
                font-size: 36px;
                font-weight: 600;
                color: #FFFFFF;
                line-height: 88px;
                background-color: #E60027;
            }
        }
    }

    .modal__compliance {
        width: calc(100% - 40px);
        padding: 0 20px;
        position: fixed;
        display: flex;
        align-items: center;
        bottom: -48px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 20px;
        color: #ffffff;
        text-align: center;
        line-height: 32px;
        z-index: 2001;

        .compliance__identification {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 68px;
            min-width: 68px;
            height: 32px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            font-size: 22px;
        }

        >mu-view:nth-child(2) {
            flex: 1;
        }

        .compliance__information {
            flex: 1;
            text-align: center;
            margin-right: 68px;
            // 保证当文字接近22字时一行显示完，优先级大于居中。因为22字时即使margin-right为0也无法显示下
            white-space: nowrap;
        }
    }
}

.level-two-dialog {
    .mu-dialog__container {
        width: 600px;
        border-radius: 16px;
        position: relative;
        overflow: visible !important;
    }

    .mu-dialog__content {
        padding: 0;
    }

    .level-two-dialog-headImg {
        width: 600px;
        height: 200px;
    }

    .level-two-dialog-closeImg {
        width: 28px;
        height: 28px;
        top: 5%;
        right: 5%;
        display: flex;
        position: absolute;
    }

    .level-two-dialog-headBlock {
        position: absolute;
        top: 8%;
        left: 7%;

        .title {
            font-size: 48px;
            font-weight: 600;
            line-height: 48px;
            letter-spacing: 0px;
            color: #3477FF;
            text-align: left;
            margin-bottom: 30px;
        }

        .subtitle {
            font-size: 36px;
            font-weight: 600;
            line-height: 36px;
            letter-spacing: 0px;
            color: #333333;
        }
    }

    .level-two-dialog-main-block {
        display: flex;
        margin: 0 50px;

        .level-two-dialog-step-block {
            // flex: 1;
            font-size: 24px;
            color: #3477FF;
            padding: 25px 0;
            width: 15px;
        }

        .level-two-dialog-step-line {
            background: #3477FF;
            opacity: 0.5;
            width: 3px;
            height: 140px;
            margin-left: 6px;
        }

        .level-two-dialog-text-block {
            flex: 1;
            font-size: 26px;
            line-height: 36px;
            letter-spacing: 0px;
            margin: 15px 0 15px 40px;
            text-align: left;

            &-first {
                margin-bottom: 100px;
            }
        }

        .level-two-dialog-img-block {
            flex: 1;
        }
    }

    .level-two-dialog-firstImg {
        width: 240px;
        height: 150px;
        border-radius: 20px 20px 0px 0px;
        display: flex;
        margin: -20px 0 20px 0;
    }

    .level-two-dialog-secondImg {
        width: 240px;
        height: 150px;
        border-radius: 20px 20px 0px 0px;
        display: flex;
    }

    .level-two-dialog-tipImg {
        width: 323px;
        height: 86.76%;
        border-radius: 21px;
    }

    .level-two-dialog-button {
        left: 60px;
        top: 629px;
        width: 480px;
        height: 100px;
        border-radius: 50px;
        opacity: 1;
        background: #3477FF;
        font-size: 36px;
        font-weight: 600;
        line-height: 100px;
        text-align: center;
        letter-spacing: 0px;
        color: #FFFFFF;
        margin: 0 auto;
    }

    .level-two-dialog-emtry {
        height: 40px;
    }

}

.modal__compliance {
    width: calc(100% - 40px);
    padding: 0 20px;
    position: fixed;
    display: flex;
    align-items: center;
    bottom: -48px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    text-align: center;
    line-height: 32px;
    z-index: 2001;

    .compliance__identification {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68px;
        min-width: 68px;
        height: 32px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        font-size: 22px;
    }

    >mu-view:nth-child(2) {
        flex: 1;
    }

    .compliance__information {
        flex: 1;
        text-align: center;
        margin-right: 68px;
        // 保证当文字接近22字时一行显示完，优先级大于居中。因为22字时即使margin-right为0也无法显示下
        white-space: nowrap;
    }
}

.level-three-dialog-tipImg {
    width: 222px;
    height: 38px;
    margin-bottom: -25px;
}

.level-one-dialog {
    .mu-dialog__container {
        width: 600px;
        border-radius: 16px;
        position: relative;
        overflow: visible !important;
    }

    .mu-dialog__content {
        padding: 0;
    }

    .level-one-dialog-tipImg {
        width: 222px;
        height: 38px;
        margin-top: 40px;
    }

    .level-one-dialog-closeImg {
        width: 28px;
        height: 28px;
        top: 5%;
        right: 5%;
        display: flex;
        position: absolute;
    }

    .title {
        font-family: PingFang SC;
        font-size: 36px;
        font-weight: 600;
        line-height: 54px;
        text-align: center;
        letter-spacing: 0px;
        margin-bottom: 20px;
        color: #333333;
    }

    .level-one-dialog-main-block {
        display: flex;
        margin: 0 30px 0 40px;

        .level-one-dialog-step-block {
            // flex: 1;
            font-size: 24px;
            color: #3477FF;
            padding: 25px 0;
            width: 15px;
            margin-left: 10px;
            margin-top: 25px;
        }

        .level-one-dialog-step-line {
            background: #3477FF;
            opacity: 0.5;
            width: 3px;
            height: 140px;
            margin-left: 6px;
        }

        .level-one-dialog-text-block {
            flex: 1;
            font-size: 26px;
            line-height: 36px;
            letter-spacing: 0px;
            margin: 36px 0 15px 40px;
            text-align: left;
            padding-right: 60px;

            &-first {
                margin-bottom: 70px;
            }

            &-second {
                margin-bottom: 108px;
            }
        }

        .level-one-dialog-img-block {
            flex: 1;
        }
    }

    .level-one-dialog-step-block {
        // flex: 1;
        font-size: 24px;
        color: #3477FF;
        padding: 25px 0;
        width: 15px;
    }

    .level-one-dialog-firstImg {
        width: 240px;
        height: 150px;
        border-radius: 20px 20px 0px 0px;
        display: flex;
        margin-bottom: 35px;
    }

    .level-one-dialog-secondImg {
        width: 240px;
        height: 150px;
        border-radius: 20px 20px 0px 0px;
        display: flex;
        margin-bottom: 35px;
    }

    .level-one-dialog-button {
        width: 500px;
        height: 88px;
        border-radius: 8px;
        background: #3477FF;
        font-size: 36px;
        font-weight: 600;
        line-height: 100px;
        text-align: center;
        letter-spacing: 0px;
        color: #FFFFFF;
        margin: -6px auto;
    }

    .level-one-dialog-emtry {
        height: 30px;
    }
}