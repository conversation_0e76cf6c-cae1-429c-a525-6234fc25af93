import Madp, { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUIcon} from '@mu/zui';
import { inject, observer } from '@tarojs/mobx';
import { SubComp1Props, SubComp1State } from '../types/sub-comp-demo';

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'RangeDateSelectDrawer', // 就是当前组件类名
}))
@inject('componentStore')
@observer
export default class SubCompDemo extends Component<SubComp1Props, SubComp1State> {
  public static defaultProps: SubComp1Props;

  public static propTypes: InferProps<SubComp1Props>;

  public constructor(props: SubComp1Props) {
    super(props);
  }

  public render(): JSX.Element {
    const {
      trackedBeaconId,
      componentStore: {
        sub1,
        localSubComputedThing,
      },
    } = this.props || {};
    return (
      <MUView>
        <MUIcon value='tip'></MUIcon>
        <MUView>demo 子组件显示：{ sub1 }</MUView>
        <MUView>{localSubComputedThing}</MUView>
        <MUView beaconId="MyButton1" parentId={trackedBeaconId}> 一个demo按钮 </MUView>
      </MUView>
    );
  }
}
