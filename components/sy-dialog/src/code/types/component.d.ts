import { MouseEvent, ComponentClass } from 'react';
import { CommonEventFunction } from '@tarojs/components/types/common';

import MUComponent from './base';

export interface MUSyDialogProps extends MUComponent {
  /**
   * 是否开启
   * @default false
   */
  isOpened?: boolean;
  /**
   * 点击按钮触发事件
   */
  onClick?: CommonEventFunction;

  /**
   * lui展位数据
   * */
  luiData?: object;
}

export interface MUSyDialogState {
  isWEB?: boolean;
  isWEAPP?: boolean;
  isALIPAY?: boolean;
  isShow?: boolean;
}

declare const MUSyDialogComponent: ComponentClass<MUSyDialogProps>;

export default MUSyDialogComponent;
