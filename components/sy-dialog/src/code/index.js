import { Component } from '@tarojs/taro';
import PropTypes from 'prop-types';
import {
  MUV<PERSON>w,
  MUImage,
  MUDialog,
  MUModal
} from '@mu/zui';
import { getDrainageLevel } from '@mu/business-basic';
import { isIOS, isMuapp } from '@mu/madp-utils';
import Madp from '@mu/madp';

if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}

const close = 'https://file.mucfc.com/abf/1/0/202207/202207111607298b1569.png';
const middleHeadBlock = 'https://file.mucfc.com/abf/1/0/202312/202312102023376b780d.png';
const middleFirstImg = 'https://file.mucfc.com/abf/1/0/202312/2023121215495656d7e3.png';
const middleSecondImg = 'https://file.mucfc.com/abf/1/0/202312/202312102023378b2520.png';
const middleTipImg = 'https://file.mucfc.com/abf/1/0/202312/202312112116426101a6.png';
const lowHeadImg = 'https://file.mucfc.com/abf/1/25/202503/2025032119325143c577.png';
const lowTip = 'https://file.mucfc.com/abf/1/0/202312/20231210202337dc35ec.png';
const strongFirstImg = 'https://file.mucfc.com/abf/1/0/202312/20231210202337b0b352.png';
const strongSecondImg = 'https://file.mucfc.com/abf/1/25/202503/202503211932199c4300.png';
const strongThirdImg = 'https://file.mucfc.com/abf/1/0/202312/2023121020233763f802.png';
const goHomePopupUrl = 'https://file.mucfc.com/abf/1/25/202503/2025032119325143c577.png';
const unfreezeImgs = 'https://file.mucfc.com/abf/21/0/202411/20241112141443187b69.png'

const propTypes = {
  onConfirmClick: PropTypes.func, // 点击弹窗按钮回调
  levelResultCallback: PropTypes.func, // 圣约等级是否是属于需要阻止流程的回调
  openModal: PropTypes.bool, // 弹窗开关
  type: PropTypes.number, // getDrainageLevel方法类型，类似于3代表去查提额类型的圣约情况
  jumpUrl: PropTypes.string, // 跳转链接
  needDefaultModal: PropTypes.bool, // 是否需要默认的兜底弹窗
};

const defaultProps = {
  openModal: false,
  onConfirmClick: () => { },
  levelResultCallback: () => { },
  type: 0,
  jumpUrl: '',
  needDefaultModal: true,
};

class SyDialog extends Component {
  static options = {
    addGlobalClass: true
  }

  config = {
    styleIsolation: 'shared'
  }

  async componentDidMount() {
    PropTypes.checkPropTypes(propTypes, this.props, 'prop', 'SyDialog');
    const {
      type
    } = this.props;
    await this.drainagelevel(type);
  }

  componentDidUpdate(prevProps) {
    // 比较是否有差异，有差异才执行更新
    if (JSON.stringify(this.props) === JSON.stringify(prevProps)) {
      return;
    }
  }

  onCopyUrl() { // 复制路径
    const { jumpUrl } = this.props;
    // 自有 app 使用原生提供的复制方法
    const copyUrl = jumpUrl;
    if (isMuapp()) {
      Madp.htmlCopy(copyUrl).then(() => {
      }).catch(() => {
        alert('复制失败');
      });
    } else if (process.env.TARO_ENV !== 'h5') {
      // 微信小程序
      Madp.setClipboardData({
        data: copyUrl,
        success() {
        },
        fail() {
          alert('复制失败');
        }
      });
    } else {
      // 非自有 app 渠道使用封装的复制文字方法（可能会有兼容性问题，暂未发现）
      const copySucceed = this.copyText(copyUrl);
      if (!copySucceed) {
        alert('复制失败');
      }
    }
  }

  drainagelevel = async (type) => {
    const {
      levelResultCallback
    } = this.props;
    let level = '';
    if (getDrainageLevel && typeof getDrainageLevel === 'function') {
      const res = await getDrainageLevel(type) || 'CL';
      level = res;
    }
    if (levelResultCallback && typeof levelResultCallback === 'function') {
      // 圣约等级不等于CL或者等于CL但是出现异常情况，类似于接口报错、渠道漏配以及自有渠道号（0、2app）在浏览器打开，是需要阻止流程继续的
      levelResultCallback({
        needStopProcess: (level !== 'CL' || (level === 'CL' && !isMuapp()))
      });
    }
    this.setState({
      level
    });
  }

  /**
  * 复制文字
  */
  copyText = (text) => {
    const textAreaElement = document.createElement('textArea');
    textAreaElement.value = text;
    document.body.appendChild(textAreaElement);
    textAreaElement.setAttribute('readonly', 'readonly');
    if (!document.execCommand) {
      return false;
    }
    if (isIOS()) {
      const range = document.createRange();
      range.selectNodeContents(textAreaElement);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
      textAreaElement.setSelectionRange(0, text.length);
    } else {
      textAreaElement.select();
    }
    const copySucceed = document.execCommand('copy');
    textAreaElement.blur();
    document.activeElement.blur();
    document.body.removeChild(textAreaElement);
    return copySucceed;
  };

  buttonClick = () => {
    const { onConfirmClick } = this.props;
    const { level } = this.state;
    // 圣约等级为L1时，点击按钮需要跳转
    if (onConfirmClick && typeof onConfirmClick === 'function') {
      if (level === 'L2') {
        this.onCopyUrl();
      }
      onConfirmClick({
        type: 'closeModal'
      });
    }
  };

  jumpButtonClick = () => {
    const { jumpUrl, onConfirmClick } = this.props;
    // 圣约等级为L1时，点击按钮需要跳转
    if (onConfirmClick && typeof onConfirmClick === 'function') {
      onConfirmClick({
        type: 'clickJumpButton'
      });
      Madp.navigateTo({
        url: jumpUrl,
      });
    }
  };

  render() {
    const {
      openModal,
      needDefaultModal,
      type
    } = this.props;
    const {
      level
    } = this.state;
    const cardView = (
      openModal && (
        <MUView>
          {level === 'L1' && (
            <MUDialog
              className="level-one-dialog"
              isOpened
              beaconId="LevelOneDialog"
            >
              {type === 2 ? <MUImage className="level-one-dialog-tipImg" src={lowTip} alt="" />
                : <MUView className="level-one-dialog-tipImg"></MUView>}
              <MUImage
                className="level-one-dialog-closeImg"
                src={close}
                onClick={this.buttonClick}
                beaconId="closeLevelOneDialog"
                alt=""
              />
              <MUView className="level-one-dialog-headBlock">
                <MUView className="title">请前往招联官方应用使用</MUView>
              </MUView>
              <MUView className="level-one-dialog-main-block">
                {/* 步骤条 */}
                <MUView className="level-one-dialog-step-block">
                  <MUView>1</MUView>
                  <MUView className="level-one-dialog-step-line" />
                  <MUView>2</MUView>
                  <MUView className="level-one-dialog-step-line" />
                  <MUView>3</MUView>
                </MUView>

                <MUView className="level-one-dialog-text-block">
                  <MUView className="level-one-dialog-text-block-first">
                    <MUView>
                      点击下方按钮立即前往‘
                      <MUView style="color:#3477FF;display:inline">招联金融</MUView>
                      ’
                    </MUView>
                  </MUView>
                  <MUView className="level-one-dialog-text-block-second">
                    <MUView>进入页面后下载APP</MUView>
                  </MUView>
                  <MUView className="level-one-dialog-text-block-third">
                    <MUView>
                      登录完成后进入首页
                      {type === 2 ? (<MUView style="display:inline">‘<MUView style="color:#3477FF;display:inline">借钱</MUView>’ 或‘<MUView style="color:#3477FF;display:inline">额度恢复</MUView>’</MUView>)
                        : (<MUView style="display:inline">‘<MUView style="color:#3477FF;display:inline">提额</MUView>’</MUView>)}
                    </MUView>
                  </MUView>
                </MUView>
                <MUView className="level-one-dialog-img-block">
                  <MUImage
                    className="level-one-dialog-firstImg"
                    src={strongFirstImg}
                    alt=""
                  />
                  <MUImage
                    className="level-one-dialog-secondImg"
                    src={strongSecondImg}
                    alt=""
                  />
                  {type === 2 ? <MUImage
                    className="level-one-dialog-secondImg"
                    src={unfreezeImgs}
                    alt=""
                  />
                    : <MUImage
                      className="level-one-dialog-secondImg"
                      src={strongThirdImg}
                      alt=""
                    />}
                </MUView>
              </MUView>
              <MUView
                className="level-one-dialog-button"
                onClick={this.jumpButtonClick}
                beaconId="clickLevelOneDialog"
              >
                立即前往
              </MUView>
              <MUView className="level-one-dialog-emtry" />
            </MUDialog>
          )}
          {level === 'L2' && (
            <MUDialog
              className="level-two-dialog"
              isOpened
              beaconId="LevelTwoDialog"
            >
              {middleHeadBlock && (
                <MUImage
                  className="level-two-dialog-headImg"
                  src={middleHeadBlock}
                  alt=""
                />
              )}
              <MUImage
                className="level-two-dialog-closeImg"
                src={close}
                onClick={this.buttonClick}
                beaconId="closeLevelTwoDialog"
                alt=""
              />
              <MUView className="level-two-dialog-headBlock">
                <MUView className="title">只需2步</MUView>
                {type === 2 ? <MUView className="subtitle">即可申请额度评估</MUView>
                  : <MUView className="subtitle">即可享受最高提额至30万</MUView>}
              </MUView>
              <MUView className="level-two-dialog-main-block">
                {/* 步骤条 */}
                <MUView className="level-two-dialog-step-block">
                  <MUView>1</MUView>
                  <MUView className="level-two-dialog-step-line" />
                  <MUView>2</MUView>
                </MUView>

                <MUView className="level-two-dialog-text-block">
                  <MUView className="level-two-dialog-text-block-first">
                    <MUView>打开浏览器</MUView>
                    <MUView>粘贴链接</MUView>
                  </MUView>
                  <MUView className="level-two-dialog-text-block-second">
                    <MUView>下载登录</MUView>
                    <MUView>招联金融APP</MUView>
                  </MUView>
                </MUView>
                <MUView>
                  <MUImage
                    className="level-two-dialog-firstImg"
                    src={middleFirstImg}
                    alt=""
                  />
                  <MUImage
                    className="level-two-dialog-secondImg"
                    src={middleSecondImg}
                    alt=""
                  />
                </MUView>
              </MUView>
              <MUImage
                className="level-two-dialog-tipImg"
                src={middleTipImg}
                alt=""
              />
              <MUView
                className="level-two-dialog-button"
                onClick={this.buttonClick}
                beaconId="clickLevelTwoDialog"
              >
                去浏览器粘贴链接
              </MUView>
              <MUView className="level-two-dialog-emtry" />
              <MUView className="modal__compliance">
                <MUView className="compliance__identification">服务</MUView>
                <MUView className="compliance__information">本弹窗由招联金融推送</MUView>
              </MUView>
            </MUDialog>
          )}
          {level === 'L3' && (
            <MUDialog
              className="default-dialog"
              isOpened
              beaconId="LevelThreeDialog"
            >
              {lowHeadImg && (
                <MUImage
                  className="contain__img"
                  src={lowHeadImg}
                  alt=""
                />
              )}
              {type !== 2 && <MUImage
                className="level-three-dialog-tipImg"
                src={lowTip}
                alt=""
              />}
              <MUView className="contain__content">
                <MUView className="contain__content__title_level_three">
                  <MUView className="main_title">请前往招联官方应用使用</MUView>
                  <MUView className="sub_title">
                    下载登录招联金融官方应用，点击首页
                    {type === 2 ? (<MUView style="display:inline">‘<MUView style="color:#3477FF;display:inline">借钱</MUView>’ 或‘<MUView style="color:#3477FF;display:inline">额度恢复</MUView>’</MUView>)
                      : (<MUView style="display:inline">‘<MUView style="color:#3477FF;display:inline">提额</MUView>’</MUView>)}
                    按钮后即可使用
                  </MUView>
                </MUView>
                <MUView
                  className="contain__content__button"
                  beaconId="clickLevelThreeDialog"
                  onClick={this.buttonClick}
                >
                  我知道了
                </MUView>
              </MUView>
              <MUView className="modal__compliance">
                <MUView className="compliance__identification">服务</MUView>
                <MUView className="compliance__information">本弹窗由招联金融推送</MUView>
              </MUView>
            </MUDialog>
          )}
          {level === 'L4' && (
            <MUModal
              isOpened
              beaconId="LevelFourDialog"
              closeOnClickOverlay={false}
              content="功能暂未开放，请过段时间再试"
              confirmText="知道了"
              onConfirm={this.buttonClick}
            />
          )}
          {(level === 'CL' && !isMuapp() && needDefaultModal) && (
            <MUDialog
              className="default-dialog"
              isOpened={openModal}
              beaconId="DefaultDialog"
            >
              {goHomePopupUrl && (
                <MUImage
                  className="contain__img"
                  src={goHomePopupUrl}
                  alt=""
                />
              )}
              <MUView className="contain__content">
                <MUView className="contain__content__title">
                  <MUView className="main_title">请去招联金融官方应用使用</MUView>
                  <MUView className="sub_title">此功能仅支持在招联金融官方应用使用，请下载登录官方应用，点击首页
                    {type === 2 ? <MUView style="display:inline">“借钱”或“额度恢复”</MUView> : <MUView style="display:inline">“慧提额”</MUView>}
                    按钮后使用</MUView>
                </MUView>
                <MUView
                  className="contain__content__button"
                  beaconId="clickDefaultDialog"
                  onClick={this.buttonClick}
                >
                  我知道了
                </MUView>
              </MUView>
              <MUView className="modal__compliance">
                <MUView className="compliance__identification">服务</MUView>
                <MUView className="compliance__information">本弹窗由招联金融推送</MUView>
              </MUView>
            </MUDialog>
          )}
        </MUView>
      )
    );

    return cardView;
  }
}

SyDialog.propTypes = propTypes;
SyDialog.defaultProps = defaultProps;
export default SyDialog;
