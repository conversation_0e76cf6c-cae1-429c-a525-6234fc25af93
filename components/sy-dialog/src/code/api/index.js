import { fetch, apiHost } from '@mu/business-basic';

export const getUserInfo = async () => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.userInformation.getUserInfo`);
    return res || {};
  } catch (e) {
    return {};
  }
};

export const unBind = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.login.logout`, {
      data
    });
    return res;
  } catch (e) {
    return null;
  }
};
