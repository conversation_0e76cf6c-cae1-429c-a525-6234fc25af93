/* write style here */
.silence-unfreeze-comp {
    .unfreeze-restore {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        // width: 690px;

        &-bg1 {
            width: 162px;
            height: 143px;
            margin-top: 20px;
            margin-bottom: 50px;
        }

        &-bg2 {
            width: 162px;
            height: 143px;
            margin-top: 20px;
            margin-bottom: 50px;
        }

        .icon {
            position: absolute;
            right: -10px;
            top: -10px;
        }

        &-time {
            text-align: center;
            margin: 50px 0 100px 0;
            display: flex;

            &-num {
                color: #3477ff;
                text-align: center;
                font-size: 28px;
                font-weight: 600;
                font-family: "PingFang SC";
                line-height: 32px;
            }

            &-text {
                color: #808080;
                text-align: center;
                font-size: 28px;
                font-weight: 400;
                font-family: "PingFang SC";
                line-height: 32px;
            }
        }

        &-tip-block {
            display: flex;

            .tip {
                width: 28px;
                height: 32px;
                margin: -5px 10px 0 0;
            }

            .text {
                opacity: 1;
                color: #a6a6a6;
                text-align: left;
                font-size: 24px;
                font-weight: 400;
                font-family: "PingFang SC";
                line-height: 24px;
            }

        }

        &-text {
            opacity: 1;
            color: #333333;
            text-align: center;
            font-size: 28px;
            font-weight: 400;
            font-family: 'PingFang SC';
        }
    }

    .pt {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .mu-dialog__overlay {
        backdrop-filter: blur(15px);
    }

    .guide-modal {
        .mu-dialog__container {
            position: relative;
            border-radius: 8px;
            width: 560px;
            height: 626px;
            background: linear-gradient(180deg, #E1EAFF 1%, #FFFFFF 26%);
            // justify-content: center;
            // align-items: center;
        }

        .mu-dialog__content {
            padding: unset !important;
        }

        &-bg {
            padding: 40px 30px;
        }

        &-icon {
            position: absolute;
            top: 4%;
            right: 4%;
        }

        &-title {
            font-family: PingFang SC;
            font-size: 36px;
            font-weight: 600;
            line-height: 60px;
            letter-spacing: 0px;
            text-align: left;
        }

        &-text-block {
            background: url('https://file.mucfc.com/abf/1/0/202406/20240607150344e4d5f4.png') no-repeat left top / contain;
            margin-top: 28px;
            width: 500px;
            height: 248px;
            position: relative;

            &-word {
                padding: 50px 0 50px 24px;
                height: 150px;
            }

            &-title {
                font-family: PingFang SC;
                font-size: 36px;
                font-weight: 600;
                line-height: 50px;
                letter-spacing: 0px;
                text-align: left;
                // padding-top: 20px;
            }

            &-price {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 54px;
                padding-top: 6px;
                padding-bottom: 13px;
            }
            .less {
                padding-top: 20px;
            }

            &-subtitle {
                font-family: PingFang SC;
                font-size: 26px;
                font-weight: normal;
                line-height: 48px;
                letter-spacing: 0px;
                color: #808080;
                text-align: left;
                padding-top: 5px;

            }
            &-desc {
                font-size: 24px;
            }

            &-picture {
                display: flex;
                width: 160px;
                height: 160px;
                position: absolute;
                top: 21%;
                right: 0%;
                &-red{
                    width: 160px;
                    height: 130px;
                    display: flex;
                    position: absolute;
                    top: 28%;
                    right: 0%;
                }
            }
        }

        &-red {
            background: url('https://file.mucfc.com/abf/14/15/202411/20241106105324cb3157.png') no-repeat left top / contain;
            margin-top: 14px;
            width: 500px;
            height: 100%;
            position: relative;
        }

        &-blue {
            background: url('https://file.mucfc.com/abf/14/15/202411/20241106105324871e0c.png') no-repeat left top / contain;
            margin-top: 14px;
            width: 500px;
            height: 100%;
            position: relative;
        }

        &-button {
            border-radius: 50px;
            height: 100px;
            width: 500px;
            background: #3477FF;
            font-family: PingFang SC;
            font-size: 36px;
            font-weight: 600;
            line-height: 100px;
            text-align: center;
            letter-spacing: 0px;
            color: #FFFFFF;
            margin-top: 40px;
        }

        &-sub-button {
            height: 100px;
            width: 200px;
            font-family: PingFang SC;
            font-size: 32px;
            font-weight: normal;
            line-height: 100px;
            text-align: center;
            letter-spacing: 0px;
            color: #808080;
            display: inline-block;
        }
    }

}
