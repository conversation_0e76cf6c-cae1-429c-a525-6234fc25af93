import {
    dispatchTrackEvent
  } from '@mu/madp-track';
  import {
    getEnv,
    getCurrentPageUrl,
  } from '@mu/madp-utils';
/**
 * 业务组件需要用到的公共方法封装
 * */

/**
 * 手动埋点方法，在基础方法上拓展，封装添加模块Id、PageId
 * @param {string} pageId 页面Id
 * @param {object} beaconObj 埋点数据对象，跟dispatchTrackEvent定义的所需参数一样
 */
 export const sendTrackBeacon = (pageId, beaconObj) => {
    let moduleId = process.env.TRACK_MODULE_ID; // 默认工程moduleId
    // 兼容小程序moduleId获取（路由上带）
    const currentRoute = getCurrentPageUrl();
    if (
      (process.env.TARO_ENV !== 'h5') && typeof currentRoute === 'string'
    ) {
      const matchRes = currentRoute.match(/^\/?(.+)\/pages\/.+$/);
      if (matchRes) {
        // eslint-disable-next-line prefer-destructuring
        moduleId = matchRes[1];
      }
    }
    let beaconId = moduleId;
    // 传参只有pageId
    if (pageId && !beaconObj.beaconId) {
      beaconId = `${beaconId}.${pageId}`;
    }
    // 传参已拼pageId
    if (!pageId && beaconObj.beaconId) {
      beaconId = `${beaconId}.${beaconObj.beaconId}`;
    }
    // 传参分开pageId和beaconId
    if (pageId && beaconObj.beaconId) {
      beaconId = `${beaconId}.${pageId}.${beaconObj.beaconId}`;
    }
  
    dispatchTrackEvent({
      ...beaconObj,
      beaconId
    });
  };

  /**
 * 前端域名
 */
// 当前环境
const env = process.env.BUILD_ENV || getEnv();

let domain = 'https://m-zl.mucfc.com';
if (env !== 'prod') {
  domain = `https://m-zl-${env}.cfcmu.cn`;
}

export const urlDomain = domain;
/**
 * @param {promises} funcs 需要捕获错误的 promise
 */
export const catchPromiseErr = funcs => {
  return funcs.map(func => func.catch(err => Promise.resolve({ interfaceFail: true })));
};

export default {
  sendTrackBeacon,
  urlDomain,
  catchPromiseErr
}