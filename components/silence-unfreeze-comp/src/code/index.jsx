import Madp, { Component } from '@mu/madp';
import PropTypes from 'prop-types';
import {
  MUView, MUDialog, MUImage, MUIcon, MURichText
} from '@mu/zui';
import { isMuapp } from '@mu/madp-utils';
import { observer, PropTypes as MobxPropTypes } from '@tarojs/mobx';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import { getLocalConstant } from '@mu/business-basic';
import {
  queryAdmission,
  queryAccount,
  createCase,
  queryCaseInfo,
  queryCreditStatus
} from '../code/api/index';
import {  urlDomain, catchPromiseErr } from '../code/utils/index';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}

const defaultProps = {
  beaconId: 'SlienceUnfreezeComp',
  jumpBackUrl: '',
  needOpenWebview: false,
  controlCode: '',
  limitAmount: '',
  hasRestoreType: false,
  isShowCountDownModal: true,
  themeColor: '',
};

const safe = 'https://file.mucfc.com/abf/1/0/202403/20240304152137692f6e.png';
const tip = 'https://file.mucfc.com/abf/1/0/202403/2024030415213770932c.png';
const guideAndHasAuth = 'https://file.mucfc.com/abf/1/0/202406/20240611144205296408.png';
const guideAndHasAuthRed = 'https://file.mucfc.com/abf/1/25/202411/20241118160246ab9fbf.png';
const guideAndNotAuth = 'https://file.mucfc.com/abf/1/0/202406/2024061114420518b96b.png';
const guideAndNotAuthRed = 'https://file.mucfc.com/abf/1/25/202411/202411181602466c9e9b.png';

const propTypes = {
  beaconId: PropTypes.string,
  jumpBackUrl: PropTypes.string,
  needOpenWebview: PropTypes.bool,
  controlCode: PropTypes.string,
  limitAmount: PropTypes.string,
  hasRestoreType: PropTypes.bool,
  isShowCountDownModal: PropTypes.bool,
  themeColor: PropTypes.string,
};

@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'SlienceUnfreezeComp', // 就是当前组件类名
}))

class SlienceUnfreezeComp extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isShowGuideModal: false, // 展示静默解冻弹窗
      isShowUnfreezeModal: false, // 展示引导资料解冻的弹窗
      activateLimit: '',
      availLimit: '',
      isShowGuideLoanModal: false // 展示借款引导弹窗
    };
    this.UnfreezeCount = 5;
  }

  async componentDidMount() {
    PropTypes.checkPropTypes(propTypes, this.props, 'props', 'SlienceUnfreezeComp');
    dispatchTrackEvent({
      target: this,
      beaconId: 'entrySlienceUnfreeze',
      event: EventTypes.EV,
    })
    await this.initData();
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation: 'shared'
  }

  async initData() {
    try {
      this.setState({
        isShowUnfreezeModal: true
      })
      dispatchTrackEvent({
        target: this,
        beaconId: 'slienceUnfreezeInitComposeCase',
        event: EventTypes.EV,
      })
      const { caseNo } = await createCase({
        createCaseScene: '2'//简易申请（无资料，直接终审）
      }, 'b6ada291df3b2d5a');
      this.countDown(caseNo);//案件号通过入参方式传递，防止setState异步取不到caseNo，接口报错进入死循环
    } catch (e) {
      clearInterval(this.countDownInterval);
      const {
        resultCallback,
      } = this.props;
      if (resultCallback && typeof resultCallback === 'function') {
        resultCallback({
          type: 'interface-fail'
        });
      }
      this.setState({
        isShowGuideModal: false,
        isShowUnfreezeModal: false,
      })
    }
  }


  /**
   * 适配账户数据
   * @param {*} resData
   * @returns
   */
  adpterAccount(resData) {
    let data = {}; // 适配后的数据
    const { accountList } = resData || {};
    const list
      = accountList
      && accountList.filter(item => item.acctBusiType === '01');
    const { limitInfoList } = list[0] || {};
    const D01 = limitInfoList && limitInfoList.find(item => item.limitType === 'D01');
    return D01;
  }

  /**
   * 关闭弹窗后回调
   * @returns
   */

  handleGoLoanModalClose() {
    this.setState({ isShowGuideLoanModal: false });
  }
  closeModal() {
    dispatchTrackEvent({
      target: this,
      beaconId: 'slienceUnfreezeCloseModal',
      beaconContent: {
        cus: {
          err: '异常关闭静默解管控弹窗'
        }
      },
      event: EventTypes.EV
    });
    clearInterval(this.countDownInterval);
    const {
      resultCallback
    } = this.props;
    if (resultCallback && typeof resultCallback === 'function') {
      resultCallback({
        type: 'interface-fail'
      });
    }
    this.setState({
      isShowGuideModal: false,
      isShowUnfreezeModal: false
    });
  }


  countDown(caseNo) {//入参为案件号
    const {
      resultCallback,
      hasRestoreType,
      userId,
      customerId
    } = this.props;
    if (!caseNo) {//没有案件号(建案失败)，清楚定时器,关闭弹窗
      this.closeModal()
      return
    }
    let hasGetSuccessResult = false;
    this.countDownInterval = setInterval(async () => {
          const newCountingNum = this.UnfreezeCount - 1; // 下次计数
          // 更新记数
          this.UnfreezeCount = newCountingNum;
          // 如果用户是额度压降的用户，倒计时出结果，成功，让它继续倒计时，不用继续查询applyinfo，倒计时结果就清除就可以了
          if (hasGetSuccessResult && hasRestoreType) {
            if (newCountingNum === 0) {
              clearInterval(this.countDownInterval);
            }
            return;
          }
          dispatchTrackEvent({
            target: this,
            beaconId: 'slienceUnfreezeInitApplyInfo',
            event: EventTypes.EV,
          })
          const promise = [];
          promise.push(queryCaseInfo({ queryCaseScene: '1', caseNo }, false, false));
          promise.push(queryCreditStatus());
          const [caseInfo, applyCreditRes] = await Promise.all(catchPromiseErr(promise));
          if (caseInfo.interfaceFail || applyCreditRes.interfaceFail) {
            this.closeModal()
            return;
          }
          const { applyState, controlFlag } = caseInfo || {}
          // 获取回收额度池的额度
          const { activeLimitInfoList = [] } = applyCreditRes || {};
          const recycleLimitInfo = activeLimitInfoList && activeLimitInfoList.filter(item => item.poolType === 'RECYCLE_FIX');
          const { activateLimit } = (recycleLimitInfo && recycleLimitInfo[0]) || {};
          // 出了接管控成功结果而且恢复状态不为待恢复或者恢复中，触发回调事件，告诉业务方可以请求额度接口，刷新页面了
          this.setState({
            activateLimit: activateLimit
          });
          if (applyState === '5') {
            if (resultCallback && typeof resultCallback === 'function') {
              resultCallback({
                type: 'success'
              });
            }
            const result = (await queryAccount({ userId, customerId, queryScene: '00' })) || {};
            const D01 = this.adpterAccount(result) || {};  // 取 D01 额度
            let tempLimit = D01.availLimit && parseFloat(D01.availLimit);
            if (tempLimit > 100) {
              clearInterval(this.countDownInterval);
              this.setState({
                isShowGuideModal: false,
                isShowUnfreezeModal: false,
                isShowGuideLoanModal: true,
                availLimit: tempLimit.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',') // 转换千分位格式处理
              });
            } else {
              if (hasRestoreType) {
                // 这里只会进入一次，后续会被上面return，走不下来
                hasGetSuccessResult = true;
                // ---兜底关闭弹窗，3s后自动关闭弹窗
                this.goCountDown();
              } else {
                this.setState({
                  isShowGuideModal: false,
                  isShowUnfreezeModal: false
                })
                clearInterval(this.countDownInterval);
              }
            }
          } else if (applyState === '6' || controlFlag === 'Y') {//对应旧的applyStep=7
            // 失败情况下，展示引导弹窗，关闭倒计时
            // 清除定时器
            clearInterval(this.countDownInterval);
            await this.getQueryAdmission();
            return;
          }
          // 倒计时结束展示刷新按钮，清除计时器
          if (newCountingNum === 0) {
            clearInterval(this.countDownInterval);
            // 倒计时结束还未出结果，默认失败
            // 原有的applyStep=3审批中状态所对应新的applyState值，3-其他状态以外的审批中、4-强补件、7-不可预约、8-待预约、9-已预约、10-专家评估中
            const approvalStatusList = ['3', '4', '7', '8', '9', '10'];
            if (approvalStatusList.includes(applyState)) {
              if (resultCallback && typeof resultCallback === 'function') {
                resultCallback({
                  type: 'not-get-result'
                });
              }
              this.setState({
                isShowGuideModal: false,
                isShowUnfreezeModal: false
              });
              return;
            }
          }
      }, 1000);
  }

  /**
   * 点击提交
   */
  async onSubmit() {
    const {
      jumpBackUrl,
      needOpenWebview,
      controlCode
    } = this.props;
    // 直接跳转接管控建案
    let channel = Madp.getChannel();
    const hasClearAuth = (controlCode === 'C205' || controlCode === 'C206' || controlCode === 'C207');
    const unfreezeUrl = process.env.TARO_ENV !== 'h5' ? `/adjust/pages/index/index?busiType=unFreeze&mapCode=${hasClearAuth ? 'bdb8f0cdda4da3d9' : '6c57eb1e46e9c695'}`
      : `${urlDomain}/${channel}/adjust/#/pages/index/index?busiType=unFreeze&mapCode=${hasClearAuth ? 'bdb8f0cdda4da3d9' : '6c57eb1e46e9c695'}`;
    const jumpUrl = `${unfreezeUrl}&redirectUrl=${encodeURIComponent(jumpBackUrl)}&needOpenWebview=${needOpenWebview}`

    if (needOpenWebview === 'Y') {
      // app端首页需要新开页面，不然会将首页的地步导航栏带到结果页
      Madp.navigateTo({
        url: jumpUrl,
        useAppRouter: isMuapp()
      });
    } else if (process.env.TARO_ENV !== 'h5') {
      Madp.reLaunch({
        url: jumpUrl
      });
    } else {
      Madp.redirectTo({
        url: jumpUrl
      });
    }
    this.setState({
      isShowGuideModal: false,
      isShowUnfreezeModal: false
    });
  }

  getLoanUrl = () => {
    let loanUrl = getLocalConstant({ configName: 'loan', nameSpace: 'link' });
    loanUrl = `${loanUrl}?cashLoanMode=1`;
    return loanUrl;
  };

  /**
   * 点击去借钱
   */
  onLoan() {
    const {
      needOpenWebview,
      resultCallback
    } = this.props;
    // 直接跳转接管控建案
    const jumpUrl = this.getLoanUrl();
    if (resultCallback && typeof resultCallback === 'function') {
      resultCallback({
        type: 'success-goto-loan'
      });
    }

    if (needOpenWebview === 'Y') {
      // app端首页需要新开页面，不然会将首页的地步导航栏带到结果页
      Madp.navigateTo({
        url: jumpUrl,
        useAppRouter: isMuapp()
      });
    } else {
      Madp.navigateTo({
        url: jumpUrl
      });
    }
    this.setState({
      isShowGuideLoanModal: false
    });
  }

  async getQueryAdmission() {
    const {
      resultCallback,
      controlCode,
      suggest
    } = this.props;
    const hasClearAuth = (controlCode === 'C205' || controlCode === 'C206' || controlCode === 'C207');
    const { admissionDetails } = await queryAdmission('UNFREEZE', {}, { mapCode: hasClearAuth ? 'bdb8f0cdda4da3d9' : '6c57eb1e46e9c695' });
    const canSubmitUnfreezeAuth = admissionDetails && admissionDetails.multiAdjustLimitAdmissionDetail && admissionDetails.multiAdjustLimitAdmissionDetail.canSubmitUnfreezeAuth;
    const { loan_ctrl_typ } = suggest || {};
    const isC201Tag = loan_ctrl_typ && loan_ctrl_typ.resultVal === 'C201N';
    const C201Control = isC201Tag && controlCode === 'C201'
    this.setState({
      canSubmitUnfreezeAuth: canSubmitUnfreezeAuth
    });
    if (canSubmitUnfreezeAuth && !C201Control) {
      if (resultCallback && typeof resultCallback === 'function') {
        resultCallback({
          type: hasClearAuth ? 'fail-clear-auth' : 'fail-no-clear-auth'
        });
      }
      this.setState({
        isShowGuideModal: true,
        isShowUnfreezeModal: false
      });
      return;
    } else {
      if (resultCallback && typeof resultCallback === 'function') {
        resultCallback({
          type: 'fail-not-auth'
        });
      }
      this.setState({
        isShowGuideModal: false,
        isShowUnfreezeModal: false
      });
      return;
    }

  }

  async goCountDown() {
    setTimeout(() => {
      this.setState({
        isShowUnfreezeModal: false,
        isShowGuideModal: false
      });
      clearInterval(this.countDownInterval);
    }, 3000);
  }


  render() {
    const {
      isShowGuideModal,
      isShowUnfreezeModal,
      activateLimit,
      isShowGuideLoanModal,
      availLimit
    } = this.state;
    const {
      limitAmount,
      isShowCountDownModal,
      themeColor,
      controlCode,
      resultCallback,
      beaconId
    } = this.props;
    const hasClearAuth = (controlCode === 'C205' || controlCode === 'C206' || controlCode === 'C207');
    const unClearModalTitle = `建议认证<span style="color: ${themeColor || '#3477FF'}">更多资料</span>`;
    const clearModalTitle = `<span style="color: ${themeColor || '#3477FF'}">${activateLimit || limitAmount}</span>元可用额度`;
    const loanBgClassName = themeColor === '#E60027' ? 'guide-modal-red' : 'guide-modal-blue';
    return <MUView className="silence-unfreeze-comp">

      {isShowCountDownModal && isShowUnfreezeModal && <MUDialog isOpened={isShowUnfreezeModal && isShowCountDownModal} beaconId="silenceModal">
        <MUView className="unfreeze-restore">
          <MUView className="unfreeze-restore-bg1">
            <MUImage className="pt" src={safe} />
          </MUView>
          <MUView className="unfreeze-restore-text">
            为保障您的资金安全，正在进行账户安全检测...
          </MUView>
          <MUView className="unfreeze-restore-time">
            <MUView className="unfreeze-restore-time-num">{this.UnfreezeCount > 0 ? this.UnfreezeCount : '0'}</MUView>
            <MUView className="unfreeze-restore-time-text">秒</MUView>
          </MUView>
          <MUView className="unfreeze-restore-tip-block">
            <MUImage className="tip" src={tip} />
            <MUView className="text">全程信息加密，保障您的账户安全</MUView>
          </MUView>
        </MUView>
      </MUDialog>}
      {isShowGuideModal && <MUView className="guide-modal">
        <MUDialog isOpened={isShowGuideModal} beaconId="guideModal">
          <MUView>
            {hasClearAuth ? (
              <MUView className="guide-modal-bg">
                <MUIcon
                  beaconId="closeIcon"
                  onClick={() => this.setState({ isShowGuideModal: false })}
                  value="close2"
                  className="guide-modal-icon"
                  color="#a6a6a6"
                  size="18"
                />
                <MUView className="guide-modal-title">您有额度评估机会未使用</MUView>
                <MUView className="guide-modal-text-block">
                  <MUView className="guide-modal-text-block-word">
                    <MUView className="guide-modal-text-block-title">评估通过最高可获得</MUView>
                    <MURichText className="guide-modal-text-block-title" nodes={clearModalTitle} />
                    <MUView className="guide-modal-text-block-subtitle">请认证公积金、个税</MUView>
                  </MUView>
                  {themeColor === '#E60027' ? <MUImage className="guide-modal-text-block-picture-red" src={guideAndHasAuthRed} />
                    : <MUImage className="guide-modal-text-block-picture" src={guideAndHasAuth} />}
                </MUView>
                <MUView className="guide-modal-button" style={{ background: themeColor ? themeColor : '#3477FF' }} beaconId="clearGoUnfreeze" onClick={this.onSubmit.bind(this)}>去评估</MUView>
                <MUView className="guide-modal-sub-button" beaconId="clearClose" onClick={() => this.setState({ isShowGuideModal: false })}>暂不评估</MUView>
              </MUView>
            ) : (
              <MUView className="guide-modal-bg">
                <MUIcon
                  beaconId="closeIcon"
                  onClick={() => this.setState({ isShowGuideModal: false })}
                  value="close2"
                  className="guide-modal-icon"
                  color="#a6a6a6"
                  size="18"
                />
                <MUView className="guide-modal-title">您有额度评估机会未使用</MUView>
                <MUView className="guide-modal-text-block">
                  <MUView className="guide-modal-text-block-word">
                    <MURichText className="guide-modal-text-block-title less" nodes={unClearModalTitle} />
                    <MUView className="guide-modal-text-block-subtitle">您的额度暂不可用</MUView>
                  </MUView>
                  {themeColor === '#E60027' ? <MUImage className="guide-modal-text-block-picture-red" src={guideAndNotAuthRed} />
                    : <MUImage className="guide-modal-text-block-picture" src={guideAndNotAuth} />}
                </MUView>
                <MUView className="guide-modal-button" style={{ background: themeColor ? themeColor : '#3477FF' }} beaconId="unClearGoUnfreeze" onClick={this.onSubmit.bind(this)}>去评估</MUView>
                <MUView className="guide-modal-sub-button" beaconId="unClearClose" onClick={() => this.setState({ isShowGuideModal: false })}>暂不评估</MUView>
              </MUView>
            )}
          </MUView>
        </MUDialog>
      </MUView>}
      {isShowGuideLoanModal && <MUView className="guide-modal">
        <MUDialog isOpened={isShowGuideLoanModal} parentId={beaconId} beaconId="guideLoanModal">
          <MUView>
            <MUView className="guide-modal-bg">
              <MUIcon parentId={beaconId} beaconId="closeIconGuideLoanModal" onClick={() => this.handleGoLoanModalClose()} value="close2" className="guide-modal-icon" color="#a6a6a6" size="18" />
              <MUView className="guide-modal-title">经系统评估</MUView>
              <MUView className={loanBgClassName}>
                <MUView className="guide-modal-text-block-word">
                  <MUView className="guide-modal-text-block-title">您当前可用额度(元)</MUView>
                  <MUView className="guide-modal-text-block-title guide-modal-text-block-price" style={{ color: themeColor ? themeColor : '#3477FF' }}>{availLimit}</MUView>
                  <MUView className="guide-modal-text-block-subtitle guide-modal-text-block-desc">您的额度已恢复，建议立即使用额度借款</MUView>
                </MUView>
              </MUView>
              <MUView className="guide-modal-button" style={{ background: themeColor ? themeColor : '#3477FF' }} parentId={beaconId} beaconId="goGuideLoanModal" onClick={this.onLoan.bind(this)}>去借钱</MUView>
              <MUView className="guide-modal-sub-button" parentId={beaconId} beaconId="cancelGoGuideLoanModal" onClick={() => this.handleGoLoanModalClose()}>暂不使用</MUView>
            </MUView>
          </MUView>
        </MUDialog>
      </MUView>}
    </MUView>;
  }
  // }
}

SlienceUnfreezeComp.propTypes = propTypes;
SlienceUnfreezeComp.defaultProps = defaultProps;
export default observer(SlienceUnfreezeComp);
