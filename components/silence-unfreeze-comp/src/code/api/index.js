import { fetch, apiHost } from '@mu/business-basic';
import Madp from '@mu/madp';

const operationId = {
  /* 查询授信产品合同信息 */
  composeCase: 'mucfc.apply.apply.composeCase',
  applyInfo: 'mucfc.apply.apply.applyInfo',
  /** 申请准入检查 */
  doAdmission: 'mucfc.apply.admission.doAdmission',
  queryAccount: 'mucfc.loan.account.getAccount', // 查询账户接口
  createCase: 'mucfc.apply.acpt.createCase',//建案接口
  queryCaseInfo: 'mucfc.apply.acpt.queryCaseInfo',//查询案件信息
  queryCreditStatus: 'mucfc.apply.acpt.queryCreditStatus'//查询授信状态
}
const API_URL = {
  GET_APPLY_INFO: `${apiHost.mgp}/?operationId=${operationId.applyInfo}`,
  COMPOSE_CASE: `${apiHost.mgp}/?operationId=${operationId.composeCase}`,
  QUERY_ADMISSION_STATUS: `${apiHost.mgp}/?operationId=${operationId.doAdmission}`,
  QUERY_ACCOUNT: `${apiHost.mgp}/?operationId=${operationId.queryAccount}`,
  CREATE_CASE: `${apiHost.mgp}/?operationId=${operationId.createCase}`,
  QUERY_CASE_INFO: `${apiHost.mgp}/?operationId=${operationId.queryCaseInfo}`,
  QUERY_CREDIT_STATUS: `${apiHost.mgp}/?operationId=${operationId.queryCreditStatus}`
}
export const getUserInfo = async () => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.userInformation.getUserInfo`);
    return res || {};
  } catch (e) {
    return {};
  }
};

/**
 * 获取用户借款券种类
 */
 export const getComposeCase = async (params, mapCode, autoLoading = false) => {
  const res = await fetch(API_URL.COMPOSE_CASE, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading,
    reqEnvParams: {
      mapCode
    },
  });
  return res;
};

export const unBind = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.login.logout`, {
      data
    });
    return res;
  } catch (e) {
    return null;
  }
};

/**
 * 查询applyinfo信息
 */
 export const checkApplyInfo = async (params, autoLoading = true, autoToast = true, mapCode) => {
  const applyInfo = await fetch(API_URL.GET_APPLY_INFO, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading,
    autoToast,
    reqEnvParams: {
      mapCode
    },
  });
  return applyInfo;
};

/**
 * 查询准入状态
 * @param {string} admissionType 准入类型
 */
 export const queryAdmission = async (admissionType, admissionInfo = {}, envParams, autoLoading = false) => {
  const { admissionDetails, result } = await fetch(API_URL.QUERY_ADMISSION_STATUS, {
    data: {
      data: {
        admissionType,
        admissionInfo
      }
    },
    autoLoading,
    reqEnvParams: {
      ...envParams
    }
  });
  return {
    [admissionType]: (admissionDetails && admissionDetails[admissionType]) || {},
    result,
    admissionDetails
  };
};

/**
 * 获取用户借款券种类
 */
export const queryAccount = async (params, autoLoading = false) => {
  const res = await fetch(API_URL.QUERY_ACCOUNT, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading
  });
  return res;
};

// 静默解管控建案接口
export const createCase = async (params, mapCode, autoLoading = false) => {
  const res = await fetch(API_URL.CREATE_CASE, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading,
    reqEnvParams: {
      mapCode
    },
  });
  return res;
};

// 查询静默解管控建案信息
export const queryCaseInfo = async (params, autoLoading = true, autoToast = true) => {
  const applyInfo = await fetch(API_URL.QUERY_CASE_INFO, {
    method: 'POST',
    data: {
      data: {
        ...params
      }
    },
    autoLoading,
    autoToast,
  });
  return applyInfo;
};

// 查询授信状态
export  const queryCreditStatus = async (data) => {
  try {
    const res = await fetch(API_URL.QUERY_CREDIT_STATUS, {
      autoLoading: false
    });
    return res;
  } catch (error) {
  }
};
