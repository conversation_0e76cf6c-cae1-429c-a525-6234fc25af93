{"name": "@mu/silence-unfreeze-comp", "version": "1.0.2-alpha.17", "description": "intro component", "main": "dist/index.js", "main:h5": "dist/h5/index.js", "sideEffects": ["dist/*", "*.scss", "*.less", "*.css"], "files": ["dist", "screenshot.png", "README.md"], "scripts": {"dev:weapp": "madp build --type weapp -- --watch", "dev:qq": "madp build --type qq -- --watch", "dev:h5": "madp build --type h5 -- --watch debug", "dev:swan": "madp build --type swan -- --watch", "dev:alipay": "madp build --type alipay -- --watch", "build:component": "cross-env TARO_BUILD_TYPE=component madp build --ui", "prepublishOnly": "npm run build:component", "lint": "eslint ./src --fix", "lint:style": "stylelint \"src/**/*.scss\" --syntax scss", "lint:style-fix": "stylelint \"src/**/*.scss\" --syntax scss --fix", "test": "NODE_ENV=test && jest --coverage", "test:ci": "npm run build:h5 && npm run test", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "handle-tag": "node ./script/handle-tag.js", "release": "standard-version", "publish:npm-major": "npm run release -- -r major && npm run handle-tag && git push --follow-tags && npm publish", "publish:npm-minor": "npm run release -- -r minor && npm run handle-tag && git push --follow-tags && npm publish", "publish:npm-patch": "npm run release -- -r patch && npm run handle-tag && git push --follow-tags && npm publish", "publish:npm-alpha": "npm run release -- -p alpha -r patch && npm run handle-tag && git push --follow-tags && npm publish", "publish:npm-beta": "npm run release -- -p beta -r patch && npm run handle-tag && git push --follow-tags && npm publish"}, "keywords": ["taro", "business component", "Multiple platform", "material"], "standard-version": {"skip": {"tag": true}}, "devDependencies": {"@mu/basic-library": "1.7.16", "@mu/zui": "1.24.3-beta.37", "@mu/madp-cli": "1.8.9", "build-plugin-component": "1.0.0", "build-scripts": "1.1.1", "enzyme": "3.10.0", "prop-types": "15.5.8", "enzyme-adapter-react-16": "1.14.0", "@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-react-jsx": "7.9.4", "@babel/preset-env": "7.9.0", "@types/webpack-env": "1.15.1", "@types/react": "16.9.25", "classnames": "2.2.6", "babel-core": "7.0.0-bridge.0", "babel-eslint": "8.2.6", "babel-jest": "23.6.0", "babel-loader": "8.1.0", "babel-plugin-syntax-dynamic-import": "6.18.0", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "1.7.0", "conventional-changelog-cli": "2.0.31", "mini-css-extract-plugin": "0.9.0", "cross-env": "5.2.1", "cz-conventional-changelog": "2.1.0", "husky": "1.3.1", "jest": "23.6.0", "nerv-server": "1.5.7", "nerv-test-utils": "1.5.7", "nervjs": "1.5.7", "standard-version": "5.0.2", "typescript": "3.8.3", "vconsole": "3.3.4", "vconsole-webpack-plugin": "1.5.1", "webpack": "4.42.1", "webpack-bundle-analyzer": "3.6.1", "webpack-merge": "4.2.2", "@types/node": "12.12.31", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "7.6.0", "@typescript-eslint/eslint-plugin": "2.25.0", "@typescript-eslint/parser": "2.25.0", "commitlint": "8.3.5", "eslint": "6.8.0", "eslint-config-airbnb": "18.1.0", "eslint-config-o2team": "0.1.6", "eslint-plugin-import": "2.22.1", "eslint-plugin-jest": "21.27.2", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "2.5.1", "lint-staged": "7.3.0", "stylelint-config-standard": "18.3.0", "stylelint-scss": "3.16.0", "fs-extra": "8.1.0"}, "componentConfig": {"name": "SilenceUnfreezeComp", "title": "silence-unfreeze-comp", "category": "Modal", "screenshot": "", "repository": "", "autoClose": true, "source": {"type": "npm", "npm": "@mu/silence-unfreeze-comp", "version": "1.0.0", "registry": ""}, "props": {}}, "publishConfig": {"registry": "http://npm.mucfc.com"}}