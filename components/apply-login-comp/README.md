# apply-login-comp

@mu/apply-login-comp



intro component

## 预览图

预览截图，需手动替换为实际的预览图，图片名不可重命名

![screenshot.png](http://unpkg.mucfc.com/@mu/apply-login-comp/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/apply-login-comp`

### 样式引入

`@import "~@mu/apply-login-comp/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/apply-login-comp',
            '@mu\\apply-login-comp'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\apply-login-comp',
            '@mu/apply-login-comp',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/apply-login-comp':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/apply-login-comp')
    },
}
```

在这里写业务组件详细接入文档
