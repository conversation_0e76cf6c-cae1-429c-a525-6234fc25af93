
import {
  apiHost,
  fetch,
} from '@mu/business-basic';


const getSendSms = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.verifyIdentify.commonSendSms`, {
      autoLoading: false,
      data: {
        data
      }
    });
    return res;
  } catch (e) {
    return null;
  }
};

const getSmsLogin = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.loginRegister.unionLogin`, {
      autoLoading: false,
      data: {
        data
      }
    });
    return res;
  } catch (e) {
    return null;
  }
};

const getUserLogin = async (data) => {
  try {
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.loginRegister.unionLogin`, {
      autoLoading: false,
      data: {
        data
      }
    });
    return res;
  } catch (e) {
    return null;
  }
};


const getContractInfo = async (data) => {
  const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.contract.getContractInfo`, {
    method: 'POST',
    autoLoading: false,
    data: {
      data
    }
  });
  return res || {};
};

const getVerifySmsCode = async (data) => {
  const res = await fetch(`${apiHost.mgp}?operationId=mucfc.basic.sms.verify`, {
    autoLoading: false,
    data: {
      data
    },
  });
  return res;
};


const unionBindAuthNew = async (param, envParams) => {
  try{
    const res = await fetch(`${apiHost.mgp}?operationId=mucfc.user.loginRegister.unionBindAuth${param}`, {
      autoLoading: false,
      envParams: {
        ...envParams
      },
    });
    return res;
  } catch (error) {
    return Promise.reject(error);
  }
};


export {
  getSendSms, getSmsLogin, getUserLogin,
  getContractInfo, getVerifySmsCode, unionBindAuthNew
};
