import Madp, { Component } from '@mu/madp';
import { Url } from '@mu/madp-utils';
import { MUView, MUAuthCode } from '@mu/zui';
import { track, EventTypes, dispatchTrackEvent } from '@mu/madp-track';
import PropTypes from 'prop-types';
import { startGeetest } from '@mu/geestest';
import MUBioMetrics from '@mu/biometrics';
import { LoginArea, IdArea } from '@mu/login-popup';
import { getSendSms, getSmsLogin, getUserLogin, getVerifySmsCode, unionBindAuthNew } from './api';
import { observer, PropTypes as MobxPropTypes } from '@tarojs/mobx';
if (!['tt', 'swan', 'kwai'].includes(process.env.TARO_ENV || '')) {
  require('../style/index.scss');
}

const defaultProps = {
  partnerMaskMobile: '',
  beaconIds: 'ApplyLoginComp',
};

const propTypes = {
  partnerMaskMobile: PropTypes.string,
  beaconIds: PropTypes.string,
};

@track((props) => ({
beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
uiType: 'ApplyLoginComp', // 就是当前组件类名
}))

class ApplyLoginComp extends Component {
  constructor(props) {
    super(props);

    this.state = {
      onePassLogin: false,
      authCodeOpened: false,
      authCodeToken: '',
      partnerMaskMobile: '',
      maskRealName: '',
      isLoginOpen: false,
      isNeedOnePass: false,
      idAreaOpen: false,
      unicomCheckResult: false,
      showLoginComp: false
    };

    const { track = {} } = this.props;
    this.pageId = track && track.trackedBeaconId;
  }

  async componentDidMount() {
    const { partnerMaskMobile, handLoginJump } = this.props;
    if (partnerMaskMobile) {
      let param = '';
      param = `&channel=${Madp.getChannel()}&faceScene=true&useLoginByAgreement=Y&agrToken=${Url.getParam('agrToken')}&needRedirect=0`;
      // 已经有手机号的，直接发起登录请求
      const { loginSuccess, loginProcessType } = await unionBindAuthNew(param);
      // 登录成功，则触发回调事件

      if (loginSuccess) {
        if (handLoginJump && typeof handLoginJump === 'function') {
          handLoginJump();
        }
        return;
        // 没有一键登录成功的用户，走后面逻辑唤起登录半屏弹窗
      } else if (loginProcessType !== 'SMS_LOGIN') {
        // 用户没有登录成功或者loginProcessType不等于SMS_LOGIN，则是接口报错，直接接口toast提示了
        return;
      }
      // 到这里还没有return住的，则是有手机号，但一键登录失败，需要往下走，去打开动码登录；
    }
    // 有手机号的用户是直接请求接口就可以，不需要调用半屏登录弹窗，走到这里的都是没有手机号的用户，打开半屏登录弹窗
    this.setState({
      showLoginComp: true
    })

    const unicomCheckResult = Url.getParam('unicomCheckResult') || false;
    dispatchTrackEvent({
      target: this,
      beaconId: 'LoginState',
      event: EventTypes.EV,
      beaconContent: {
        cus: {
          isAgreement: !!partnerMaskMobile,
          isLogin: false
        }
      }
    });
    this.setState({ partnerMaskMobile, unicomCheckResult }, () => {
      this.initOnePassH5();
    });
  }

  static options = {
    addGlobalClass: true
  }
  config = {
    styleIsolation: 'shared'
  }

  componentWillReceiveProps(nextProps) {
    const { partnerMaskMobile, flag } = this.props;
    if (partnerMaskMobile !== nextProps.partnerMaskMobile) {
      this.setState({ partnerMaskMobile: nextProps.partnerMaskMobile });
    }

    if (flag !== nextProps.flag) {
      this.initOnePassH5();
    }
  }

  initOnePassH5() {
    const { onePassH5 } = MUBioMetrics;
    const { init } = onePassH5;
    Madp.showLoading({ title: '加载中...' });
    init((res) => {
      if (res.code === '1') {
        Madp.hideLoading();
        // alert('初始化本机校验成功')
        this.setState({
          onePassLogin: true
        }, () => {
          this.initPageDate();
        });
      } else {
        Madp.hideLoading();
        // alert(JSON.stringify(res.code + res.msg));
        this.initPageDate();
        console.warn(res.code, res.msg);
      }
    });
  }

  initPageDate = () => {
    const { partnerMaskMobile } = this.props;
    if (partnerMaskMobile) {
      this.localVerification();
    } else {
      this.preJumpToApplyPage2();
    }
  }


  // 本机校验
  localVerification = () => {
    const { onePassH5 } = MUBioMetrics;
    const { startOnePass } = onePassH5;
    const { unicomCheckResult, onePassLogin } = this.state;
    if (!onePassLogin) { // 如果本机校验失败
      if (unicomCheckResult) { // 但是联通允许的登录方式
        const loginType = 'UNICOM_VERIFY_LOGIN';
        this.authCodeOk('', loginType); // 本机校验通过
      } else {
        this.setState({ authCodeOpened: true }); // 否则需要输入验证码
      }
      return;
    }
    Madp.showLoading({ title: '加载中...' });
    startOnePass({
      scene: 'SCENE_LOGIN',
      onError: (errCode) => {
        Madp.hideLoading();
        // alert('本机校验验证发生异常 ' + JSON.stringify(errCode));
        if (unicomCheckResult) {
          const loginType = 'UNICOM_VERIFY_LOGIN';
          this.authCodeOk('', loginType);
        } else {
          this.setState({ authCodeOpened: true });
        }
      },
      onOk: (token) => {
        // alert('校验成功');
        Madp.hideLoading();
        this.dologin(token);
      }
    });
  }


  preJumpToApplyPage2 = () => {

    dispatchTrackEvent({
      target: this,
      beaconId: 'LoginArea',
      event: EventTypes.SO,
    })
    this.setState({ isLoginOpen: true, isNeedOnePass: true });
  }

  dologin = async (token) => {
    // const apiName = 'mucfc.user.login.login';
    const paraDic = {
      eventId: 'A70002',
      token,
      verifyScene: true,
      loginType: 'LOCALVERI'
    };
    Madp.showLoading({ title: '正在登录...' });
    const res = await getUserLogin(paraDic);
    Madp.hideLoading();
    if (res && res.needVerify) {
      this.setState({
        maskRealName: res && res.maskRealName || '',
        idAreaOpen: true,
      });
    } else {
      this.idAreaSuccess('checkLogin', res);
    }
  }

  sendSmsCodeFore = async () => {
    const params = {};
    // const data = await MUSafeSms.init({ verifyType: 'SMP', tokenScene: 'SCENE_LOGIN' });
    // params.token = data.token;
    try {
      const geestestRet = await startGeetest({ scene: 'SCENE_LOGIN' });
      if (geestestRet && geestestRet.verifyResult === 1 && geestestRet.token) {
        params.geetestToken = geestestRet.token;
      }
      const { partnerMaskMobile } = this.state;
      params.verifyType = 'SMP';
      params.tokenScene = 'SCENE_LOGIN';
      params.mobile = partnerMaskMobile;
      params.firstScene = 'COMMON_MASKVALUE_SMS_ONE_MINUTE';
      this.sendSmsCodeAction(params);
    } catch (e) {
      alert(JSON.stringify(e));
    }
  }

  async sendSmsCodeAction(params) {
    await this.getSendSms(params);
    // this.setState({ authCodeToken: params.token });
  }

  // 发送验证码
  async getSendSms(params) {
    try {
      const res = await getSendSms(params);
      if (res && res.smsToken) {
        this.setState({ authCodeToken: res.smsToken });
      }
      Madp.showToast({
        title: '验证码发送成功',
        icon: 'none',
        duration: 2000
      });
    } catch (e) {
      // this.setState({ authCodeOpened: false })
      console.error(JSON.stringify(e));
    }
  }

  // 发送验证码
  handleSendCode = () => {
    this.sendSmsCodeFore();
  }

  // 更换手机号
  handleMobileReplace = () => {
    dispatchTrackEvent({
      target: this,
      beaconId: 'changeMobile',
      event: EventTypes.EV,
    });
    // 解决本机校验点击更换手机号去执行init问题
    const { onePassH5 } = MUBioMetrics;
    const { init } = onePassH5;
    init(()=>{});
    this.setState({ isNeedOnePass: true, isLoginOpen: true, authCodeOpened: false });
  }

  verifySmsCode = async (params) => {
    const data = {
      token: params.token,
      mobile: params.mobileNo,
      code: params.smsCode,
      checkToken: 'N'
    };
    const res = getVerifySmsCode(data);
    return res;
  }

  // 验证码
  authCodeOk = async (smsCode, loginType) => {
    const { partnerMaskMobile } = this.state;
    const { authCodeToken } = this.state;
    const params = {
      mobileNo: partnerMaskMobile,
      verifyScene: true
    };
    if (authCodeToken) params.token = authCodeToken;
    if (smsCode) params.smsCode = smsCode;
    // if (scene) params.scene = scene;
    if (loginType === 'UNICOM_VERIFY_LOGIN') {
      params.loginType = loginType;
    } else { // 验动码
      await this.verifySmsCode(params);
      params.loginType = 'SMS';
    }
    const data = await this.getSmsLogin(params);
    if (data) {
      if (data.loginSuccess) {
        this.idAreaSuccess('codeLogin', data);
      }
      if (data.needVerify) {
        this.setState({ 
          maskRealName: data && data.maskRealName || '',
          idAreaOpen: true, 
          authCodeOpened: false 
        });
      }
    }
  }

  getSmsLogin = async (params) => {
    try {
      const data = await getSmsLogin(params);
      return data;
    } catch (e) {
      dispatchTrackEvent({
        target: this,
        beaconId: 'ErrorPrompt',
        event: EventTypes.SO,
      });
      console.error(JSON.stringify(e));
    }
  }

  closeCallBack = async () => {
    dispatchTrackEvent({
      target: this,
      beaconId: 'closeLoginModal',
      event: EventTypes.EV,
    });
    const { closeModalCallBack } = this.props

    if (closeModalCallBack && typeof closeModalCallBack === 'function') {
      closeModalCallBack()
    }
  }

  /**
   * 登录成功，上传埋点且关闭弹窗
   * @param {*} scene 登录方式
   * @param {*} loginData 登录成功接口返回数据
   */
  idAreaSuccess = (scene, loginData) => {
    const { handLoginJump } = this.props;
    dispatchTrackEvent({
      target: this,
      beaconId: 'LoginSuccess',
      event: EventTypes.EV,
      beaconContent: {
        cus: {
          scene
        }
      }
    });
    this.setState({ idAreaOpen: false, isLoginOpen: false });
    setTimeout(() => {
      if (handLoginJump && typeof handLoginJump === 'function') {
        handLoginJump();
      }
    }, 300);
  }

  render() {
    const {
      authCodeOpened, partnerMaskMobile, maskRealName, isLoginOpen, isNeedOnePass, onePassLogin, idAreaOpen, showLoginComp } = this.state;
    return (
      <MUView>
        {showLoginComp && <MUView className="login-simplify">
          <MUView className="login-simplify__auth-code">
            <MUAuthCode
              beaconId="AuthCode"
              isOpened={authCodeOpened}
              title="请输入短信验证码"
              replaceMsg="更换手机号"
              parentId={this.pageId}
              phoneCode={partnerMaskMobile}
              onSendCode={this.handleSendCode}
              onMobileReplace={this.handleMobileReplace}
              onOk={this.authCodeOk}
              onClose={() => {this.setState({ authCodeOpened: false }), this.closeCallBack()}}
            />
          </MUView>
          <LoginArea
            parentId={this.pageId}
            isOpen={isLoginOpen}
            needOnePass={isNeedOnePass}
            initOnePass={onePassLogin}
            needFocusInput
            needAgreement={false}
            beaconContent={{
            }}
            onClose={() => {this.setState({ isLoginOpen: false }), this.closeCallBack()}}

          // redirectUrl={encodeURIComponent(getCurrentPageUrlWithArgs())}
            onSuccess={(loginData) => { this.idAreaSuccess('', loginData); }}
            goReal={(data) => {
              this.setState({ 
                isLoginOpen: false,
                idAreaOpen: true,
                maskRealName: data && data.maskRealName || ''
              });
            }}
            buttonText="确定"
          />
          <IdArea
            parentId={this.pageId}
            isOpen={idAreaOpen}
            idAmount={4} // 身份证输入位数默认为6，改为4位
            maskRealName={maskRealName} // 是否展示掩码姓名及申诉入口
            onSuccess={(loginData) => { this.idAreaSuccess('', loginData); }}
            onClose={() => {this.setState({ idAreaOpen: false }), this.closeCallBack()}}
            
          />
        </MUView>}
      </MUView>
    );
  }
  // }
}

ApplyLoginComp.propTypes = propTypes;
ApplyLoginComp.defaultProps = defaultProps;
export default observer(ApplyLoginComp);
