# 文字进度条

@mu/text-progress-bar



横向文字进度条

## 预览图

![screenshot.png](http://unpkg.mucfc.com/@mu/text-progress-bar/screenshot.png)

## 业务组件接入说明

### 安装

1. 自动安装 - 使用 appworks 插件插入组件会自动安装依赖
2. 手动安装 - `npm i @mu/text-progress-bar`

### 样式引入

`@import "~@mu/text-progress-bar/dist/style/index.scss";`

### 配置文件修改

业务工程需要修改 `config/extend.js` 文件，添加组件编译

```
{
    // h5的业务模块
    h5: {
        esnextModules: [
            ...
            '@mu/text-progress-bar',
            '@mu\\text-progress-bar'
        ]
    },
    // 小程序壳工程
    weapp: {
        compile: {
            include: [
            ...
            '@mu\\text-progress-bar',
            '@mu/text-progress-bar',
            ]
        }
    },
    alias: {
        ...
        '@mu/[业务模块名]/text-progress-bar':
            process.env.TARO_ENV === 'h5'
            ? path.resolve(__dirname, '..', 'node_modules', '@mu/agreement')
            : path.resolve(__dirname, '..', 'src/[业务模块名]/components/text-progress-bar')
    },
}
```

组件详细接入文档

|  属性   | 类型  |  是否必填  | 默认值  | 描述  |
|  ----  | ----  |  ----  | ----  | ----  |
| steps  | Array<string> | 否  | ['身份认证', '实名认证', '完善信息'] | 步骤 |
| doneStep  | number | 否  | 0 | 已完成步骤 |