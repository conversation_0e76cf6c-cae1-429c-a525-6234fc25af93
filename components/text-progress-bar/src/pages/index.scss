@import '~@mu/zui/dist/style/variables/default.scss';

.text-progress-bar {
  display: flex;
  // .mu-form__title--list 有 20px margin-top，要减去，再减去字体行高7px
  padding: 25px 0 5px;
  justify-content: center;

  &__item {
    display: flex;
    align-items: center;
    font-size: $font-size-base;
    color: $color-grey-0;

    .number {
      color: $color-grey-0;
      font-size: $font-size-sm;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 2;
      width: 32px;
      height: 32px;
      border-radius: 32px;
      margin: 0 6px 0 0;
      border: 2px solid $color-grey-0;
    }

    .icon {
      margin: 0 6px 0 0;
    }

    .step-name {
      line-height: 2;
    }

    .line {
      display: block;
      height: 4px;
      width: 48px;
      border-radius: 2px;
      background: $color-grey-0;
      margin: 0 26px;
    }

    &--lighten {
      color: $color-brand;

      .line,
      .number {
        border: 2px solid $color-brand;
        // background: $color-brand;
      }
    }
  }
}
