/**
 * 业务组件需要用到的公共方法封装
 * */
import Madp from '@mu/madp';

const getDefaultChannel = () => {
  const channel = {
    WEAPP_DEFAULT_CHANNEL: '1MNP',
    SWAN_DEFAULT_CHANNEL: 'MOCK',
    ALIPAY_DEFAULT_CHANNEL: 'MOCK',
    TT_DEFAULT_CHANNEL: 'MOCK',
    H5_DEFAULT_CHANNEL: 'MOCK'
  };
  let defaultChannel = 'MOCK';

  switch (Madp.getEnv()) {
    case Madp.ENV_TYPE.WEAPP:
      defaultChannel = channel.WEAPP_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.SWAN:
      defaultChannel = channel.SWAN_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.ALIPAY:
      defaultChannel = channel.ALIPAY_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.TT:
      defaultChannel = channel.TT_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.WEB:
      defaultChannel = channel.H5_DEFAULT_CHANNEL;
      break;
    default:
      defaultChannel = channel.H5_DEFAULT_CHANNEL;
      break;
  }

  return defaultChannel;
};

let channel = Madp.getChannel();
if (!channel || channel === 'MOCK') {
  channel = getDefaultChannel();
}

/**
 * 主题色变量映射表
 * 原在运行时确定主题，通过require不同的scss暴露的色号
 * ->在weapp下编译会报错（require使用了运行时才能确定的变量）
 * ->在h5下会影响主题色样式文件的import（动态require覆盖）
 */
const themeColorMap = {
  default: {
    brand: '#3477FF',
    brandLight: '#78A4F4',
    brandDark: '#346FC2'
  },
  redpink: {
    brand: '#E60027',
    brandLight: '#E60027',
    brandDark: '#E60027'
  },
};

const channelConfig = {
  '3CUAPP': 'redpink'
}

const theme = channelConfig[channel] || 'default';

console.log('theme', theme);

const themeColor = (themeColorMap && themeColorMap[theme]) || {};

export {
  themeColor
}
