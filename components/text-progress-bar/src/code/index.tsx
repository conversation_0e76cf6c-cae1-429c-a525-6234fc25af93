import  { Component } from '@mu/madp';
import { track } from '@mu/madp-track';
import { InferProps } from 'prop-types';
import { MUView, MUIcon } from '@mu/zui';
import { MUTextProgressBarProps, MUTextProgressBarState } from './types/component';
import { themeColor } from './utils';
if(!['tt','swan','kwai'].includes(process.env.TARO_ENV||'')) {
  require('../style/index.scss');
}
const activeStyle = {
  color: themeColor.brand,
};
const borderStyle = {
  border: `1px solid ${themeColor.brand}`

}
const bgStyle = {
  backgroundColor: themeColor.brand,
};
@track((props) => ({
  beaconId: props.beaconId, // 自定义组件里面一定要加上track注解并且 包含这三个参数
  beaconContent: props.beaconContent, // 接收引入本组件传进来的beaconContent
  uiType: 'TextProgressBar', // 就是当前组件类名
}))
export default class TextProgressBar extends Component<MUTextProgressBarProps, MUTextProgressBarState> {
  public static options = {
    addGlobalClass: true
  }
  config:any = {
    styleIsolation:'shared'
  }
  public static defaultProps: MUTextProgressBarProps;

  public static propTypes: InferProps<MUTextProgressBarProps>;

  public render(): JSX.Element {
    const { steps = ['身份认证', '实名认证', '完善信息'], doneStep = 0 } = this.props;
    return (
      <MUView className="text-progress-bar">
        {steps && steps.length && steps.map((step, index) => (
          <MUView
            className="text-progress-bar__item"
            style={{ ...(doneStep >= index + 1 ? activeStyle : {}) }}
          >
            {doneStep >= index + 2 && (
            <MUIcon
              className="icon"
              value='checked'
              size="17"
            />
            )}
            {doneStep <= index + 1 && (
            <MUView
              className="number"
              style={{ ...(doneStep >= index + 1 ? { ...borderStyle, ...activeStyle } : {}) }}
            >
              {index + 1}
            </MUView>
            )}
            <MUView className="step-name">{step}</MUView>
            {index + 1 !== steps.length && (
            <MUView
              className="line"
              style={{ ...(doneStep >= index + 2 ? bgStyle : {}) }}
            />
            )}
          </MUView>
        ))}
      </MUView>
    );
  }
}
