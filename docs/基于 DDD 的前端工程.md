# 基于 DDD 的前端工程

## 分层架构

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/Rq8z1PLnvha4wKAN/1b0b6788b64a4c18b71850b44cdb2c111257.png)

整体理念基于领域驱动设计（Domain-Driven Design），领域层位于最核心的位置，并且有着一个严格的依赖规则：上层可以依赖下层，但下层不能依赖上层。

目标是在前端工程设计中能做到：

*   视图层尽可能薄：获得的数据能够直接使用到视图层中，禁止在视图层中对数据进行转换、筛选、计算等逻辑操作。
    
*   不同职责的代码进行分层：将不同职责代码合理分层，每层尽可能纯净，互不影响。
    
*   前端字段不受后端影响：返回字段进行纠正，字段含义尽可能直观，在视图层使用时，能够更清晰地描述视图结构。
    
*   可纵观全局领域：前端进行领域模块结构设计时，能够纵览整个项目下所有的领域，以及每个领域下具有的逻辑功能。
    

### 领域层 domain

领域层是整个项目的核心层，它掌管了所有领域下的行为与定义。这部分应该是与具体技术实现无关的纯粹的业务逻辑。

**聚合：**指的是限界上下文中一个个的代码集合体，由实体或值对象构成，其中有一个处于根节点的实体被称为**聚合根**（Aggregate Root）。聚合根控制着所有聚集在其中的其他元素，并且它的名称就是整个聚合的名称。

**实体：**是充血的数据对象，包含了数据和业务规则，且数据和业务规则是内聚的。每个实体都有一个唯一标识（ID），例如订单、用户、账户等。实体在全局领域中是唯一的，不可能在别的领域中存在相同的实体。在前端中，我们把它定义为一个 class 类，在构造函数中初始化实体的属性，在类中定义了实体的方法，实现与其自身紧密相关的业务操作， 例如，LoanApplication 实体可能有 submit()、approve()、reject()等方法。同一个实体的逻辑确保只在实体类中编写，在不同视图下可复用。

**值对象：**也是充血的数据对象，也可以包含业务规则，但没有唯一标识，例如Money、Address、Phone等。

**领域服务层：**领域服务封装不适合放在实体或值对象中的领域逻辑，当某个业务操作或领域逻辑：    

*   涉及多个不同的实体（或聚合）。
    
*   不自然地属于任何一个特定的实体（即，将这个逻辑放在任何一个实体中都会显得不协调或破坏该实体的单一职责）。
    
*   代表一个重要的领域过程或计算，但本身不是一个“事物”（没有身份）。
    
*   可能需要访问外部资源（如后台服务）来协调操作
    

 领域服务本身通常是无状态的。它们不持有自己的数据状态，而是操作传入的领域对象（实体或值对象）。每次调用的结果仅依赖于输入参数。

### 数据接口层 data-source

数据接口层是整个项目的根基，位于架构分层中的基础设施层，提供了结构清晰、定义规范、前端可直接使用的数据。为了收敛业务逻辑，数据接口层只可以被领域层调用。

**api：**数据请求层，负责 HTTP 请求，是项目中唯一与后端服务进行交互的一层。在这一层中集结了项目内所有的接口函数，避免了数据接口分散到各个页面，统一存放更易管理。

**translator：**数据转换层，这层负责将后端返回的数据“清洗”，改造成更直观的字段(key)、更方便使用的数据(value)。在这一层对接口字段、内容经过二次加工，避免了后端定义字段不规范、混乱对前端的影响，含义清晰、规范的字段在视图层使用时更具有表现力，这里我们解决了上文提出的接口字段不可控性问题。

### 视图层

包括 pages 和 components 中的 tsx 和样式部分。

视图层也就是我们书写交互逻辑、样式的一层，唯一跟前端框架耦合(React、Vue)的一层，这一层只需要调用页面 store，将返回值直接体现在视图层中，无需编写条件判断、数据筛选、数据转换等与视图展示无关的逻辑代码，这些“糙活”都在其他层中已经完成，所以视图层是非常“薄”的一层，只需关注视图的展示与交互，整个 HTML 结构非常直观清晰。

分层作用：将控制层中返回的数据直接使用，视图层中只编写交互与样式。除了视图层与前端框架有关，其他层可独立应用于任何框架的。

### 控制层

包括 pages 和 components 下的 store。 

管理 UI 状态、处理用户交互、对领域对象和领域服务进行编排和组合，一般不包含具体的业务逻辑。控制层被视图层调用。

## 目录结构

```powershell
tbf-loan-fe  
+ bin                           # 可执行命令，如构建脚本
+ config                       # 构建打包配置
+ src                           # 源代码目录
  + domain                  # 领域目录，对应领域层                      
    + models                  # 领域模型 (实体和值对象)
      + loan                    # 借款聚合
        - loan.ts                 # 聚合根
        - coupon.ts               # 优惠券实体
        - amount.ts               # 值对象
      + bill                    # 账单聚合
        - bill.ts           
    + services            # 领域服务
      - loanSubmissionService.ts
      - formValidationService.ts
  + data-source                     # 数据源目录，对应数据接口层
      + user
        - api.ts                  # 用户相关API封装
        - translator.ts           # 数据转换器
      + ...
  + components               # 模块内部可复用组件
  + pages                     # 页面目录
    + homepage             # page1功能目录
      + components          # 页面内组件
        + component1          # 页面内组件1
          - store.ts         # 组件store
          - index.tsx      
      - index.tsx            # 页面入口，对应视图层
      - store.ts             # 页面store，对应控制层
    + page2
    + page3
  + constants                # 常量配置    
  + assets                    # 图片、字体等静态资源
  + styles                      # 全局样式、主题变量
  + utils                       # 工具函数目录，对应基础层
  - app.tsx                     # 模块入口文件 
  
- .npmrc                        # NPM配置
- .editorconfig                # 编辑器配置
- tsconfig.json                # TypeScript配置
- package.json                 # 依赖包声明
- package.lock.json           # 依赖包版本锁定
- README.md  
```

## DDD 的好处

对于前端而言，实施DDD有肉眼可见的好处：

*   稳定的业务知识体系
    
*   可传承的代码体系
    
*   脱离UI的单元测试
    
*   跨端开发、多端共用的便捷性
    
*   明确的团队分工
    
*   需求变更的快速响应
    
*   持续敏捷
    

这些好处对于需要长时间持续迭代的项目团队而言，非常有价值。

## 参考资料

*   [领域驱动设计在前端中的应用](https://mp.weixin.qq.com/s/pROCXZNZ7RKeYDlDUJng_Q)
    
*   [探究 DDD 在前端开发中的应用（二）：什么是 DDD](https://darkyzhou.net/articles/frontend-with-ddd-2)
    
*   [《领域驱动设计精粹》（作者 Vaughn Vernon）](https://book.douban.com/subject/30333944/)