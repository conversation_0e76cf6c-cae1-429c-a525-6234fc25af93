/**
 * 含有lui的业务区块测试页面
 * */
/* eslint-disable */
import Madp, { Component } from '@mu/madp';
import { getEnv } from '@mu/madp-utils';
import { MUView } from '@mu/zui';
import { injectState } from '@mu/leda';
import CodeSnippet from '../code';
import './index.scss';
// leda引擎获取数据，调试demo页面，可使用实际业务的pageid测试业务组件
// const pageId = 'f8a1902a-2cb9-456d-a903-acf335a7f976'; // 该pageId是app首页的pageId
// 需要引入的展位组件 stateKey 集合，展位组件名首字母改成小写，一个展位需要多次使用时需要在末尾加上$符号和数字(数字从1开始)
// eg: bannerWithoutBorder$1, bannerWithoutBorder$2
const stateKeys = [
  'bannerWithoutBorder',
];
//
// @injectState({
//   debug: true,
//   isOpenCache: true,
//   isCacheCustomerTag: true,
//   pageId,
//   stateKeys,
// })
export default class DemoPage extends Component {

  constructor() {
    super();
    this.state = {
      isWEAPP: getEnv() === Madp.ENV_TYPE.WEAPP,
      isALIPAY: getEnv() === Madp.ENV_TYPE.ALIPAY,
      isWEB: getEnv() === Madp.ENV_TYPE.WEB,
    };
  }

  onButtonClick = (event) => {
    const {
      isWEB,
    }
      = this.state;
    if (isWEB) {
      alert('您点击了按钮！');
    } else {
      Madp.showModal({ content: '您点击了按钮！', showCancel: false });
    }
  };

  render() {
    const { bannerWithoutBorder } = this.state;
    return (
      <CodeSnippet luiData={bannerWithoutBorder} />
    );
  }
}
