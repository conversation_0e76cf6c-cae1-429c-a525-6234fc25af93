import Madp from '@mu/madp';
import PropTypes from 'prop-types';
import { injectState } from '@mu/leda';
import { MUView, MUImage, MUButton } from '@mu/zui';
import { BottomBtn, ImageTitle } from '@mu/lui';
import {
  urlDomain,
  channel,
  getMapCode
} from '../utils';
import './index.scss';
import entryImg from '../img/entry.png';

const defaultImgMap = {
  bld: 'https://file.mucfc.com/cop/1/28/202109/202109130939238691d8.png',
  ygd: 'https://file.mucfc.com/cop/1/28/202109/202109130939232e82ce.png'
};

const defaultPageTitle = {
  ygd: '申请员工贷',
  bld: '申请白领贷'
};

const LEGAO_PAGE_ID = 'e1e7597d-5202-4d01-876d-e1bd7b241d87';
@injectState({
  pageId: LEGAO_PAGE_ID,
  stateKeys: [
    'bottomBtn',
    'imageTitle',
  ],
  didShowRefresh: false
})
class CommonIntro extends Madp.Component {
  constructor(props) {
    super(props);

    this.state = {
      imgStatus: {
        dynamicImg: false,
        staticImg: false,
      },
      buttonStatus: {
        dynamicButton: false,
        staticButton: false,
      }
    };
  }

  async componentDidMount() {
    const {
      type,
      pageTitle,
    } = this.props;
    Madp.setNavigationBarTitle({
      title: pageTitle || defaultPageTitle[type] || ''
    });
  }

  ledaDidMount(state) {
    const {
      imageTitle,
      bottomBtn
    } = state;
    // 是否用展位背景图
    if (!imageTitle || !(imageTitle && imageTitle.dataObj && imageTitle.dataObj.imgUrl)) {
      this.setState({
        imgStatus: {
          showDynamicImg: false,
          showStaticImg: true,
        }
      });
    } else {
      this.setState({
        imgStatus: {
          showDynamicImg: true,
          showStaticImg: false,
        }
      });
    }
    // 是否用展位按钮
    if (!bottomBtn || !(bottomBtn && bottomBtn.dataObj && bottomBtn.dataObj.title)) {
      this.setState({
        buttonStatus: {
          showDynamicButton: false,
          showStaticButton: true,
        }
      });
    } else {
      this.setState({
        buttonStatus: {
          showDynamicButton: true,
          showStaticButton: false,
        }
      });
    }
    this.setState({
      imageTitle,
      bottomBtn
    });
  }

  /**
   * 点击按钮跳转申请
   */
  jumpToApply() {
    Madp.navigateTo({
      url: `${urlDomain}/${channel}/apply/#/pages/index/index?mapCode=${getMapCode()}`
    });
  }

  /**
   * 点击图片跳转产品信息
   */
  jumpToProductInfo() {
    const { productInfoUrl } = this.props;
    if (productInfoUrl) {
      Madp.navigateTo({
        url: productInfoUrl
      });
    }
  }

  render() {
    const { type } = this.props;
    const {
      imgStatus, buttonStatus, bottomBtn, imageTitle
    } = this.state;

    // 默认图片
    const defaultImg = defaultImgMap[type];
    return (
      <MUView
        className="common-intro"
        data-template="zl"
      >
        <MUView
          className="entryView"
          beaconId="submitIcon"
          onClick={() => { this.jumpToProductInfo(); }}
        >
          <MUImage
            className="entryView-img"
            src={entryImg}
          />
        </MUView>
        {/* 默认背景图 */}
        <MUView className="common-intro__img-wrap">
          {/* 展位背景图 */}
          {imgStatus.showDynamicImg && <ImageTitle {...imageTitle} />}
          {imgStatus.showStaticImg && (
          <MUImage
            className="img"
            src={defaultImg}
            mode="widthFix"
          />
          )}
        </MUView>
        {/* 吸底按钮 */}
        <MUView className="common-intro__fix-button">
          {/* 展位按钮 */}
          {buttonStatus.showDynamicButton && <BottomBtn {...bottomBtn} />}
          {/* 默认按钮 */}
          {buttonStatus.showStaticButton && (
            <MUButton
              className="button"
              type="primary"
              beaconId="introButton"
              onClick={this.jumpToApply}
            >
              立即申请
            </MUButton>
          )}
        </MUView>

      </MUView>
    );
  }
}

// 区块接收的入参，及类型
CommonIntro.propTypes = {
  type: PropTypes.string.isRequired,
  pageTitle: PropTypes.string,
};

// 区块入参的默认值
CommonIntro.defaultProps = {
  pageTitle: '',
  type: 'bld'
};

export default CommonIntro;
