@import "~@mu/zui/dist/style/variables/default.scss";

$iphoneXBottomCon: constant(safe-area-inset-bottom);
$iphoneXBottomEnv: env(safe-area-inset-bottom);

.common-intro {
  position: relative;
  min-height: 100vh;
  background: $color-white;

  &__img-wrap {
    display: flex;
    flex-direction: column;
    padding-bottom: calc(#{$at-button-height} + #{$iphoneXBottomEnv});

    .img {
      width: 100%;
      height: auto;
    }
  }

  &__fix-button {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;

    .button {
      border-radius: 0;
    }

    &::after {
      content: "";
      display: block;
      width: 100%;
      height: $iphoneXBottomEnv;
      background: #f3f3f3;
    }

    .bottom-btn {
      height: 100px;

      .bottom-btn__txt {
        font-size: 36px;
      }
    }
  }

  .entryView {
    position: absolute;
    right: 30px;
    top: 0;
    z-index: 9999;
    width: 76px;
    height: 80px;
    box-sizing: border-box;
    border-radius: 0 0 100px 100px;

    &-img {
      width: 100%;
      height: 100%;
    }
  }
}
