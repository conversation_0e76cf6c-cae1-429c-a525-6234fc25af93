import {
  getEnv,
  getCurrentPageUrl
} from '@mu/madp-utils';
import Madp from '@mu/madp';
import {
  dispatchTrackEvent
} from '@mu/madp-track';

// 当前环境
const env = process.env.BUILD_ENV || getEnv();

/**
 * 前端域名
 */
let domain = 'https://m-zl.mucfc.com';
if (env !== 'prod') {
  domain = `https://m-zl-${env}.cfcmu.cn`;
}

const getDefaultChannel = () => {
  const channel = {
    WEAPP_DEFAULT_CHANNEL: '1MNP',
    SWAN_DEFAULT_CHANNEL: 'MOCK',
    ALIPAY_DEFAULT_CHANNEL: 'MOCK',
    TT_DEFAULT_CHANNEL: 'MOCK',
    H5_DEFAULT_CHANNEL: 'MOCK'
  };
  let defaultChannel = 'MOCK';

  switch (Madp.getEnv()) {
    case Madp.ENV_TYPE.WEAPP:
      defaultChannel = channel.WEAPP_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.SWAN:
      defaultChannel = channel.SWAN_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.ALIPAY:
      defaultChannel = channel.ALIPAY_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.TT:
      defaultChannel = channel.TT_DEFAULT_CHANNEL;
      break;
    case Madp.ENV_TYPE.WEB:
      defaultChannel = channel.H5_DEFAULT_CHANNEL;
      break;
    default:
      defaultChannel = channel.H5_DEFAULT_CHANNEL;
      break;
  }

  return defaultChannel;
};

// eslint-disable-next-line import/no-mutable-exports
let channel = Madp.getChannel();
if (!channel || channel === 'MOCK') {
  channel = getDefaultChannel();
}

const urlDomain = domain;

/**
 * 手动埋点方法，在基础方法上拓展，封装添加模块Id、PageId
 * @param {string} pageId 页面Id
 * @param {object} beaconObj 埋点数据对象，跟dispatchTrackEvent定义的所需参数一样
 */
const sendTrackBeacon = (pageId, beaconObj) => {
  let moduleId = process.env.TRACK_MODULE_ID; // 默认工程moduleId
  // 兼容小程序moduleId获取（路由上带）
  const currentRoute = getCurrentPageUrl();
  if (
    (process.env.TARO_ENV === 'weapp' || process.env.TARO_ENV === 'alipay') && typeof currentRoute === 'string'
  ) {
    const matchRes = currentRoute.match(/^\/?(.+)\/pages\/.+$/);
    if (matchRes) {
      // eslint-disable-next-line prefer-destructuring
      moduleId = matchRes[1];
    }
  }
  let beaconId = moduleId;
  // 传参只有pageId
  if (pageId && !beaconObj.beaconId) {
    beaconId = `${beaconId}.${pageId}`;
  }
  // 传参已拼pageId
  if (!pageId && beaconObj.beaconId) {
    beaconId = `${beaconId}.${beaconObj.beaconId}`;
  }
  // 传参分开pageId和beaconId
  if (pageId && beaconObj.beaconId) {
    beaconId = `${beaconId}.${pageId}.${beaconObj.beaconId}`;
  }

  dispatchTrackEvent({
    ...beaconObj,
    beaconId
  });
};

/**
 * 获取mapCode
 */
const getMapCode = () => Madp.getStorageSync('applyMapCode', 'SESSION') || Madp.getStorageSync('mapCode', 'SESSION') || '';

export {
  urlDomain,
  channel,
  getMapCode,
  sendTrackBeacon
}
