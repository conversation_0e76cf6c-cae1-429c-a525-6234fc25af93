.creditTest-comp {
  &_qa {
    display: flex;
    flex-direction: column;

    &--banner {
      width: 100%;
    }

    &--main {
      position: relative;
      top: -76px;
      background: #fff;
      border-radius: 32px;
      padding: 46px 41px 0;

      .test-title {
        font-size: 36px;
      }

      .test-radio {
        padding: 0;
        height: fit-content;

        .mu-radio__option-group {
          padding: 0 0 42px;
          flex-wrap: wrap;

          .mu-radio__option {
            margin: 30px 20px 0 0;
          }
        }

        .mu-radio__title {
          min-width: 128px;
          height: 60px !important;
          font-size: 32px !important;
          padding: 0 28px !important;
          color: #808080 !important;
          border: 2px solid #a6a6a6 !important;
          border-radius: 30px !important;

          &--checked {
            color: #fff !important;
            border: none !important;
            padding: 0 30px !important;
          }
        }
      }
    }

    &--btn {
      margin-top: 20px !important;
    }
  }

  &_result {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 36px;

    &--icon {
      width: 220px;
      height: 220px;
    }

    &--title {
      font-size: 32px;
      color: #333;
      margin-top: 44px;
    }

    &--limit {
      font-size: 100px;
      line-height: 100px;
      color: #333;
      margin-top: 40px;
    }

    &--subTitle {
      font-size: 28px;
      color: #808080;
      margin-top: 30px;

      .percent {
        color: #f84;
        font-size: 40px;
        margin: 0 10px;
      }
    }

    &--btnTip {
      width: 226px;
      height: 47px;
      margin: 92px 0 12px;
    }

    &--btn {
      width: calc(100% - 60px);
    }

    &--tip {
      font-size: 26px;
      color: #a6a6a6;
      margin-top: 40px;
    }
  }
}
