import Madp from '@mu/madp';
import {
  M<PERSON><PERSON>iew, MUImage, MUButton, MURadio, MUText
} from '@mu/zui';
import {
  urlDomain,
  channel,
  getMapCode
} from '../utils';
import './index.scss';

const BannerImg = 'https://file.mucfc.com/cop/1/25/202109/2021091409562997febe.png';
const BtnTipImg = 'https://file.mucfc.com/cop/1/25/202109/2021091409562933fd12.png';
const ResultIconImg = 'https://file.mucfc.com/cop/1/25/202109/20210914095629c43f48.png';

const PERCENTAGE_LIST = ['78%', '86%', '91%', '95%'];
const CREDIT_LIST = ['8888', '20888', '27888'];
const TEST_DATA = [{
  title: '你的性别',
  answerList: ['男', '女'],
  creditList: ['8888', '8888']
}, {
  title: '你的年龄',
  answerList: ['90后', '80后', '70后', '其他'],
  creditList: ['0', '2000', '4000', '8000']
}, {
  title: '你已有的信用卡额度',
  answerList: ['没有信用卡', '不到2万', '2万-5万', '超过5万'],
  creditList: ['0', '10000', '15000', '20000']
}];

class CreditTest extends Madp.Component {
  constructor(props) {
    super(props);
    this.state = {
      showPage: false,
      showTestResult: false,
      percentageList: [], // 测一测百分比结果分布
      creditList: [], // 测一测额度区间
      testList: [], // 问题类别
      totalCredit: 0 // 测一测总得分额度
    };
  }

  componentDidMount() {
    Madp.setNavigationBarTitle({ title: '测一测' });
    this.initTestInfo();
  }

  updateCredit(testIndex, radioValue) {
    const {
      testList
    } = this.state;
    let {
      totalCredit
    } = this.state;
    const oldRadioValue = testList[testIndex].credit; // 原本已选的值（关联勾选的）
    testList[testIndex].credit = radioValue; // 更新当前选择值（关联勾选的）

    let fixValue = 0; // 需要修正的偏差
    let trueValue = radioValue; // 实际无偏差的值

    const { answerList } = testList[testIndex];
    answerList.forEach((answer, index) => {
      if (answer.value === radioValue) {
        fixValue = index; // 偏差值
        trueValue += fixValue; // 抵消偏差
      }

      if (answer.value === oldRadioValue) {
        totalCredit -= (oldRadioValue + index); // 先减去抵消偏差后的已选的值
      }
    });
    this.setState({
      totalCredit: totalCredit += trueValue // 记录加新值
    });
  }

  initTestInfo() {
    const testList = [];
    TEST_DATA.forEach((item) => {
      const answerListNew = [];
      item.answerList.forEach((answer, index) => {
        answerListNew.push({
          label: answer,
          value: Number(item.creditList[index]) - index // 设置偏差，避免相同value影响勾选
        });
      });

      testList.push({
        title: item.title, // 题目
        answerList: answerListNew,
        credit: -1 // 当前选择得分额度
      });
    });
    this.setState({
      percentageList: PERCENTAGE_LIST, // 测一测百分比结果分布
      creditList: CREDIT_LIST, // 测一测额度区间
      testList, // 问题类别
      totalCredit: 0 // 测一测总得分额度
    })
  }

  /**
   * 测一测问答页
   */
  testPageView = () => {
    const {
      testList
    } = this.state;

    if (!testList || testList.length < 0) {
      return null;
    }
    const disabledTestBtn = !(testList && testList.every((item) => item.credit > -1));

    return (
      <MUView
        className="creditTest-comp_qa"
        data-template="zl"
      >
        <MUImage
          className="creditTest-comp_qa--banner"
          src={BannerImg}
          mode="widthFix"
        />

        <MUView className="creditTest-comp_qa--main">
          {testList.map((item, index) => (
            <MUView className="creditTest-comp_qa--main_item">
              <MUView className="test-title">{item.title}</MUView>
              <MURadio
                className="test-radio"
                type="badge"
                options={item.answerList}
                value={item.credit}
                beaconId="testRadio"
                onClick={(value) => this.updateCredit(index, value)} // 减去index回复原值
              />
            </MUView>
          ))}

          <MUButton
            className="creditTest-comp_qa--btn"
            type="primary"
            beaconId="testBtn"
            disabled={disabledTestBtn}
            onClick={() => this.setState({ showTestResult: true })}
          >
            测一测额度

          </MUButton>
        </MUView>
      </MUView>
    );
  }

  /**
   * 测一测结果页
   */
  testReusltView = () => {
    const {
      totalCredit,
      creditList,
      percentageList
    } = this.state;

    // 根据测算额度分值，匹配所属额度区间，进而匹配对应的百分比
    let resultPercentage = percentageList.length > 0 ? percentageList[percentageList.length - 1] : '0%';
    // eslint-disable-next-line no-restricted-syntax, no-unused-vars
    for (const index in creditList.slice()) {
      if (totalCredit <= Number(creditList[index])) {
        resultPercentage = percentageList[index] || resultPercentage;
        break;
      }
    }

    return (
      <MUView className="creditTest-comp_result">
        <MUImage
          className="creditTest-comp_result--icon"
          src={ResultIconImg}
        />
        <MUView className="creditTest-comp_result--title">预估你的借款额度为(元)</MUView>
        <MUView className="creditTest-comp_result--limit">{totalCredit.toLocaleString()}</MUView>
        <MUView className="creditTest-comp_result--subTitle">
          你的额度已超过全国
          <MUText className="percent">{resultPercentage}</MUText>
          的小伙伴
        </MUView>

        <MUImage
          className="creditTest-comp_result--btnTip"
          src={BtnTipImg}
        />
        <MUButton
          className="creditTest-comp_result--btn"
          type="primary"
          beaconId="goApplyBtn"
          onClick={this.goApply}
        >
          立即申请

        </MUButton>

        <MUView className="creditTest-comp_result--tip">本额度测评结果仅提供参考，详细以实际申请为准</MUView>
      </MUView>
    );
  }

  /**
   * 结果页点击去申请
   * mapcode在链接上带
   */
  goApply() {
    Madp.navigateTo({
      url: `${urlDomain}/${channel}/apply/#/index?mapCode=${getMapCode()}`
    });
  }

  render() {
    const { showTestResult } = this.state;
    return (
      <MUView className="creditTest-comp">
        {showTestResult ? this.testReusltView() : this.testPageView()}
      </MUView>
    );
  }
}

// 区块接收的入参，及类型
CreditTest.propTypes = {
};

// 区块入参的默认值
CreditTest.defaultProps = {
};

export default CreditTest;
