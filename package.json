{"name": "@mu/abf-material-fe", "version": "0.1.0", "description": "申请业务线物料包", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "generate": "madp generate", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./  --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\" --fix"}, "files": ["build"], "devDependencies": {"@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-react-jsx": "7.9.4", "@babel/preset-env": "7.9.0", "@commitlint/cli": "8.3.5", "@commitlint/config-conventional": "7.6.0", "@mu/basic-library": "1.3.28-beta.2", "@mu/das-beacon": "1.1.7", "@mu/madp": "1.4.0", "@mu/madp-cli": "1.9.5-alpha.4", "@mu/zui": "1.22.2-beta.3", "@types/node": "12.12.31", "@types/react": "16.9.25", "@types/webpack-env": "1.15.1", "@typescript-eslint/eslint-plugin": "4.0.0", "@typescript-eslint/parser": "2.25.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "8.2.6", "babel-jest": "23.6.0", "babel-loader": "8.1.0", "babel-plugin-syntax-dynamic-import": "6.18.0", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-decorators-legacy": "1.3.5", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-preset-env": "1.7.0", "build-plugin-component": "1.0.0", "build-scripts": "1.1.1", "classnames": "2.2.6", "commitlint": "8.3.5", "conventional-changelog-cli": "2.0.31", "cross-env": "5.2.1", "cz-conventional-changelog": "2.1.0", "enzyme": "3.10.0", "enzyme-adapter-react-16": "1.14.0", "eslint": "6.8.0", "eslint-config-airbnb": "18.1.0", "eslint-config-o2team": "0.1.6", "eslint-config-taro": "3.4.4", "eslint-plugin-import": "2.22.1", "eslint-plugin-jest": "21.27.2", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.19.0", "eslint-plugin-react-hooks": "2.5.1", "eslint-plugin-taro": "3.3.20", "eslint-plugin-typescript": "0.14.0", "fs-extra": "8.1.0", "husky": "1.3.1", "jest": "23.6.0", "lint-staged": "7.3.0", "mini-css-extract-plugin": "0.9.0", "nerv-server": "1.5.7", "nerv-test-utils": "1.5.7", "nervjs": "1.5.7", "prop-types": "15.7.2", "standard-version": "5.0.2", "stylelint": "^14.9.1", "stylelint-config-standard": "^26.0.0", "stylelint-scss": "3.16.0", "typescript": "3.8.3", "vconsole": "3.3.4", "vconsole-webpack-plugin": "1.5.1", "webpack": "4.42.1", "webpack-bundle-analyzer": "3.6.1", "webpack-merge": "4.2.2"}, "keywords": ["material", "base"], "author": {"name": "mucfc", "email": ""}, "license": "MIT", "repository": {"type": "git", "url": ""}, "publishConfig": {"registry": "http://npm.mucfc.com"}, "materialConfig": {"template": "direct:ssh://**********************:22222/codingcorp/mdp/mdp-muwa-template-comp.git", "type": "taro"}}