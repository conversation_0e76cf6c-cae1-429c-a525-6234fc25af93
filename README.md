# 物料开发

### 安装依赖

安装物料项目的依赖，lint插件均在物料项目工程下

```bash
$ npm install
```

### 新增一个物料

```bash
# 可不指定物料类型，在命令行进行选择物料类型
$ madp add
# select page|block|component|scaffold

# 也可直接指定物料类型创建物料
$ madp add  page
$ madp add  block
$ madp add  component
$ madp add  scaffold
```

#### 物料类型说明

* `page` - 新增一个页面模板，开发完成发布物料后，使用物料会将代码片段`src/code`下的文件复制到自己的工程中的指定文件夹下，作为页面文件，可通过路由访问
* `block` - 新增一个代码片段，开发完成发布物料后，使用物料会将代码片段`src/code`下的文件复制到自己的工程中，并在代码中以自定义组件的方式引入
* `component` - 新增一个业务组件，开发完成发布物料后，使用物料会在代码中以npm的方式引入该组件
* `scaffold` - 新增一个项目工程模板，开发完成发布物料后，可使用此模板新建工程

#### 物料分类

使用```madp add``` 命令创建物料时，需要根据物料的展示内容，选择物料的分类，`block` 和 `component` 的物料分类可选值如下

1. `block/page` 的分类（`category`）可选值：

* `Card` - 卡片
* `Modal` - 弹窗
* `DataDisplay` - 详情数据展示
* `Information` - 信息内容
* `Landing` - 结果内容
* `Exception` - 异常内容
* `List` - 列表数据内容
* `Form` - 表单数据内容
* `Filter` - 过滤器
* `Video` - 视频内容
* `Table` - 表格
* `Chart` - 图表
* `Others` - 上述类型中无合适类型

2. `component` 的分类（`category`）可选值：

* `Card` - 卡片
* `Modal` - 弹窗
* `Form` - 表单
* `List` - 列表
* `Filter` - 过滤器
* `DataDisplay` - 数据展示
* `Information` - 信息介绍
* `Table` - 表格
* `Chart` - 图表
* `Others` - 上述类型中无合适类型

### 开发调试物料

```bash
# block 区块
$ cd blocks/ExampleBlock
$ npm install
$ npm run dev:h5

# component 业务组件
$ cd components/ExampleComponent
$ npm install
$ npm run dev:h5

# component 项目模板
$ cd scaffolds/ExampleScaffold
```

** package.json 的 dependencies 字段 说明  **

1. `block/page` 类型的物料会下载源码，代码无需打包构建，`src/code` 中使用到的依赖 放到 `dependencies` 中，业务使用物料时会和业务工程中的package.json作对比，并自动安装依赖；
2. `component` 类型的物料需要打包构建发布，根据实际开发情况选择 依赖放在 `dependencies` 还是 `devDependencies`，为包的体积考虑请将基础依赖放置在 `devDependencies`
   中，这类依赖是业务工程一定会引入的依赖， 例如：zui；
3. `scaffold` 类型的物料仅做模板发布使用，只需要一个 `standard-version` 依赖，且该依赖配置对后续流程无任何影响，无需特别关注。

#### block/page - 区块/页面模板 代码开发说明

1. 源码放置在 `src/code` 文件夹下，不允许修改文件名，后续物料使用，会将该文件夹下的所有代码直接复制到业务工程中
2. 其他代码均为调试 `src/code` 使用，不会作为物料内容

#### component - 业务组件开发说明

1. 组件代码放在 `src/component` 文件夹下
2. 需要在 `src/index` 文件中导出组件
3. 使用 `npm run build:component` 打包组件

### 发布物料

```bash
# 进入物料目录
$ cd blocks/ExampleBlock
# 使用指定脚本发布物料，自动生成版本号和git tag
# 发布物料正式版本
$ npm run publish:npm

# 发布物料alpha版本
$ npm run publish:npm-alpha

# 发布物料beta版本使
$ npm run publish:npm-beta
```

**注意: 发布物料需要使用上面给定的脚本生成版本号和git tag,因为standard-version自动生成的tag只有版本号内容，而物料项目中存在多个物料，tag可能会重复 所以自定义生成tag。**

### 生成物料数据

#### 重要：物料数据生成前，需要确认每个物料均已发布

```bash
$ madp generate
```

该命令会根据项目目录 blocks/components/scaffolds 中的物料生成整个物料项目的全部物料信息 materials.json 文件。 示例:

```json
{
  "template": "material-test",
  "type": "react",
  "name": "material-test",
  "description": "物料源测试",
  "author": "",
  "blocks": [
    {
      "languageType": "js",
      "homepage": "http://npm.mucfc.com/-/web/detail/@mu/mu-block-test",
      "description": "intro block",
      "repository": "http://gitfe.mucfc.com/mu_materials/@mu/mu-block-test",
      "name": "MuBlockTest",
      "title": "demo block",
      "category": "Basic",
      "source": {
        "type": "npm",
        "npm": "@mu/mu-block-test",
        "version": "0.1.0",
        "registry": "http://npm.mucfc.com"
      },
      "dependencies": {
        "prop-types": "15.5.8"
      },
      "categories": [
        "Basic"
      ],
      "screenshot": "http://npm.mucfc.com/-/web/detail/@mu/mu-block-test",
      "screenshots": [
        "http://npm.mucfc.com/-/web/detail/@mu/mu-block-test"
      ],
      "publishTime": "2022-04-13T02:44:35.309Z",
      "updateTime": "2022-04-13T02:44:35.309Z",
      "componentType": "@mu/mdp-material-base-fe"
    }
  ],
  "components": [
    {
      "languageType": "js",
      "homepage": "http://npm.mucfc.com/-/web/detail/@mu/mu-material-com-test",
      "description": "intro component",
      "name": "MuMaterialComTest",
      "title": "demo component",
      "category": "Basic",
      "source": {
        "type": "npm",
        "npm": "@mu/mu-material-com-test",
        "version": "1.1.0",
        "registry": "http://npm.mucfc.com"
      },
      "dependencies": {
        "prop-types": "15.5.8"
      },
      "categories": [
        "Basic"
      ],
      "screenshot": "http://npm.mucfc.com/-/web/detail/@mu/mu-material-com-test",
      "screenshots": [
        "http://npm.mucfc.com/-/web/detail/@mu/mu-material-com-test"
      ],
      "publishTime": "2022-04-13T02:44:48.863Z",
      "updateTime": "2022-04-13T05:59:38.452Z",
      "componentType": "@mu/mdp-material-base-fe"
    }
  ],
  "scaffolds": [
    {
      "languageType": "ts",
      "homepage": "http://npm.mucfc.com/-/web/detail/@mu/tpl-scaffold-test",
      "description": "intro scaffold",
      "name": "TplScaffoldTest",
      "title": "test scaffold",
      "category": "Basic",
      "categories": [
        "Basic"
      ],
      "screenshot": "http://npm.mucfc.com/-/web/detail/@mu/tpl-scaffold-test",
      "screenshots": [
        "http://npm.mucfc.com/-/web/detail/@mu/tpl-scaffold-test"
      ],
      "source": {
        "type": "npm",
        "npm": "@mu/tpl-scaffold-test",
        "version": "0.4.0",
        "registry": "http://npm.mucfc.com",
        "author": "mucfc"
      },
      "dependencies": {},
      "publishTime": "2022-04-13T02:09:35.065Z",
      "updateTime": "2022-04-13T07:09:14.968Z",
      "componentType": "@mu/mdp-material-base-fe"
    }
  ]
}
```

#### 物料信息MaterialObject字段说明

| 属性            | 类型          | 必须 | 默认值 | 描述                                                                                                                                                                                                                                                                          |
| ----------------- | --------------- | ------ | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| languageType    | String(js/ts) | 是   |        | 物料使用的语言类型                                                                                                                                                                                                                                                            |
| homepage        | String        | 是   |        | 物料的说明页，如果有的话可以手动修改物料的package.json文件的homepage属性，否则默认使用该物料的npm的说明页                                                                                                                                                                     |
| description     | String        | 是   |        | 物料的描述信息                                                                                                                                                                                                                                                                |
| name            | String        | 是   |        | 物料名称,component类型的物料会以此名称作为 import 导入的名称，即import name from 'npmName'                                                                                                                                                                                    |
| title           | String        | 是   |        | 物料Title                                                                                                                                                                                                                                                                     |
| category        | String        | 否   |        | 物料的用途分类                                                                                                                                                                                                                                                                |
| screenshot      | String        | 否   |        | 物料快照图,如果物料发布到npm中的目录下有命名为screenshot.(png/jpg)的图片文件，那么会生成unpkg的图片链接，在插件可视化页面中浏览时可以方便的告知开发人员物料的大致样式                                                                                                         |
| source          | SourceObject  | 是   |        | 物料的源信息，一般是npm相关信息，详情见SourceObject                                                                                                                                                                                                                           |
| importStatement | String        | 否   |        | 业务组件专用字段,如果业务组件使用时引用语句不是import name from 'npmName'这种,而是import {name1, name2} from 'npmName',那么就需要在物料目录下的package.json中的componentConfig中配置importStatement，如果配置了该属性，在使用组件时，编辑器生成的import语句即为我们配置的语句 |
| componentType   | String        | 是   |        | 物料所属物料包分类，主要用来在编辑器中筛选使用，直接使用物料项目根目录package.json中的name生成。与物料后台管理登记的物料包标识对应                                                                                                                                            |
| dependencies    | Object        | 否   |        | 物料依赖项                                                                                                                                                                                                                                                                    |
| publishTime     | String        | 否   |        | 物料发布时间                                                                                                                                                                                                                                                                  |
| updateTime      | String        | 否   |        | 物料更新时间                                                                                                                                                                                                                                                                  |

#### SourceObject

| 属性     | 类型        | 必须 | 默认值 | 描述                                 |
| ---------- | ------------- | ------ | -------- | -------------------------------------- |
| type     | String(npm) | 是   |        | 源类型,这里一般是npm                 |
| npm      | String      | 是   |        | 物料npm包名                          |
| version  | String      | 是   |        | 物料npm包版本，可以使用latest这种    |
| registry | String      | 是   |        | 物料源镜像地址(http://npm.mucfc.com) |
| author   | String      | 否   |        | 物料开发者                           |

### 如何将已存在的npm组件纳入到物料包中？

在物料项目中通过madp add 命令创建的物料由于代码在物料项目中，所以执行madp
generate命令会自动生成信息到最终的materials.json文件中，如果我们有在其他仓库中已经发布到npm上的一些组件也需要纳入到物料中，不必将代码复制到物料项目的工程中进行维护，这类组件的维护方式还是跟之前一样，只不过需要在物料项目的src/extra.json中维护它的物料信息对象(
MaterialObject)即可。

#### extra.json:

```json
{
  "blocks": [],
  "components": [],
  "scaffolds": []
}
```

blocks对应需要添加的额外区块物料，components对应需要添加的额外业务组件物料，scaffolds对应需要添加的额外项目模板物料。一般需要添加的可能是业务组件物料。
以@mu/credit-card额度卡片业务组件为例，将已发布的额度卡片纳入到物料包中，需要在extra.json的components字段中添加额度卡片的物料信息。

```json
{
  "blocks": [],
  "components": [
    {
      "name": "CreditCard",
      "category": "Components",
      "title": "额度卡片",
      "description": "额度卡片",
      "homepage": "http://npm.mucfc.com/-/web/detail/@mu/credit-card",
      "repository": "额度卡片的gitlab地址",
      "source": {
        "type": "npm",
        "npm": "@mu/credit-card",
        "version": "latest",
        "registry": "http://npm.mucfc.com"
      },
      "importStatement": "import { CreditCard,CARD_ACTION } from '@mu/credit-card';",
      "componentType": "物料项目的name，对应物料后台管理系统登记的物料包标识"
    }
  ],
  "scaffolds": []
}
```

**注意**: extra.json中的内容需手动维护，不像之前madp generate生成的物料信息，部分内容如果没有会自动拼接处理，extra.json中的所有内容需要手动完善。 那么在执行madp
generate时，除了会扫描物料项目中所有物料生成的信息，也会合并extra.json中的物料信息，最终输出的json是两者的并集
