{"template": "direct:ssh://*******************:8082/mdp/mdp-fma-framework/madp-template.git", "type": "taro", "name": "@mu/abf-material-fe", "description": "申请业务线物料包", "author": {"name": "mucfc", "email": ""}, "blocks": [{"languageType": "js", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/common-intro", "description": "公共介绍", "repository": "", "name": "CommonIntro", "title": "公共介绍", "category": "DataDisplay", "screenshot": "http://unpkg.mucfc.com/@mu/common-intro/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/common-intro", "version": "0.1.1-beta.1", "registry": "http://npm.mucfc.com"}, "props": {}, "categories": ["DataDisplay"], "screenshots": ["http://unpkg.mucfc.com/@mu/common-intro/screenshot.png"], "dependencies": {}, "publishTime": "2022-08-26T01:22:44.174Z", "updateTime": "2023-04-03T08:12:50.057Z"}, {"languageType": "js", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/credit-test", "description": "额度测试", "repository": "", "name": "CreditTest", "title": "额度测试", "category": "Information", "screenshot": "http://unpkg.mucfc.com/@mu/credit-test/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/credit-test", "version": "0.1.1-beta.1", "registry": "http://npm.mucfc.com"}, "props": {}, "categories": ["Information"], "screenshots": ["http://unpkg.mucfc.com/@mu/credit-test/screenshot.png"], "dependencies": {}, "publishTime": "2022-08-26T01:23:14.380Z", "updateTime": "2023-04-03T08:14:20.085Z"}], "components": [{"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/action-sheet-picker", "description": "intro component", "repository": "", "name": "ActionSheetPicker", "title": "面板选择", "category": "Modal", "screenshot": "http://unpkg.mucfc.com/@mu/action-sheet-picker/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/action-sheet-picker", "version": "1.0.1-beta.2", "registry": "http://npm.mucfc.com"}, "props": {"range": "{[]}", "handleItemClick": "{() => {}}"}, "categories": ["Modal"], "screenshots": ["http://unpkg.mucfc.com/@mu/action-sheet-picker/screenshot.png"], "dependencies": {}, "publishTime": "2022-07-27T09:07:12.362Z", "updateTime": "2022-08-25T13:07:22.776Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/certified-bill", "description": "账单认证", "repository": "", "name": "CertifiedBill", "title": "账单认证", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/certified-bill/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/certified-bill", "version": "1.0.1-beta.1", "registry": "http://npm.mucfc.com"}, "props": {}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/certified-bill/screenshot.png"], "dependencies": {}, "publishTime": "2023-03-09T03:50:32.574Z", "updateTime": "2023-03-30T13:36:20.444Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/click-selector", "description": "点击全责某项", "repository": "", "name": "ClickSelector", "title": "点击选择", "category": "DataDisplay", "screenshot": "http://unpkg.mucfc.com/@mu/click-selector/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/click-selector", "version": "1.2.1-beta.2", "registry": "http://npm.mucfc.com"}, "props": {"range": "{[]}", "onClickItem": "{() => {}}"}, "categories": ["DataDisplay"], "screenshots": ["http://unpkg.mucfc.com/@mu/click-selector/screenshot.png"], "dependencies": {}, "publishTime": "2022-07-27T08:59:37.912Z", "updateTime": "2023-01-17T08:42:23.414Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/hospital-group", "description": "根据地区选择医院", "repository": "", "name": "HospitalGroup", "title": "医院选择组件", "category": "Information", "screenshot": "http://unpkg.mucfc.com/@mu/hospital-group/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/hospital-group", "version": "1.0.1-beta.2", "registry": "http://npm.mucfc.com"}, "props": {"handleChooseArea": "{() => {}}", "handleHospitalNameChange": "{() => {}}", "handleDepartNameChange": "{() => {}}"}, "categories": ["Information"], "screenshots": ["http://unpkg.mucfc.com/@mu/hospital-group/screenshot.png"], "dependencies": {"@mu/vague-search": "1.0.1-beta.2", "@mu/basic-library": "1.7.2-beta.3"}, "publishTime": "2022-07-27T09:00:31.471Z", "updateTime": "2022-11-03T08:00:54.109Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/photo-upload", "description": "图片上传", "repository": "", "name": "PhotoUpload", "title": "图片上传", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/photo-upload/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/photo-upload", "version": "1.0.1-beta.8", "registry": "http://npm.mucfc.com"}, "props": {}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/photo-upload/screenshot.png"], "dependencies": {"@mu/basic-library": "1.7.2-beta.3"}, "publishTime": "2022-07-19T02:01:56.477Z", "updateTime": "2022-11-03T08:05:14.880Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/record-screen", "description": "录屏组件", "repository": "", "name": "RecordScreen", "title": "录屏组件", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/record-screen/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/record-screen", "version": "1.0.1-beta.6", "registry": "http://npm.mucfc.com"}, "props": {}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/record-screen/screenshot.png"], "dependencies": {}, "publishTime": "2023-03-06T11:57:20.142Z", "updateTime": "2023-03-30T13:35:30.911Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/result-loading", "description": "loading结果页倒计时", "repository": "", "name": "ResultLoading", "title": "result-loading", "category": "Card", "screenshot": "http://unpkg.mucfc.com/@mu/result-loading/screenshot.png", "source": {"type": "npm", "npm": "@mu/result-loading", "version": "1.1.1-beta.2", "registry": "http://npm.mucfc.com"}, "props": {"initCountingNum": "{10}", "interval": "{2}", "queryResult": "{() => {}}"}, "categories": ["Card"], "screenshots": ["http://unpkg.mucfc.com/@mu/result-loading/screenshot.png"], "dependencies": {}, "publishTime": "2022-08-02T09:36:04.328Z", "updateTime": "2023-01-17T08:43:51.066Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/school-group", "description": "根据地区选择学校", "repository": "", "name": "SchoolGroup", "title": "学校选择", "category": "Information", "screenshot": "http://unpkg.mucfc.com/@mu/school-group/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/school-group", "version": "1.0.1-beta.2", "registry": "http://npm.mucfc.com"}, "props": {"handleSchoolNameChange": "{() => {}}", "handleChooseArea": "{() => {}}"}, "categories": ["Information"], "screenshots": ["http://unpkg.mucfc.com/@mu/school-group/screenshot.png"], "dependencies": {"@mu/vague-search": "1.0.1-beta.2", "@mu/basic-library": "1.7.2-beta.3"}, "publishTime": "2022-07-27T09:01:01.652Z", "updateTime": "2022-11-03T08:03:14.920Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/text-progress-bar", "description": "横向文字进度条", "repository": "", "name": "TextProgressBar", "title": "文字进度条", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/text-progress-bar/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/text-progress-bar", "version": "1.1.0-beta.5", "registry": "http://npm.mucfc.com"}, "props": {"steps": "{[]}", "doneStep": "'0"}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/text-progress-bar/screenshot.png"], "dependencies": {}, "publishTime": "2022-07-27T09:01:27.366Z", "updateTime": "2022-11-10T07:51:44.430Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/vague-search", "description": "模糊搜索", "repository": "", "name": "VagueSearch", "title": "模糊搜索", "category": "Form", "screenshot": "http://unpkg.mucfc.com/@mu/vague-search/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/vague-search", "version": "1.0.1-beta.3", "registry": "http://npm.mucfc.com"}, "props": {"optionList": "{[]}", "handleChange": "{() => {}}", "itemClick": "{() => {}}"}, "categories": ["Form"], "screenshots": ["http://unpkg.mucfc.com/@mu/vague-search/screenshot.png"], "dependencies": {}, "publishTime": "2022-07-20T11:28:14.659Z", "updateTime": "2022-08-25T13:09:55.444Z"}, {"languageType": "ts", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/video-verify", "description": "视频组件", "repository": "", "name": "VideoVerify", "title": "视频组件", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/video-verify/screenshot.png", "autoClose": true, "source": {"type": "npm", "npm": "@mu/video-verify", "version": "1.0.1-beta.16", "registry": "http://npm.mucfc.com"}, "props": {}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/video-verify/screenshot.png"], "dependencies": {}, "publishTime": "2023-02-09T16:09:22.466Z", "updateTime": "2023-03-01T10:00:55.748Z"}], "scaffolds": [], "pages": [{"languageType": "js", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/apply-base-page", "description": "申请业务基础页面模板,使用申请页面的统一开发模式,集成了统一的hoc", "repository": "", "name": "ApplyBasePage", "title": "申请业务基础页面模板", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/apply-base-page/screenshot.png", "source": {"type": "npm", "npm": "@mu/apply-base-page", "version": "1.0.1", "registry": "http://npm.mucfc.com"}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/apply-base-page/screenshot.png"], "dependencies": {"@mu/basic-library": "1.3.28-beta.2"}, "publishTime": "2022-08-09T06:17:33.572Z", "updateTime": "2023-04-03T08:17:04.628Z"}, {"languageType": "js", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/apply-form-submit-page", "description": "申请表单提交页面模板，接入协议相关逻辑", "repository": "", "name": "ApplyFormSubmitPage", "title": "申请表单提交页面模板", "category": "Form", "screenshot": "http://unpkg.mucfc.com/@mu/apply-form-submit-page/screenshot.png", "source": {"type": "npm", "npm": "@mu/apply-form-submit-page", "version": "1.0.1", "registry": "http://npm.mucfc.com"}, "categories": ["Form"], "screenshots": ["http://unpkg.mucfc.com/@mu/apply-form-submit-page/screenshot.png"], "dependencies": {"@mu/agreement": "1.5.22-nostyle.0", "@mu/basic-library": "1.3.28-beta.2"}, "publishTime": "2022-08-09T06:21:36.500Z", "updateTime": "2023-04-03T08:19:16.603Z"}, {"languageType": "js", "componentType": "@mu/abf-material-fe", "homepage": "http://npm.mucfc.com/-/web/detail/@mu/apply-simple-result-page", "description": "申请业务通用结果页面模板", "repository": "", "name": "ApplySimpleResultPage", "title": "申请结果页模板", "category": "Others", "screenshot": "http://unpkg.mucfc.com/@mu/apply-simple-result-page/screenshot.png", "source": {"type": "npm", "npm": "@mu/apply-simple-result-page", "version": "1.0.1", "registry": "http://npm.mucfc.com"}, "categories": ["Others"], "screenshots": ["http://unpkg.mucfc.com/@mu/apply-simple-result-page/screenshot.png"], "dependencies": {"@mu/basic-library": "1.3.28-beta.2"}, "publishTime": "2022-08-09T06:25:10.801Z", "updateTime": "2023-04-03T08:20:14.489Z"}]}