{"extends": ["stylelint-config-standard"], "rules": {"at-rule-no-unknown": [true, {"ignoreAtRules": ["mixin", "extend", "content", "include"]}], "selector-type-no-unknown": [true, {"ignoreTypes": ["page", "scroll-view"]}], "unit-no-unknown": [true, {"ignoreUnits": ["rpx"]}], "indentation": 2, "no-descending-specificity": null, "selector-class-pattern": null, "no-duplicate-selectors": true, "no-invalid-double-slash-comments": null, "no-empty-source": true, "declaration-block-no-redundant-longhand-properties": true, "font-family-no-missing-generic-family-keyword": true, "function-url-quotes": null, "declaration-block-no-duplicate-properties": true, "number-leading-zero": "always", "number-no-trailing-zeros": true, "media-feature-name-case": "lower", "selector-pseudo-class-case": "lower", "selector-pseudo-element-case": "lower", "selector-type-case": "lower", "at-rule-name-case": "lower", "function-name-case": "lower", "value-keyword-case": "lower", "property-case": "lower", "block-opening-brace-space-before": "always", "media-feature-colon-space-before": "never", "media-feature-colon-space-after": "always", "media-feature-range-operator-space-after": "always", "media-feature-range-operator-space-before": "always", "comment-whitespace-inside": "always", "function-parentheses-space-inside": "never", "media-feature-parentheses-space-inside": "never", "selector-combinator-space-before": "always", "selector-combinator-space-after": "always", "declaration-block-trailing-semicolon": "always", "function-comma-space-after": "always", "function-comma-space-before": "never", "function-whitespace-after": "always", "value-list-comma-space-before": "never", "value-list-comma-space-after": "always", "declaration-colon-space-before": "never", "declaration-colon-space-after": "always", "shorthand-property-no-redundant-values": true, "length-zero-no-unit": true, "selector-max-empty-lines": 0, "no-extra-semicolons": true, "color-function-notation": "legacy"}, "overrides": [{"files": "**/*.scss", "customSyntax": "postcss"}]}